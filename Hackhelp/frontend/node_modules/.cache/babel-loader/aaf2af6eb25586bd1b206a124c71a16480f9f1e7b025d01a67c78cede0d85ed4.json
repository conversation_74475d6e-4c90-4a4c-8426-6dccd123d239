{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Hackhelp/frontend/src/Pages/Home.js\";\nimport React from 'react';\nimport WorkspaceApp from '../Page_Components/Home/WorkSpace';\nimport Sample2 from '../Page_Components/Home/Sample2';\nimport RedesignedWorkspace from '../Page_Components/Home/FinalWorkSpace';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Home() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen font-sans\",\n    children: /*#__PURE__*/_jsxDEV(Sample2, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 9\n  }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "WorkspaceApp", "Sample2", "RedesignedWorkspace", "jsxDEV", "_jsxDEV", "Home", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Hackhelp/frontend/src/Pages/Home.js"], "sourcesContent": ["import React from 'react'\nimport WorkspaceApp from '../Page_Components/Home/WorkSpace';\nimport Sample2 from '../Page_Components/Home/Sample2';\nimport RedesignedWorkspace from '../Page_Components/Home/FinalWorkSpace';\n\nexport default function Home() {\n\n    return (\n        <div className=\"min-h-screen font-sans\">\n        {/* <WorkspaceApp/> */}\n        <Sample2/>\n        {/* <RedesignedWorkspace/> */}\n        </div>\n    )\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,OAAO,MAAM,iCAAiC;AACrD,OAAOC,mBAAmB,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,eAAe,SAASC,IAAIA,CAAA,EAAG;EAE3B,oBACID,OAAA;IAAKE,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eAEvCH,OAAA,CAACH,OAAO;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEL,CAAC;AAEd;AAACC,EAAA,GATuBP,IAAI;AAAA,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}