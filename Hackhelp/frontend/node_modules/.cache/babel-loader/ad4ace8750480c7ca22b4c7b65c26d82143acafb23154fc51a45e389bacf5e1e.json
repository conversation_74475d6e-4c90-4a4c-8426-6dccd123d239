{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Hackhelp/frontend/src/Pages/PowerPointer.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PowerPointerGenerator({\n  isDarkMode = false\n}) {\n  _s();\n  const [activeTab, setActiveTab] = useState('basic');\n  const [formData, setFormData] = useState({\n    project_name: \"\",\n    problem_statement: \"\",\n    solution: \"\",\n    target_audience: \"\",\n    key_features: \"\",\n    tech_stack: \"\",\n    business_impact: \"\",\n    design_template: \"1\"\n  });\n  const [isOutputVisible, setIsOutputVisible] = useState(false);\n  const [isErrorVisible, setIsErrorVisible] = useState(false);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [downloadUrl, setDownloadUrl] = useState('');\n  const tabs = [{\n    id: 'basic',\n    label: 'Basic Info',\n    icon: '👤'\n  }, {\n    id: 'project',\n    label: 'Project',\n    icon: '💡'\n  }, {\n    id: 'technical',\n    label: 'Technical',\n    icon: '⚙️'\n  }, {\n    id: 'design',\n    label: 'Design',\n    icon: '🎨'\n  }];\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const generatePresentation = () => {\n    setIsOutputVisible(false);\n    setIsErrorVisible(false);\n    setIsLoading(true);\n    const payload = {\n      ...formData,\n      key_features: formData.key_features.split(',').map(f => f.trim()),\n      tech_stack: formData.tech_stack.split(',').map(t => t.trim())\n    };\n    fetch(\"http://localhost:8000/api/startup/generate-powerpoint/\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify(payload)\n    }).then(response => {\n      if (!response.ok) throw new Error(`HTTP ${response.status}`);\n      return response.json();\n    }).then(data => {\n      if (data.url) {\n        const fullUrl = `http://localhost:8000/${data.url}`;\n        setDownloadUrl(fullUrl);\n        setIsOutputVisible(true);\n      } else {\n        throw new Error(\"No URL received in response\");\n      }\n    }).catch(error => {\n      setErrorMessage(\"❌ Error: \" + error.message);\n      setIsErrorVisible(true);\n    }).finally(() => {\n      setIsLoading(false);\n    });\n  };\n  const getFieldsForTab = tabId => {\n    switch (tabId) {\n      case 'basic':\n        return [{\n          key: 'project_name',\n          label: 'Project Name',\n          type: 'input',\n          placeholder: 'EcoTracker Pro'\n        }, {\n          key: 'target_audience',\n          label: 'Target Audience',\n          type: 'input',\n          placeholder: 'Environmental enthusiasts, researchers, and policy makers'\n        }];\n      case 'project':\n        return [{\n          key: 'problem_statement',\n          label: 'Problem Statement',\n          type: 'textarea',\n          placeholder: 'Lack of accessible tools for individuals to track and reduce their carbon footprint in daily activities'\n        }, {\n          key: 'solution',\n          label: 'Solution',\n          type: 'textarea',\n          placeholder: 'AI-powered mobile app that automatically tracks carbon emissions and provides personalized reduction strategies'\n        }, {\n          key: 'key_features',\n          label: 'Key Features',\n          type: 'textarea',\n          placeholder: 'Real-time emission tracking, AI recommendations, community challenges, progress analytics, carbon offset marketplace'\n        }, {\n          key: 'business_impact',\n          label: 'Business Impact',\n          type: 'textarea',\n          placeholder: 'Projected 30% reduction in user carbon footprint within 6 months, potential to impact 1M+ users globally'\n        }];\n      case 'technical':\n        return [{\n          key: 'tech_stack',\n          label: 'Tech Stack',\n          type: 'textarea',\n          placeholder: 'React Native, Node.js, TensorFlow, MongoDB, AWS, Google Maps API, Stripe'\n        }];\n      case 'design':\n        return [{\n          key: 'design_template',\n          label: 'Design Template',\n          type: 'select',\n          options: [{\n            value: \"1\",\n            label: \"Professional (Default)\"\n          }, {\n            value: \"2\",\n            label: \"Creative\"\n          }, {\n            value: \"3\",\n            label: \"Minimal\"\n          }, {\n            value: \"4\",\n            label: \"Academic\"\n          }, {\n            value: \"5\",\n            label: \"Business\"\n          }, {\n            value: \"6\",\n            label: \"Technical\"\n          }, {\n            value: \"7\",\n            label: \"Modern\"\n          }, {\n            value: \"8\",\n            label: \"Corporate\"\n          }, {\n            value: \"9\",\n            label: \"Elegant\"\n          }]\n        }];\n      default:\n        return [];\n    }\n  };\n  const isFormValid = () => {\n    return formData.project_name && formData.problem_statement && formData.solution;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen p-4 transition-colors duration-200 ${isDarkMode ? 'bg-[#111827]' : 'bg-gray-50'}`,\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .theme-primary { color: #621639; }\n        .theme-primary-light { color: #831843; }\n        .theme-accent { background-color: #fce7f3; }\n        .theme-accent-text { color: #831843; }\n        .theme-border { border-color: #621639; }\n        .theme-focus:focus { \n          --tw-ring-color: #621639; \n          border-color: #621639; \n        }\n        .theme-button-light {\n          background-color: #fce7f3;\n          color: #831843;\n        }\n        .theme-button-light:hover {\n          background-color: #621639;\n          color: white;\n        }\n        .theme-button-dark {\n          background-color: #4c1d3b;\n          color: #fce7f3;\n        }\n        .theme-button-dark:hover {\n          background-color: #621639;\n        }\n        .theme-tab-active-light {\n          background-color: white;\n          color: #831843;\n          border-bottom: 2px solid #621639;\n        }\n        .theme-tab-active-dark {\n          background-color: #475569;\n          color: white;\n          border-bottom: 2px solid #621639;\n        }\n        .theme-tab-inactive-light {\n          color: #831843;\n        }\n        .theme-tab-inactive-light:hover {\n          background-color: #f9fafb;\n        }\n        .theme-tab-inactive-dark {\n          color: #d1d5db;\n        }\n        .theme-tab-inactive-dark:hover {\n          background-color: #4b5563;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-5xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: `text-xl font-bold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n          children: \"\\u2728 PowerPoint Generator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`,\n          children: \"Create professional hackathon presentations in minutes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `rounded-lg shadow-sm border overflow-hidden ${isDarkMode ? 'bg-[#1f2937] border-slate-600' : 'bg-white border-gray-200'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex border-b ${isDarkMode ? 'bg-slate-600 border-slate-500' : 'theme-accent border-gray-200'}`,\n              children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab(tab.id),\n                className: `flex-1 px-3 py-2 text-xs font-medium transition-all duration-200 focus:outline-none ${activeTab === tab.id ? isDarkMode ? 'theme-tab-active-dark' : 'theme-tab-active-light' : isDarkMode ? 'theme-tab-inactive-dark' : 'theme-tab-inactive-light'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1\",\n                  children: tab.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this), tab.label]\n              }, tab.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: getFieldsForTab(activeTab).map(field => {\n                  var _field$options;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: `block text-xs font-medium mb-1 ${isDarkMode ? 'text-gray-200' : 'theme-accent-text'}`,\n                      children: field.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 23\n                    }, this), field.type === 'textarea' ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      name: field.key,\n                      value: formData[field.key],\n                      onChange: handleChange,\n                      placeholder: field.placeholder,\n                      rows: 2,\n                      className: `w-full px-3 py-2 text-sm rounded-md transition-colors resize-none theme-focus ${isDarkMode ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 25\n                    }, this) : field.type === 'select' ? /*#__PURE__*/_jsxDEV(\"select\", {\n                      name: field.key,\n                      value: formData[field.key],\n                      onChange: handleChange,\n                      className: `w-full px-3 py-2 text-sm rounded-md transition-colors theme-focus ${isDarkMode ? 'bg-slate-900 border-slate-500 text-white' : 'bg-[#f9fafb] border-gray-300 text-gray-900'}`,\n                      children: (_field$options = field.options) === null || _field$options === void 0 ? void 0 : _field$options.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: option.value,\n                        children: option.label\n                      }, option.value, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 243,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      name: field.key,\n                      value: formData[field.key],\n                      onChange: handleChange,\n                      placeholder: field.placeholder,\n                      className: `w-full px-3 py-2 text-sm rounded-md transition-colors theme-focus ${isDarkMode ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 25\n                    }, this)]\n                  }, field.key, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 21\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: generatePresentation,\n            disabled: isLoading || !isFormValid(),\n            className: `w-full py-3 px-4 text-sm rounded-lg font-semibold transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 focus:outline-none ${isLoading || !isFormValid() ? 'bg-gray-300 text-gray-500 cursor-not-allowed transform-none' : isDarkMode ? 'theme-button-dark' : 'theme-button-light'}`,\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  className: \"opacity-25\",\n                  cx: \"12\",\n                  cy: \"12\",\n                  r: \"10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  className: \"opacity-75\",\n                  fill: \"currentColor\",\n                  d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), \"Generating Presentation...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-1\",\n                children: \"\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this), \"Generate Presentation\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [isErrorVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `rounded-md border-l-4 p-3 ${isDarkMode ? 'bg-red-900 border-red-500' : 'bg-red-50 border-red-500'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: `h-4 w-4 mr-2 ${isDarkMode ? 'text-red-400' : 'text-red-400'}`,\n                xmlns: \"http://www.w3.org/2000/svg\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: `text-xs ${isDarkMode ? 'text-red-300' : 'text-red-700'}`,\n                children: errorMessage\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `rounded-lg shadow-sm border overflow-hidden ${isDarkMode ? 'bg-slate-700 border-slate-600' : 'bg-white border-gray-200'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `px-4 py-3 border-b ${isDarkMode ? 'bg-slate-600 border-slate-500' : 'bg-gradient-to-r from-pink-50 to-purple-50 border-gray-200'}`,\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: `text-sm font-semibold flex items-center ${isDarkMode ? 'text-white' : 'text-gray-800'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1\",\n                  children: \"\\uD83D\\uDCCB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this), \"Generated Presentation\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [!isOutputVisible && !isLoading && !isErrorVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3 ${isDarkMode ? 'bg-slate-600' : 'bg-gray-100'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: `w-6 h-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-400'}`,\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`,\n                  children: \"Your generated presentation will appear here\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-400'}`,\n                  children: \"Fill in the form and click generate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3 ${isDarkMode ? 'bg-slate-600' : 'theme-accent'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: `animate-spin h-6 w-6 ${isDarkMode ? 'text-gray-300' : 'theme-primary'}`,\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      className: \"opacity-25\",\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 371,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      className: \"opacity-75\",\n                      fill: \"currentColor\",\n                      d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`,\n                  children: \"Creating your presentation...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-400'}`,\n                  children: \"This may take a few moments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this), isOutputVisible && downloadUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3 ${isDarkMode ? 'bg-slate-600' : 'theme-accent'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: `w-6 h-6 ${isDarkMode ? 'text-green-400' : 'theme-primary'}`,\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: `text-sm font-semibold mb-2 ${isDarkMode ? 'text-white' : 'theme-primary'}`,\n                  children: \"Presentation Ready! \\uD83C\\uDF89\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-xs mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`,\n                  children: \"Your presentation has been generated successfully.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: downloadUrl,\n                  download: true,\n                  className: `inline-flex items-center px-4 py-2 text-sm rounded-lg font-medium transition-all ${isDarkMode ? 'theme-button-dark' : 'theme-button-light theme-border'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-3 h-3 mr-1\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 23\n                  }, this), \"Download Presentation\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), !isLoading && !isOutputVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `rounded-lg p-4 border ${isDarkMode ? 'bg-gradient-to-br from-slate-700 to-slate-600 border-slate-500' : 'bg-gradient-to-br from-pink-50 to-purple-50 theme-border'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: `text-sm font-semibold mb-2 flex items-center ${isDarkMode ? 'text-white' : 'text-gray-800'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-1\",\n                children: \"\\uD83D\\uDCA1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this), \"Pro Tips\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: `space-y-1 text-xs ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1 theme-primary\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this), \"Keep your project description concise but impactful\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1 theme-primary\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 21\n                }, this), \"Mention specific technologies and innovations\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1 theme-primary\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this), \"Include measurable business impact metrics\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1 theme-primary\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 21\n                }, this), \"Choose a template that matches your audience\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n}\n_s(PowerPointerGenerator, \"ygDyDEKCfq566OMf4iAegUGGmis=\");\n_c = PowerPointerGenerator;\nexport default PowerPointerGenerator;\n\n// import React, { useState } from 'react';  // Remove useEffect since we won't need it\n\n// function PowerPointerGenerator() {\n//   const [formData, setFormData] = useState({\n//     project_name: \"HackHelp\",\n//     problem_statement: \"Lack of centralized assistance and support for hackathon participants\",\n//     solution: \"AI-powered platform providing comprehensive hackathon support tools\",\n//     target_audience: \"Hackathon participants, organizers, and mentors\",\n//     key_features: \"LinkedIn post generator, PowerPoint creator, mentor matching, team resource hub\",\n//     tech_stack: \"React, Django, Gemini Pro, python-pptx, TailwindCSS\",\n//     business_impact: \"Improved hackathon experience and increased participant success rate\",\n//     design_template: \"1\"  // Default template\n//   });\n\n//   const [isOutputVisible, setIsOutputVisible] = useState(false);\n//   const [isErrorVisible, setIsErrorVisible] = useState(false);\n//   const [errorMessage, setErrorMessage] = useState('');\n//   const [isLoading, setIsLoading] = useState(false);\n//   const [downloadUrl, setDownloadUrl] = useState('');\n\n//   const handleChange = (e) => {\n//     const { name, value } = e.target;\n//     setFormData(prev => ({ ...prev, [name]: value }));\n//   };\n\n//   const generatePresentation = () => {\n//     setIsOutputVisible(false);\n//     setIsErrorVisible(false);\n//     setIsLoading(true);\n\n//     const payload = {\n//       ...formData,\n//       key_features: formData.key_features.split(',').map(f => f.trim()),\n//       tech_stack: formData.tech_stack.split(',').map(t => t.trim())\n//     };\n\n//     fetch(\"http://localhost:8000/api/startup/generate-powerpoint/\", {\n//       method: \"POST\",\n//       headers: { \"Content-Type\": \"application/json\" },\n//       body: JSON.stringify(payload)\n//     })\n//       .then(response => {\n//         if (!response.ok) throw new Error(`HTTP ${response.status}`);\n//         return response.json();\n//       })\n//       .then(data => {\n//         if (data.url) {\n//           const fullUrl = `http://localhost:8000/${data.url}`;\n//           setDownloadUrl(fullUrl);\n//           setIsOutputVisible(true);\n//         } else {\n//           throw new Error(\"No URL received in response\");\n//         }\n//       })\n//       .catch(error => {\n//         setErrorMessage(\"❌ Error: \" + error.message);\n//         setIsErrorVisible(true);\n//       })\n//       .finally(() => {\n//         setIsLoading(false);\n//       });\n//   };\n//   return (\n//     <div className=\"min-h-screen bg-gradient-to-b from-white to-gray-100 py-12 px-4 sm:px-6 lg:px-8\">\n//       <div className=\"max-w-4xl mx-auto\">\n//         <div className=\"text-center mb-10\">\n//           <h1 className=\"text-4xl font-bold text-black mb-3\">🎯 PowerPoint Generator</h1>\n//           <p className=\"text-gray-600\">Fill out the form below and choose a design template.</p>\n//         </div>\n\n//         <div className=\"bg-white shadow-lg rounded-xl p-6 mb-8\">\n//           <div className=\"grid grid-cols-1 md:grid-cols-2 gap-5\">\n//             {Object.entries(formData).map(([key, val]) => (\n//               <div key={key} className=\"form-group\">\n//                 <label htmlFor={key} className=\"block text-sm font-medium text-gray-700 mb-1 capitalize\">\n//                   {key.replace(/_/g, ' ')}:\n//                 </label>\n//                 {key === 'design_template' ? (\n//                   <select\n//                     id={key}\n//                     name={key}\n//                     value={val}\n//                     onChange={handleChange}\n//                     className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-sm\"\n//                   >\n//                     <option value=\"1\">Professional (Default)</option>\n//                     <option value=\"2\">Creative</option>\n//                     <option value=\"3\">Minimal</option>\n//                     <option value=\"4\">Academic</option>\n//                     <option value=\"5\">Business</option>\n//                     <option value=\"6\">Technical</option>\n//                     <option value=\"7\">Modern</option>\n//                     <option value=\"8\">Corporate</option>\n//                     <option value=\"9\">Elegant</option>\n//                   </select>\n//                 ) : (\n//                   <textarea\n//                     id={key}\n//                     name={key}\n//                     value={val}\n//                     onChange={handleChange}\n//                     rows={key === 'problem_statement' || key === 'solution' || key === 'business_impact' ? 2 : 1}\n//                     className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-sm\"\n//                   />\n//                 )}\n//               </div>\n//             ))}\n//           </div>\n//         </div>\n\n//         <div className=\"flex flex-col sm:flex-row justify-center gap-4 mb-8\">\n//         <button\n//             className={`px-6 py-3 rounded-md text-white font-medium transition duration-200 ${\n//             isLoading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'\n//             } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center`}\n//             onClick={generatePresentation}\n//             disabled={isLoading}\n//         >\n//             {isLoading ? (\n//             <>\n//                 <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n//                 <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n//                 <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n//                 </svg>\n//                 Generating...\n//             </>\n//             ) : (\n//             'Generate PowerPoint'\n//             )}\n//         </button>\n//         </div>\n\n//         {isErrorVisible && (\n//           <div className=\"bg-red-50 border-l-4 border-red-500 p-4 mb-8 rounded\">\n//             <div className=\"flex\">\n//               <div className=\"flex-shrink-0\">\n//                 <svg className=\"h-5 w-5 text-red-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n//                   <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n//                 </svg>\n//               </div>\n//               <div className=\"ml-3\">\n//                 <p className=\"text-sm text-red-700\">\n//                   {errorMessage}\n//                 </p>\n//               </div>\n//             </div>\n//           </div>\n//         )}\n\n//         {isOutputVisible && downloadUrl && (\n//           <div className=\"bg-f3efec rounded-xl shadow-md p-6 mb-8\">\n//             <h3 className=\"text-lg font-medium mb-4 text-gray-800\">Your PowerPoint is Ready!</h3>\n//             <div className=\"flex justify-center\">\n//               <a\n//                 href={downloadUrl}\n//                 download\n//                 className=\"px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition duration-200 flex items-center\"\n//               >\n//                 <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n//                   <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"/>\n//                 </svg>\n//                 Download Presentation\n//               </a>\n//             </div>\n//           </div>\n//         )}\n//       </div>\n//     </div>\n//   );\n// }\n\n// export default PowerPointerGenerator;\nvar _c;\n$RefreshReg$(_c, \"PowerPointerGenerator\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "PowerPointerGenerator", "isDarkMode", "_s", "activeTab", "setActiveTab", "formData", "setFormData", "project_name", "problem_statement", "solution", "target_audience", "key_features", "tech_stack", "business_impact", "design_template", "isOutputVisible", "setIsOutputVisible", "isErrorVisible", "setIsErrorVisible", "errorMessage", "setErrorMessage", "isLoading", "setIsLoading", "downloadUrl", "setDownloadUrl", "tabs", "id", "label", "icon", "handleChange", "e", "name", "value", "target", "prev", "generatePresentation", "payload", "split", "map", "f", "trim", "t", "fetch", "method", "headers", "body", "JSON", "stringify", "then", "response", "ok", "Error", "status", "json", "data", "url", "fullUrl", "catch", "error", "message", "finally", "getFieldsForTab", "tabId", "key", "type", "placeholder", "options", "isFormValid", "className", "children", "jsx", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "tab", "onClick", "field", "_field$options", "onChange", "rows", "option", "disabled", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d", "xmlns", "fillRule", "clipRule", "strokeLinecap", "strokeLinejoin", "href", "download", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Hackhelp/frontend/src/Pages/PowerPointer.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nfunction PowerPointerGenerator({ isDarkMode = false }) {\n  const [activeTab, setActiveTab] = useState('basic');\n  const [formData, setFormData] = useState({\n    project_name: \"\",\n    problem_statement: \"\",\n    solution: \"\",\n    target_audience: \"\",\n    key_features: \"\",\n    tech_stack: \"\",\n    business_impact: \"\",\n    design_template: \"1\"\n  });\n\n  const [isOutputVisible, setIsOutputVisible] = useState(false);\n  const [isErrorVisible, setIsErrorVisible] = useState(false);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [downloadUrl, setDownloadUrl] = useState('');\n\n  const tabs = [\n    { id: 'basic', label: 'Basic Info', icon: '👤' },\n    { id: 'project', label: 'Project', icon: '💡' },\n    { id: 'technical', label: 'Technical', icon: '⚙️' },\n    { id: 'design', label: 'Design', icon: '🎨' }\n  ];\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const generatePresentation = () => {\n    setIsOutputVisible(false);\n    setIsErrorVisible(false);\n    setIsLoading(true);\n    \n    const payload = {\n      ...formData,\n      key_features: formData.key_features.split(',').map(f => f.trim()),\n      tech_stack: formData.tech_stack.split(',').map(t => t.trim())\n    };\n\n    fetch(\"http://localhost:8000/api/startup/generate-powerpoint/\", {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\" },\n      body: JSON.stringify(payload)\n    })\n      .then(response => {\n        if (!response.ok) throw new Error(`HTTP ${response.status}`);\n        return response.json();\n      })\n      .then(data => {\n        if (data.url) {\n          const fullUrl = `http://localhost:8000/${data.url}`;\n          setDownloadUrl(fullUrl);\n          setIsOutputVisible(true);\n        } else {\n          throw new Error(\"No URL received in response\");\n        }\n      })\n      .catch(error => {\n        setErrorMessage(\"❌ Error: \" + error.message);\n        setIsErrorVisible(true);\n      })\n      .finally(() => {\n        setIsLoading(false);\n      });\n  };\n\n  const getFieldsForTab = (tabId) => {\n    switch (tabId) {\n      case 'basic':\n        return [\n          { key: 'project_name', label: 'Project Name', type: 'input', placeholder: 'EcoTracker Pro' },\n          { key: 'target_audience', label: 'Target Audience', type: 'input', placeholder: 'Environmental enthusiasts, researchers, and policy makers' }\n        ];\n      case 'project':\n        return [\n          { key: 'problem_statement', label: 'Problem Statement', type: 'textarea', placeholder: 'Lack of accessible tools for individuals to track and reduce their carbon footprint in daily activities' },\n          { key: 'solution', label: 'Solution', type: 'textarea', placeholder: 'AI-powered mobile app that automatically tracks carbon emissions and provides personalized reduction strategies' },\n          { key: 'key_features', label: 'Key Features', type: 'textarea', placeholder: 'Real-time emission tracking, AI recommendations, community challenges, progress analytics, carbon offset marketplace' },\n          { key: 'business_impact', label: 'Business Impact', type: 'textarea', placeholder: 'Projected 30% reduction in user carbon footprint within 6 months, potential to impact 1M+ users globally' }\n        ];\n      case 'technical':\n        return [\n          { key: 'tech_stack', label: 'Tech Stack', type: 'textarea', placeholder: 'React Native, Node.js, TensorFlow, MongoDB, AWS, Google Maps API, Stripe' }\n        ];\n      case 'design':\n        return [\n          { key: 'design_template', label: 'Design Template', type: 'select', options: [\n            { value: \"1\", label: \"Professional (Default)\" },\n            { value: \"2\", label: \"Creative\" },\n            { value: \"3\", label: \"Minimal\" },\n            { value: \"4\", label: \"Academic\" },\n            { value: \"5\", label: \"Business\" },\n            { value: \"6\", label: \"Technical\" },\n            { value: \"7\", label: \"Modern\" },\n            { value: \"8\", label: \"Corporate\" },\n            { value: \"9\", label: \"Elegant\" }\n          ]}\n        ];\n      default:\n        return [];\n    }\n  };\n\n  const isFormValid = () => {\n    return formData.project_name && formData.problem_statement && formData.solution;\n  };\n\n  return (\n    <div className={`min-h-screen p-4 transition-colors duration-200 ${\n      isDarkMode ? 'bg-[#111827]' : 'bg-gray-50'\n    }`}>\n      {/* Custom CSS for theme colors */}\n      <style jsx>{`\n        .theme-primary { color: #621639; }\n        .theme-primary-light { color: #831843; }\n        .theme-accent { background-color: #fce7f3; }\n        .theme-accent-text { color: #831843; }\n        .theme-border { border-color: #621639; }\n        .theme-focus:focus { \n          --tw-ring-color: #621639; \n          border-color: #621639; \n        }\n        .theme-button-light {\n          background-color: #fce7f3;\n          color: #831843;\n        }\n        .theme-button-light:hover {\n          background-color: #621639;\n          color: white;\n        }\n        .theme-button-dark {\n          background-color: #4c1d3b;\n          color: #fce7f3;\n        }\n        .theme-button-dark:hover {\n          background-color: #621639;\n        }\n        .theme-tab-active-light {\n          background-color: white;\n          color: #831843;\n          border-bottom: 2px solid #621639;\n        }\n        .theme-tab-active-dark {\n          background-color: #475569;\n          color: white;\n          border-bottom: 2px solid #621639;\n        }\n        .theme-tab-inactive-light {\n          color: #831843;\n        }\n        .theme-tab-inactive-light:hover {\n          background-color: #f9fafb;\n        }\n        .theme-tab-inactive-dark {\n          color: #d1d5db;\n        }\n        .theme-tab-inactive-dark:hover {\n          background-color: #4b5563;\n        }\n      `}</style>\n\n      <div className=\"max-w-5xl mx-auto\">\n        {/* Header */}\n        <div className=\"text-center mb-6\">\n          <h1 className={`text-xl font-bold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>\n            ✨ PowerPoint Generator\n          </h1>\n          <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n            Create professional hackathon presentations in minutes\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-6\">\n          {/* Form Section */}\n          <div className=\"space-y-4\">\n            {/* Tab Navigation */}\n            <div className={`rounded-lg shadow-sm border overflow-hidden ${\n              isDarkMode \n                ? 'bg-[#1f2937] border-slate-600' \n                : 'bg-white border-gray-200'\n            }`}>\n              <div className={`flex border-b ${\n                isDarkMode \n                  ? 'bg-slate-600 border-slate-500' \n                  : 'theme-accent border-gray-200'\n              }`}>\n                {tabs.map((tab) => (\n                  <button\n                    key={tab.id}\n                    onClick={() => setActiveTab(tab.id)}\n                    className={`flex-1 px-3 py-2 text-xs font-medium transition-all duration-200 focus:outline-none ${\n                      activeTab === tab.id\n                        ? isDarkMode ? 'theme-tab-active-dark' : 'theme-tab-active-light'\n                        : isDarkMode ? 'theme-tab-inactive-dark' : 'theme-tab-inactive-light'\n                    }`}\n                  >\n                    <span className=\"mr-1\">{tab.icon}</span>\n                    {tab.label}\n                  </button>\n                ))}\n              </div>\n\n              {/* Form Content */}\n              <div className=\"p-4\">\n                <div className=\"space-y-3\">\n                  {getFieldsForTab(activeTab).map((field) => (\n                    <div key={field.key}>\n                      <label className={`block text-xs font-medium mb-1 ${\n                        isDarkMode ? 'text-gray-200' : 'theme-accent-text'\n                      }`}>\n                        {field.label}\n                      </label>\n                      {field.type === 'textarea' ? (\n                        <textarea\n                          name={field.key}\n                          value={formData[field.key]}\n                          onChange={handleChange}\n                          placeholder={field.placeholder}\n                          rows={2}\n                          className={`w-full px-3 py-2 text-sm rounded-md transition-colors resize-none theme-focus ${\n                            isDarkMode \n                              ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' \n                              : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'\n                          }`}\n                        />\n                      ) : field.type === 'select' ? (\n                        <select\n                          name={field.key}\n                          value={formData[field.key]}\n                          onChange={handleChange}\n                          className={`w-full px-3 py-2 text-sm rounded-md transition-colors theme-focus ${\n                            isDarkMode \n                              ? 'bg-slate-900 border-slate-500 text-white' \n                              : 'bg-[#f9fafb] border-gray-300 text-gray-900'\n                          }`}\n                        >\n                          {field.options?.map((option) => (\n                            <option key={option.value} value={option.value}>\n                              {option.label}\n                            </option>\n                          ))}\n                        </select>\n                      ) : (\n                        <input\n                          type=\"text\"\n                          name={field.key}\n                          value={formData[field.key]}\n                          onChange={handleChange}\n                          placeholder={field.placeholder}\n                          className={`w-full px-3 py-2 text-sm rounded-md transition-colors theme-focus ${\n                            isDarkMode \n                              ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' \n                              : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'\n                          }`}\n                        />\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Action Button */}\n            <button\n              onClick={generatePresentation}\n              disabled={isLoading || !isFormValid()}\n              className={`w-full py-3 px-4 text-sm rounded-lg font-semibold transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 focus:outline-none ${\n                isLoading || !isFormValid()\n                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed transform-none'\n                  : isDarkMode\n                    ? 'theme-button-dark'\n                    : 'theme-button-light'\n              }`}\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center\">\n                  <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Generating Presentation...\n                </div>\n              ) : (\n                <div className=\"flex items-center justify-center\">\n                  <span className=\"mr-1\">🚀</span>\n                  Generate Presentation\n                </div>\n              )}\n            </button>\n          </div>\n\n          {/* Output Section */}\n          <div className=\"space-y-4\">\n            {/* Error Message */}\n            {isErrorVisible && (\n              <div className={`rounded-md border-l-4 p-3 ${\n                isDarkMode \n                  ? 'bg-red-900 border-red-500' \n                  : 'bg-red-50 border-red-500'\n              }`}>\n                <div className=\"flex items-center\">\n                  <svg className={`h-4 w-4 mr-2 ${\n                    isDarkMode ? 'text-red-400' : 'text-red-400'\n                  }`} xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <p className={`text-xs ${\n                    isDarkMode ? 'text-red-300' : 'text-red-700'\n                  }`}>\n                    {errorMessage}\n                  </p>\n                </div>\n              </div>\n            )}\n\n            {/* Preview Card */}\n            <div className={`rounded-lg shadow-sm border overflow-hidden ${\n              isDarkMode \n                ? 'bg-slate-700 border-slate-600' \n                : 'bg-white border-gray-200'\n            }`}>\n              <div className={`px-4 py-3 border-b ${\n                isDarkMode \n                  ? 'bg-slate-600 border-slate-500' \n                  : 'bg-gradient-to-r from-pink-50 to-purple-50 border-gray-200'\n              }`}>\n                <h3 className={`text-sm font-semibold flex items-center ${\n                  isDarkMode ? 'text-white' : 'text-gray-800'\n                }`}>\n                  <span className=\"mr-1\">📋</span>\n                  Generated Presentation\n                </h3>\n              </div>\n\n              <div className=\"p-4\">\n                {/* Default State */}\n                {!isOutputVisible && !isLoading && !isErrorVisible && (\n                  <div className=\"text-center py-8\">\n                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3 ${\n                      isDarkMode ? 'bg-slate-600' : 'bg-gray-100'\n                    }`}>\n                      <svg className={`w-6 h-6 ${\n                        isDarkMode ? 'text-gray-400' : 'text-gray-400'\n                      }`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"/>\n                      </svg>\n                    </div>\n                    <p className={`text-sm mb-1 ${\n                      isDarkMode ? 'text-gray-300' : 'text-gray-600'\n                    }`}>Your generated presentation will appear here</p>\n                    <p className={`text-xs ${\n                      isDarkMode ? 'text-gray-400' : 'text-gray-400'\n                    }`}>Fill in the form and click generate</p>\n                  </div>\n                )}\n\n                {/* Loading State */}\n                {isLoading && (\n                  <div className=\"text-center py-8\">\n                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3 ${\n                      isDarkMode ? 'bg-slate-600' : 'theme-accent'\n                    }`}>\n                      <svg className={`animate-spin h-6 w-6 ${\n                        isDarkMode ? 'text-gray-300' : 'theme-primary'\n                      }`} xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                    </div>\n                    <p className={`text-sm mb-1 ${\n                      isDarkMode ? 'text-gray-300' : 'text-gray-600'\n                    }`}>Creating your presentation...</p>\n                    <p className={`text-xs ${\n                      isDarkMode ? 'text-gray-400' : 'text-gray-400'\n                    }`}>This may take a few moments</p>\n                  </div>\n                )}\n\n                {/* Success State */}\n                {isOutputVisible && downloadUrl && (\n                  <div className=\"text-center py-8\">\n                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3 ${\n                      isDarkMode ? 'bg-slate-600' : 'theme-accent'\n                    }`}>\n                      <svg className={`w-6 h-6 ${\n                        isDarkMode ? 'text-green-400' : 'theme-primary'\n                      }`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"/>\n                      </svg>\n                    </div>\n                    <h4 className={`text-sm font-semibold mb-2 ${\n                      isDarkMode ? 'text-white' : 'theme-primary'\n                    }`}>Presentation Ready! 🎉</h4>\n                    <p className={`text-xs mb-4 ${\n                      isDarkMode ? 'text-gray-300' : 'text-gray-600'\n                    }`}>Your presentation has been generated successfully.</p>\n                    <a\n                      href={downloadUrl}\n                      download\n                      className={`inline-flex items-center px-4 py-2 text-sm rounded-lg font-medium transition-all ${\n                        isDarkMode\n                          ? 'theme-button-dark'\n                          : 'theme-button-light theme-border'\n                      }`}\n                    >\n                      <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"/>\n                      </svg>\n                      Download Presentation\n                    </a>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Tips Card */}\n            {!isLoading && !isOutputVisible && (\n              <div className={`rounded-lg p-4 border ${\n                isDarkMode \n                  ? 'bg-gradient-to-br from-slate-700 to-slate-600 border-slate-500' \n                  : 'bg-gradient-to-br from-pink-50 to-purple-50 theme-border'\n              }`}>\n                <h4 className={`text-sm font-semibold mb-2 flex items-center ${\n                  isDarkMode ? 'text-white' : 'text-gray-800'\n                }`}>\n                  <span className=\"mr-1\">💡</span>\n                  Pro Tips\n                </h4>\n                <ul className={`space-y-1 text-xs ${\n                  isDarkMode ? 'text-gray-300' : 'text-gray-600'\n                }`}>\n                  <li className=\"flex items-start\">\n                    <span className=\"mr-1 theme-primary\">•</span>\n                    Keep your project description concise but impactful\n                  </li>\n                  <li className=\"flex items-start\">\n                    <span className=\"mr-1 theme-primary\">•</span>\n                    Mention specific technologies and innovations\n                  </li>\n                  <li className=\"flex items-start\">\n                    <span className=\"mr-1 theme-primary\">•</span>\n                    Include measurable business impact metrics\n                  </li>\n                  <li className=\"flex items-start\">\n                    <span className=\"mr-1 theme-primary\">•</span>\n                    Choose a template that matches your audience\n                  </li>\n                </ul>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default PowerPointerGenerator;\n\n\n\n// import React, { useState } from 'react';  // Remove useEffect since we won't need it\n\n// function PowerPointerGenerator() {\n//   const [formData, setFormData] = useState({\n//     project_name: \"HackHelp\",\n//     problem_statement: \"Lack of centralized assistance and support for hackathon participants\",\n//     solution: \"AI-powered platform providing comprehensive hackathon support tools\",\n//     target_audience: \"Hackathon participants, organizers, and mentors\",\n//     key_features: \"LinkedIn post generator, PowerPoint creator, mentor matching, team resource hub\",\n//     tech_stack: \"React, Django, Gemini Pro, python-pptx, TailwindCSS\",\n//     business_impact: \"Improved hackathon experience and increased participant success rate\",\n//     design_template: \"1\"  // Default template\n//   });\n\n//   const [isOutputVisible, setIsOutputVisible] = useState(false);\n//   const [isErrorVisible, setIsErrorVisible] = useState(false);\n//   const [errorMessage, setErrorMessage] = useState('');\n//   const [isLoading, setIsLoading] = useState(false);\n//   const [downloadUrl, setDownloadUrl] = useState('');\n\n//   const handleChange = (e) => {\n//     const { name, value } = e.target;\n//     setFormData(prev => ({ ...prev, [name]: value }));\n//   };\n\n//   const generatePresentation = () => {\n//     setIsOutputVisible(false);\n//     setIsErrorVisible(false);\n//     setIsLoading(true);\n    \n//     const payload = {\n//       ...formData,\n//       key_features: formData.key_features.split(',').map(f => f.trim()),\n//       tech_stack: formData.tech_stack.split(',').map(t => t.trim())\n//     };\n\n//     fetch(\"http://localhost:8000/api/startup/generate-powerpoint/\", {\n//       method: \"POST\",\n//       headers: { \"Content-Type\": \"application/json\" },\n//       body: JSON.stringify(payload)\n//     })\n//       .then(response => {\n//         if (!response.ok) throw new Error(`HTTP ${response.status}`);\n//         return response.json();\n//       })\n//       .then(data => {\n//         if (data.url) {\n//           const fullUrl = `http://localhost:8000/${data.url}`;\n//           setDownloadUrl(fullUrl);\n//           setIsOutputVisible(true);\n//         } else {\n//           throw new Error(\"No URL received in response\");\n//         }\n//       })\n//       .catch(error => {\n//         setErrorMessage(\"❌ Error: \" + error.message);\n//         setIsErrorVisible(true);\n//       })\n//       .finally(() => {\n//         setIsLoading(false);\n//       });\n//   };\n//   return (\n//     <div className=\"min-h-screen bg-gradient-to-b from-white to-gray-100 py-12 px-4 sm:px-6 lg:px-8\">\n//       <div className=\"max-w-4xl mx-auto\">\n//         <div className=\"text-center mb-10\">\n//           <h1 className=\"text-4xl font-bold text-black mb-3\">🎯 PowerPoint Generator</h1>\n//           <p className=\"text-gray-600\">Fill out the form below and choose a design template.</p>\n//         </div>\n\n//         <div className=\"bg-white shadow-lg rounded-xl p-6 mb-8\">\n//           <div className=\"grid grid-cols-1 md:grid-cols-2 gap-5\">\n//             {Object.entries(formData).map(([key, val]) => (\n//               <div key={key} className=\"form-group\">\n//                 <label htmlFor={key} className=\"block text-sm font-medium text-gray-700 mb-1 capitalize\">\n//                   {key.replace(/_/g, ' ')}:\n//                 </label>\n//                 {key === 'design_template' ? (\n//                   <select\n//                     id={key}\n//                     name={key}\n//                     value={val}\n//                     onChange={handleChange}\n//                     className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-sm\"\n//                   >\n//                     <option value=\"1\">Professional (Default)</option>\n//                     <option value=\"2\">Creative</option>\n//                     <option value=\"3\">Minimal</option>\n//                     <option value=\"4\">Academic</option>\n//                     <option value=\"5\">Business</option>\n//                     <option value=\"6\">Technical</option>\n//                     <option value=\"7\">Modern</option>\n//                     <option value=\"8\">Corporate</option>\n//                     <option value=\"9\">Elegant</option>\n//                   </select>\n//                 ) : (\n//                   <textarea\n//                     id={key}\n//                     name={key}\n//                     value={val}\n//                     onChange={handleChange}\n//                     rows={key === 'problem_statement' || key === 'solution' || key === 'business_impact' ? 2 : 1}\n//                     className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-sm\"\n//                   />\n//                 )}\n//               </div>\n//             ))}\n//           </div>\n//         </div>\n\n//         <div className=\"flex flex-col sm:flex-row justify-center gap-4 mb-8\">\n//         <button\n//             className={`px-6 py-3 rounded-md text-white font-medium transition duration-200 ${\n//             isLoading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'\n//             } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center`}\n//             onClick={generatePresentation}\n//             disabled={isLoading}\n//         >\n//             {isLoading ? (\n//             <>\n//                 <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n//                 <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n//                 <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n//                 </svg>\n//                 Generating...\n//             </>\n//             ) : (\n//             'Generate PowerPoint'\n//             )}\n//         </button>\n//         </div>\n\n//         {isErrorVisible && (\n//           <div className=\"bg-red-50 border-l-4 border-red-500 p-4 mb-8 rounded\">\n//             <div className=\"flex\">\n//               <div className=\"flex-shrink-0\">\n//                 <svg className=\"h-5 w-5 text-red-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n//                   <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n//                 </svg>\n//               </div>\n//               <div className=\"ml-3\">\n//                 <p className=\"text-sm text-red-700\">\n//                   {errorMessage}\n//                 </p>\n//               </div>\n//             </div>\n//           </div>\n//         )}\n\n//         {isOutputVisible && downloadUrl && (\n//           <div className=\"bg-f3efec rounded-xl shadow-md p-6 mb-8\">\n//             <h3 className=\"text-lg font-medium mb-4 text-gray-800\">Your PowerPoint is Ready!</h3>\n//             <div className=\"flex justify-center\">\n//               <a\n//                 href={downloadUrl}\n//                 download\n//                 className=\"px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition duration-200 flex items-center\"\n//               >\n//                 <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n//                   <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"/>\n//                 </svg>\n//                 Download Presentation\n//               </a>\n//             </div>\n//           </div>\n//         )}\n//       </div>\n//     </div>\n//   );\n// }\n\n// export default PowerPointerGenerator;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,qBAAqBA,CAAC;EAAEC,UAAU,GAAG;AAAM,CAAC,EAAE;EAAAC,EAAA;EACrD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,YAAY,EAAE,EAAE;IAChBC,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,EAAE;IACdC,eAAe,EAAE,EAAE;IACnBC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM4B,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAK,CAAC,EAChD;IAAEF,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC/C;IAAEF,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAK,CAAC,EACnD;IAAEF,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAK,CAAC,CAC9C;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC3B,WAAW,CAAC4B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IACjCnB,kBAAkB,CAAC,KAAK,CAAC;IACzBE,iBAAiB,CAAC,KAAK,CAAC;IACxBI,YAAY,CAAC,IAAI,CAAC;IAElB,MAAMc,OAAO,GAAG;MACd,GAAG/B,QAAQ;MACXM,YAAY,EAAEN,QAAQ,CAACM,YAAY,CAAC0B,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;MACjE5B,UAAU,EAAEP,QAAQ,CAACO,UAAU,CAACyB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACG,CAAC,IAAIA,CAAC,CAACD,IAAI,CAAC,CAAC;IAC9D,CAAC;IAEDE,KAAK,CAAC,wDAAwD,EAAE;MAC9DC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACX,OAAO;IAC9B,CAAC,CAAC,CACCY,IAAI,CAACC,QAAQ,IAAI;MAChB,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,QAAQF,QAAQ,CAACG,MAAM,EAAE,CAAC;MAC5D,OAAOH,QAAQ,CAACI,IAAI,CAAC,CAAC;IACxB,CAAC,CAAC,CACDL,IAAI,CAACM,IAAI,IAAI;MACZ,IAAIA,IAAI,CAACC,GAAG,EAAE;QACZ,MAAMC,OAAO,GAAG,yBAAyBF,IAAI,CAACC,GAAG,EAAE;QACnD/B,cAAc,CAACgC,OAAO,CAAC;QACvBxC,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC,MAAM;QACL,MAAM,IAAImC,KAAK,CAAC,6BAA6B,CAAC;MAChD;IACF,CAAC,CAAC,CACDM,KAAK,CAACC,KAAK,IAAI;MACdtC,eAAe,CAAC,WAAW,GAAGsC,KAAK,CAACC,OAAO,CAAC;MAC5CzC,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,CACD0C,OAAO,CAAC,MAAM;MACbtC,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACN,CAAC;EAED,MAAMuC,eAAe,GAAIC,KAAK,IAAK;IACjC,QAAQA,KAAK;MACX,KAAK,OAAO;QACV,OAAO,CACL;UAAEC,GAAG,EAAE,cAAc;UAAEpC,KAAK,EAAE,cAAc;UAAEqC,IAAI,EAAE,OAAO;UAAEC,WAAW,EAAE;QAAiB,CAAC,EAC5F;UAAEF,GAAG,EAAE,iBAAiB;UAAEpC,KAAK,EAAE,iBAAiB;UAAEqC,IAAI,EAAE,OAAO;UAAEC,WAAW,EAAE;QAA4D,CAAC,CAC9I;MACH,KAAK,SAAS;QACZ,OAAO,CACL;UAAEF,GAAG,EAAE,mBAAmB;UAAEpC,KAAK,EAAE,mBAAmB;UAAEqC,IAAI,EAAE,UAAU;UAAEC,WAAW,EAAE;QAA0G,CAAC,EAClM;UAAEF,GAAG,EAAE,UAAU;UAAEpC,KAAK,EAAE,UAAU;UAAEqC,IAAI,EAAE,UAAU;UAAEC,WAAW,EAAE;QAAkH,CAAC,EACxL;UAAEF,GAAG,EAAE,cAAc;UAAEpC,KAAK,EAAE,cAAc;UAAEqC,IAAI,EAAE,UAAU;UAAEC,WAAW,EAAE;QAAuH,CAAC,EACrM;UAAEF,GAAG,EAAE,iBAAiB;UAAEpC,KAAK,EAAE,iBAAiB;UAAEqC,IAAI,EAAE,UAAU;UAAEC,WAAW,EAAE;QAA2G,CAAC,CAChM;MACH,KAAK,WAAW;QACd,OAAO,CACL;UAAEF,GAAG,EAAE,YAAY;UAAEpC,KAAK,EAAE,YAAY;UAAEqC,IAAI,EAAE,UAAU;UAAEC,WAAW,EAAE;QAA2E,CAAC,CACtJ;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEF,GAAG,EAAE,iBAAiB;UAAEpC,KAAK,EAAE,iBAAiB;UAAEqC,IAAI,EAAE,QAAQ;UAAEE,OAAO,EAAE,CAC3E;YAAElC,KAAK,EAAE,GAAG;YAAEL,KAAK,EAAE;UAAyB,CAAC,EAC/C;YAAEK,KAAK,EAAE,GAAG;YAAEL,KAAK,EAAE;UAAW,CAAC,EACjC;YAAEK,KAAK,EAAE,GAAG;YAAEL,KAAK,EAAE;UAAU,CAAC,EAChC;YAAEK,KAAK,EAAE,GAAG;YAAEL,KAAK,EAAE;UAAW,CAAC,EACjC;YAAEK,KAAK,EAAE,GAAG;YAAEL,KAAK,EAAE;UAAW,CAAC,EACjC;YAAEK,KAAK,EAAE,GAAG;YAAEL,KAAK,EAAE;UAAY,CAAC,EAClC;YAAEK,KAAK,EAAE,GAAG;YAAEL,KAAK,EAAE;UAAS,CAAC,EAC/B;YAAEK,KAAK,EAAE,GAAG;YAAEL,KAAK,EAAE;UAAY,CAAC,EAClC;YAAEK,KAAK,EAAE,GAAG;YAAEL,KAAK,EAAE;UAAU,CAAC;QACjC,CAAC,CACH;MACH;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAMwC,WAAW,GAAGA,CAAA,KAAM;IACxB,OAAO9D,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACG,iBAAiB,IAAIH,QAAQ,CAACI,QAAQ;EACjF,CAAC;EAED,oBACEV,OAAA;IAAKqE,SAAS,EAAE,mDACdnE,UAAU,GAAG,cAAc,GAAG,YAAY,EACzC;IAAAoE,QAAA,gBAEDtE,OAAA;MAAOuE,GAAG;MAAAD,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEV3E,OAAA;MAAKqE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhCtE,OAAA;QAAKqE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BtE,OAAA;UAAIqE,SAAS,EAAE,0BAA0BnE,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;UAAAoE,QAAA,EAAC;QAExF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3E,OAAA;UAAGqE,SAAS,EAAE,WAAWnE,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;UAAAoE,QAAA,EAAC;QAE3E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN3E,OAAA;QAAKqE,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAExCtE,OAAA;UAAKqE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBtE,OAAA;YAAKqE,SAAS,EAAE,+CACdnE,UAAU,GACN,+BAA+B,GAC/B,0BAA0B,EAC7B;YAAAoE,QAAA,gBACDtE,OAAA;cAAKqE,SAAS,EAAE,iBACdnE,UAAU,GACN,+BAA+B,GAC/B,8BAA8B,EACjC;cAAAoE,QAAA,EACA5C,IAAI,CAACa,GAAG,CAAEqC,GAAG,iBACZ5E,OAAA;gBAEE6E,OAAO,EAAEA,CAAA,KAAMxE,YAAY,CAACuE,GAAG,CAACjD,EAAE,CAAE;gBACpC0C,SAAS,EAAE,uFACTjE,SAAS,KAAKwE,GAAG,CAACjD,EAAE,GAChBzB,UAAU,GAAG,uBAAuB,GAAG,wBAAwB,GAC/DA,UAAU,GAAG,yBAAyB,GAAG,0BAA0B,EACtE;gBAAAoE,QAAA,gBAEHtE,OAAA;kBAAMqE,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEM,GAAG,CAAC/C;gBAAI;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACvCC,GAAG,CAAChD,KAAK;cAAA,GATLgD,GAAG,CAACjD,EAAE;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUL,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN3E,OAAA;cAAKqE,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClBtE,OAAA;gBAAKqE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBR,eAAe,CAAC1D,SAAS,CAAC,CAACmC,GAAG,CAAEuC,KAAK;kBAAA,IAAAC,cAAA;kBAAA,oBACpC/E,OAAA;oBAAAsE,QAAA,gBACEtE,OAAA;sBAAOqE,SAAS,EAAE,kCAChBnE,UAAU,GAAG,eAAe,GAAG,mBAAmB,EACjD;sBAAAoE,QAAA,EACAQ,KAAK,CAAClD;oBAAK;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,EACPG,KAAK,CAACb,IAAI,KAAK,UAAU,gBACxBjE,OAAA;sBACEgC,IAAI,EAAE8C,KAAK,CAACd,GAAI;sBAChB/B,KAAK,EAAE3B,QAAQ,CAACwE,KAAK,CAACd,GAAG,CAAE;sBAC3BgB,QAAQ,EAAElD,YAAa;sBACvBoC,WAAW,EAAEY,KAAK,CAACZ,WAAY;sBAC/Be,IAAI,EAAE,CAAE;sBACRZ,SAAS,EAAE,iFACTnE,UAAU,GACN,+DAA+D,GAC/D,iEAAiE;oBACpE;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,GACAG,KAAK,CAACb,IAAI,KAAK,QAAQ,gBACzBjE,OAAA;sBACEgC,IAAI,EAAE8C,KAAK,CAACd,GAAI;sBAChB/B,KAAK,EAAE3B,QAAQ,CAACwE,KAAK,CAACd,GAAG,CAAE;sBAC3BgB,QAAQ,EAAElD,YAAa;sBACvBuC,SAAS,EAAE,qEACTnE,UAAU,GACN,0CAA0C,GAC1C,4CAA4C,EAC/C;sBAAAoE,QAAA,GAAAS,cAAA,GAEFD,KAAK,CAACX,OAAO,cAAAY,cAAA,uBAAbA,cAAA,CAAexC,GAAG,CAAE2C,MAAM,iBACzBlF,OAAA;wBAA2BiC,KAAK,EAAEiD,MAAM,CAACjD,KAAM;wBAAAqC,QAAA,EAC5CY,MAAM,CAACtD;sBAAK,GADFsD,MAAM,CAACjD,KAAK;wBAAAuC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEjB,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC,gBAET3E,OAAA;sBACEiE,IAAI,EAAC,MAAM;sBACXjC,IAAI,EAAE8C,KAAK,CAACd,GAAI;sBAChB/B,KAAK,EAAE3B,QAAQ,CAACwE,KAAK,CAACd,GAAG,CAAE;sBAC3BgB,QAAQ,EAAElD,YAAa;sBACvBoC,WAAW,EAAEY,KAAK,CAACZ,WAAY;sBAC/BG,SAAS,EAAE,qEACTnE,UAAU,GACN,+DAA+D,GAC/D,iEAAiE;oBACpE;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CACF;kBAAA,GAjDOG,KAAK,CAACd,GAAG;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAkDd,CAAC;gBAAA,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3E,OAAA;YACE6E,OAAO,EAAEzC,oBAAqB;YAC9B+C,QAAQ,EAAE7D,SAAS,IAAI,CAAC8C,WAAW,CAAC,CAAE;YACtCC,SAAS,EAAE,+JACT/C,SAAS,IAAI,CAAC8C,WAAW,CAAC,CAAC,GACvB,6DAA6D,GAC7DlE,UAAU,GACR,mBAAmB,GACnB,oBAAoB,EACzB;YAAAoE,QAAA,EAEFhD,SAAS,gBACRtB,OAAA;cAAKqE,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CtE,OAAA;gBAAKqE,SAAS,EAAC,iCAAiC;gBAACe,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAAAf,QAAA,gBAC9EtE,OAAA;kBAAQqE,SAAS,EAAC,YAAY;kBAACiB,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACrG3E,OAAA;kBAAMqE,SAAS,EAAC,YAAY;kBAACe,IAAI,EAAC,cAAc;kBAACO,CAAC,EAAC;gBAAiH;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzK,CAAC,8BAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAEN3E,OAAA;cAAKqE,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CtE,OAAA;gBAAMqE,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,yBAElC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN3E,OAAA;UAAKqE,SAAS,EAAC,WAAW;UAAAC,QAAA,GAEvBpD,cAAc,iBACblB,OAAA;YAAKqE,SAAS,EAAE,6BACdnE,UAAU,GACN,2BAA2B,GAC3B,0BAA0B,EAC7B;YAAAoE,QAAA,eACDtE,OAAA;cAAKqE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtE,OAAA;gBAAKqE,SAAS,EAAE,gBACdnE,UAAU,GAAG,cAAc,GAAG,cAAc,EAC3C;gBAAC0F,KAAK,EAAC,4BAA4B;gBAACP,OAAO,EAAC,WAAW;gBAACD,IAAI,EAAC,cAAc;gBAAAd,QAAA,eAC5EtE,OAAA;kBAAM6F,QAAQ,EAAC,SAAS;kBAACF,CAAC,EAAC,yNAAyN;kBAACG,QAAQ,EAAC;gBAAS;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvQ,CAAC,eACN3E,OAAA;gBAAGqE,SAAS,EAAE,WACZnE,UAAU,GAAG,cAAc,GAAG,cAAc,EAC3C;gBAAAoE,QAAA,EACAlD;cAAY;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD3E,OAAA;YAAKqE,SAAS,EAAE,+CACdnE,UAAU,GACN,+BAA+B,GAC/B,0BAA0B,EAC7B;YAAAoE,QAAA,gBACDtE,OAAA;cAAKqE,SAAS,EAAE,sBACdnE,UAAU,GACN,+BAA+B,GAC/B,4DAA4D,EAC/D;cAAAoE,QAAA,eACDtE,OAAA;gBAAIqE,SAAS,EAAE,2CACbnE,UAAU,GAAG,YAAY,GAAG,eAAe,EAC1C;gBAAAoE,QAAA,gBACDtE,OAAA;kBAAMqE,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,0BAElC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEN3E,OAAA;cAAKqE,SAAS,EAAC,KAAK;cAAAC,QAAA,GAEjB,CAACtD,eAAe,IAAI,CAACM,SAAS,IAAI,CAACJ,cAAc,iBAChDlB,OAAA;gBAAKqE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtE,OAAA;kBAAKqE,SAAS,EAAE,sEACdnE,UAAU,GAAG,cAAc,GAAG,aAAa,EAC1C;kBAAAoE,QAAA,eACDtE,OAAA;oBAAKqE,SAAS,EAAE,WACdnE,UAAU,GAAG,eAAe,GAAG,eAAe,EAC7C;oBAACkF,IAAI,EAAC,MAAM;oBAACK,MAAM,EAAC,cAAc;oBAACJ,OAAO,EAAC,WAAW;oBAAAf,QAAA,eACvDtE,OAAA;sBAAM+F,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACN,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAsH;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1L;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3E,OAAA;kBAAGqE,SAAS,EAAE,gBACZnE,UAAU,GAAG,eAAe,GAAG,eAAe,EAC7C;kBAAAoE,QAAA,EAAC;gBAA4C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpD3E,OAAA;kBAAGqE,SAAS,EAAE,WACZnE,UAAU,GAAG,eAAe,GAAG,eAAe,EAC7C;kBAAAoE,QAAA,EAAC;gBAAmC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CACN,EAGArD,SAAS,iBACRtB,OAAA;gBAAKqE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtE,OAAA;kBAAKqE,SAAS,EAAE,sEACdnE,UAAU,GAAG,cAAc,GAAG,cAAc,EAC3C;kBAAAoE,QAAA,eACDtE,OAAA;oBAAKqE,SAAS,EAAE,wBACdnE,UAAU,GAAG,eAAe,GAAG,eAAe,EAC7C;oBAAC0F,KAAK,EAAC,4BAA4B;oBAACR,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAAAf,QAAA,gBACpEtE,OAAA;sBAAQqE,SAAS,EAAC,YAAY;sBAACiB,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,IAAI;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eACrG3E,OAAA;sBAAMqE,SAAS,EAAC,YAAY;sBAACe,IAAI,EAAC,cAAc;sBAACO,CAAC,EAAC;oBAAiH;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3E,OAAA;kBAAGqE,SAAS,EAAE,gBACZnE,UAAU,GAAG,eAAe,GAAG,eAAe,EAC7C;kBAAAoE,QAAA,EAAC;gBAA6B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACrC3E,OAAA;kBAAGqE,SAAS,EAAE,WACZnE,UAAU,GAAG,eAAe,GAAG,eAAe,EAC7C;kBAAAoE,QAAA,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACN,EAGA3D,eAAe,IAAIQ,WAAW,iBAC7BxB,OAAA;gBAAKqE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtE,OAAA;kBAAKqE,SAAS,EAAE,sEACdnE,UAAU,GAAG,cAAc,GAAG,cAAc,EAC3C;kBAAAoE,QAAA,eACDtE,OAAA;oBAAKqE,SAAS,EAAE,WACdnE,UAAU,GAAG,gBAAgB,GAAG,eAAe,EAC9C;oBAACkF,IAAI,EAAC,MAAM;oBAACK,MAAM,EAAC,cAAc;oBAACJ,OAAO,EAAC,WAAW;oBAAAf,QAAA,eACvDtE,OAAA;sBAAM+F,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACN,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAgB;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3E,OAAA;kBAAIqE,SAAS,EAAE,8BACbnE,UAAU,GAAG,YAAY,GAAG,eAAe,EAC1C;kBAAAoE,QAAA,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/B3E,OAAA;kBAAGqE,SAAS,EAAE,gBACZnE,UAAU,GAAG,eAAe,GAAG,eAAe,EAC7C;kBAAAoE,QAAA,EAAC;gBAAkD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC1D3E,OAAA;kBACEiG,IAAI,EAAEzE,WAAY;kBAClB0E,QAAQ;kBACR7B,SAAS,EAAE,oFACTnE,UAAU,GACN,mBAAmB,GACnB,iCAAiC,EACpC;kBAAAoE,QAAA,gBAEHtE,OAAA;oBAAKqE,SAAS,EAAC,cAAc;oBAACe,IAAI,EAAC,MAAM;oBAACK,MAAM,EAAC,cAAc;oBAACJ,OAAO,EAAC,WAAW;oBAAAf,QAAA,eACjFtE,OAAA;sBAAM+F,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACN,WAAW,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAgE;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpI,CAAC,yBAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,CAACrD,SAAS,IAAI,CAACN,eAAe,iBAC7BhB,OAAA;YAAKqE,SAAS,EAAE,yBACdnE,UAAU,GACN,gEAAgE,GAChE,0DAA0D,EAC7D;YAAAoE,QAAA,gBACDtE,OAAA;cAAIqE,SAAS,EAAE,gDACbnE,UAAU,GAAG,YAAY,GAAG,eAAe,EAC1C;cAAAoE,QAAA,gBACDtE,OAAA;gBAAMqE,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,YAElC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3E,OAAA;cAAIqE,SAAS,EAAE,qBACbnE,UAAU,GAAG,eAAe,GAAG,eAAe,EAC7C;cAAAoE,QAAA,gBACDtE,OAAA;gBAAIqE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BtE,OAAA;kBAAMqE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,uDAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3E,OAAA;gBAAIqE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BtE,OAAA;kBAAMqE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,iDAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3E,OAAA;gBAAIqE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BtE,OAAA;kBAAMqE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,8CAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3E,OAAA;gBAAIqE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BtE,OAAA;kBAAMqE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gDAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACxE,EAAA,CA1cQF,qBAAqB;AAAAkG,EAAA,GAArBlG,qBAAqB;AA4c9B,eAAeA,qBAAqB;;AAIpC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAA,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}