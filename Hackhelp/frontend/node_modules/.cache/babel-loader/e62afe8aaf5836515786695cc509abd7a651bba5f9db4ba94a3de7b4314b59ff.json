{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Hackhelp/frontend/src/Pages/LinkedIn.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LinkedInPostGenerator({\n  isDarkMode = false\n}) {\n  _s();\n  const [currentTab, setCurrentTab] = useState('basic');\n  const [formData, setFormData] = useState({\n    // Basic Info\n    name: \"\",\n    role: \"\",\n    college: \"\",\n    year_of_study: \"\",\n    // Team & Event\n    team_name: \"\",\n    team_members: \"\",\n    hackathon_name: \"\",\n    hackathon_theme: \"\",\n    hackathon_mode: \"\",\n    location: \"\",\n    duration: \"\",\n    // Project Details\n    project_name: \"\",\n    problem_solved: \"\",\n    tech_stack: \"\",\n    features: \"\",\n    innovation: \"\",\n    github_link: \"\",\n    // Results & Reflection\n    result: \"\",\n    learnings: \"\",\n    mentor_or_judges: \"\",\n    gratitude_note: \"\",\n    fun_moments: \"\"\n  });\n  const [rawMarkdown, setRawMarkdown] = useState('');\n  const [isOutputVisible, setIsOutputVisible] = useState(false);\n  const [isErrorVisible, setIsErrorVisible] = useState(false);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isCopied, setIsCopied] = useState(false);\n  const [taskId, setTaskId] = useState(null);\n  const [pollingInterval, setPollingInterval] = useState(null);\n  const boldMap = {};\n  'abcdefghijklmnopqrstuvwxyz'.split('').forEach((c, i) => {\n    boldMap[c] = String.fromCodePoint(0x1d41a + i);\n    boldMap[c.toUpperCase()] = String.fromCodePoint(0x1d400 + i);\n  });\n  '0123456789'.split('').forEach((c, i) => {\n    boldMap[c] = String.fromCodePoint(0x1d7ce + i);\n  });\n  const toBold = str => str.split('').map(c => boldMap[c] || c).join('');\n  useEffect(() => {\n    if (isCopied) {\n      const timer = setTimeout(() => setIsCopied(false), 2000);\n      return () => clearTimeout(timer);\n    }\n  }, [isCopied]);\n\n  // Clean up polling on component unmount\n  useEffect(() => {\n    return () => {\n      if (pollingInterval) clearInterval(pollingInterval);\n    };\n  }, [pollingInterval]);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const tabs = [{\n    id: 'basic',\n    label: 'Basic Info',\n    icon: '👤'\n  }, {\n    id: 'event',\n    label: 'Event & Team',\n    icon: '🎯'\n  }, {\n    id: 'project',\n    label: 'Project',\n    icon: '💡'\n  }, {\n    id: 'results',\n    label: 'Results',\n    icon: '🏆'\n  }];\n  const getFieldsForTab = tabId => {\n    switch (tabId) {\n      case 'basic':\n        return [{\n          key: 'name',\n          label: 'Full Name',\n          placeholder: 'Alex Johnson',\n          type: 'input'\n        }, {\n          key: 'role',\n          label: 'Role/Title',\n          placeholder: 'Software Engineer',\n          type: 'input'\n        }, {\n          key: 'college',\n          label: 'College/University',\n          placeholder: 'Stanford University',\n          type: 'input'\n        }, {\n          key: 'year_of_study',\n          label: 'Year of Study',\n          placeholder: '3',\n          type: 'input'\n        }];\n      case 'event':\n        return [{\n          key: 'hackathon_name',\n          label: 'Hackathon Name',\n          placeholder: 'TechCrunch Disrupt 2025',\n          type: 'input'\n        }, {\n          key: 'hackathon_theme',\n          label: 'Theme/Track',\n          placeholder: 'Fintech & Blockchain',\n          type: 'input'\n        }, {\n          key: 'hackathon_mode',\n          label: 'Mode',\n          placeholder: 'Hybrid',\n          type: 'input'\n        }, {\n          key: 'location',\n          label: 'Location',\n          placeholder: 'San Francisco, CA',\n          type: 'input'\n        }, {\n          key: 'duration',\n          label: 'Duration',\n          placeholder: '36 hours',\n          type: 'input'\n        }, {\n          key: 'team_name',\n          label: 'Team Name',\n          placeholder: 'Code Innovators',\n          type: 'input'\n        }, {\n          key: 'team_members',\n          label: 'Team Members',\n          placeholder: 'Sarah Chen, Mike Rodriguez, Emma Davis',\n          type: 'input'\n        }];\n      case 'project':\n        return [{\n          key: 'project_name',\n          label: 'Project Name',\n          placeholder: 'CryptoWallet Pro',\n          type: 'input'\n        }, {\n          key: 'problem_solved',\n          label: 'Problem Solved',\n          placeholder: 'Simplifying cryptocurrency transactions for everyday users with enhanced security',\n          type: 'textarea'\n        }, {\n          key: 'tech_stack',\n          label: 'Tech Stack',\n          placeholder: 'Next.js, Python, PostgreSQL, Web3.js, AWS',\n          type: 'input'\n        }, {\n          key: 'features',\n          label: 'Key Features',\n          placeholder: 'Multi-currency support, AI fraud detection, simplified UI, hardware wallet integration',\n          type: 'textarea'\n        }, {\n          key: 'innovation',\n          label: 'Innovation/USP',\n          placeholder: 'First-of-its-kind AI-powered transaction security with one-click DeFi integration',\n          type: 'textarea'\n        }, {\n          key: 'github_link',\n          label: 'GitHub/Demo Link',\n          placeholder: 'https://github.com/alexj/cryptowallet-pro',\n          type: 'input'\n        }];\n      case 'results':\n        return [{\n          key: 'result',\n          label: 'Result/Achievement',\n          placeholder: '2nd Place 🥈',\n          type: 'input'\n        }, {\n          key: 'learnings',\n          label: 'Key Learnings',\n          placeholder: 'Blockchain development, team collaboration, user experience design, pitch presentation',\n          type: 'textarea'\n        }, {\n          key: 'mentor_or_judges',\n          label: 'Mentors/Judges',\n          placeholder: 'Lisa Park from Coinbase Ventures',\n          type: 'input'\n        }, {\n          key: 'gratitude_note',\n          label: 'Gratitude Message',\n          placeholder: 'Huge thanks to the organizers, mentors, and our amazing team for this incredible journey!',\n          type: 'textarea'\n        }, {\n          key: 'fun_moments',\n          label: 'Fun Moments',\n          placeholder: 'Pizza-fueled coding marathon at 3 AM with spontaneous karaoke breaks! 🍕🎤',\n          type: 'textarea'\n        }];\n      default:\n        return [];\n    }\n  };\n  const generatePost = () => {\n    setIsOutputVisible(false);\n    setIsErrorVisible(false);\n    setIsLoading(true);\n    const payload = {\n      ...formData,\n      team_members: formData.team_members.split(',').map(m => m.trim()),\n      tech_stack: formData.tech_stack.split(',').map(t => t.trim()),\n      features: formData.features.split(',').map(f => f.trim())\n    };\n    fetch(\"http://localhost:8000/api/startup/generate-linkedin-post/\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify(payload)\n    }).then(response => {\n      if (!response.ok) throw new Error(`HTTP ${response.status}`);\n      return response.json();\n    }).then(data => {\n      if (data.linkedin_post) {\n        const postContent = data.linkedin_post.replace(/\\\\#/g, '#');\n        const formatted = postContent.replace(/^#+ (.*)$/gm, (_, header) => toBold(header)).replace(/\\*\\*(.*?)\\*\\*/g, (_, text) => toBold(text));\n        setRawMarkdown(formatted);\n        setIsOutputVisible(true);\n      } else {\n        throw new Error(\"No post content received\");\n      }\n    }).catch(error => {\n      setErrorMessage(\"❌ Error: \" + error.message);\n      setIsErrorVisible(true);\n    }).finally(() => {\n      setIsLoading(false);\n    });\n  };\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(rawMarkdown).then(() => setIsCopied(true));\n  };\n  const isFormValid = () => {\n    return formData.name && formData.hackathon_name && formData.project_name;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen p-4 transition-colors duration-200 ${isDarkMode ? 'bg-[#111827]' : 'bg-gray-50'}`,\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .theme-primary { color: #621639; }\n        .theme-primary-light { color: #831843; }\n        .theme-accent { background-color: #fce7f3; }\n        .theme-accent-text { color: #831843; }\n        .theme-border { border-color: #621639; }\n        .theme-focus:focus { \n          --tw-ring-color: #621639; \n          border-color: #621639; \n        }\n        .theme-button-light {\n          background-color: #fce7f3;\n          color: #831843;\n        }\n        .theme-button-light:hover {\n          background-color: #621639;\n          color: white;\n        }\n        .theme-button-dark {\n          background-color: #4c1d3b;\n          color: #fce7f3;\n        }\n        .theme-button-dark:hover {\n          background-color: #621639;\n        }\n        .theme-tab-active-light {\n          background-color: white;\n          color: #831843;\n          border-bottom: 2px solid #621639;\n        }\n        .theme-tab-active-dark {\n          background-color: #475569;\n          color: white;\n          border-bottom: 2px solid #621639;\n        }\n        .theme-tab-inactive-light {\n          color: #831843;\n        }\n        .theme-tab-inactive-light:hover {\n          background-color: #f9fafb;\n        }\n        .theme-tab-inactive-dark {\n          color: #d1d5db;\n        }\n        .theme-tab-inactive-dark:hover {\n          background-color: #4b5563;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-5xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: `text-xl font-bold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n          children: \"\\u2728 LinkedIn Post Generator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`,\n          children: \"Create professional hackathon celebration posts in minutes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `rounded-lg shadow-sm border overflow-hidden ${isDarkMode ? 'bg-[#1f2937] border-slate-600' : 'bg-white border-gray-200'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex border-b ${isDarkMode ? 'bg-slate-600 border-slate-500' : 'theme-accent border-gray-200'}`,\n              children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCurrentTab(tab.id),\n                className: `flex-1 px-3 py-2 text-xs font-medium transition-all duration-200 focus:outline-none ${currentTab === tab.id ? isDarkMode ? 'theme-tab-active-dark' : 'theme-tab-active-light' : isDarkMode ? 'theme-tab-inactive-dark' : 'theme-tab-inactive-light'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1\",\n                  children: tab.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this), tab.label]\n              }, tab.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: getFieldsForTab(currentTab).map(field => /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: `block text-xs font-medium mb-1 ${isDarkMode ? 'text-gray-200' : 'theme-accent-text'}`,\n                    children: field.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this), field.type === 'textarea' ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    name: field.key,\n                    value: formData[field.key],\n                    onChange: handleChange,\n                    placeholder: field.placeholder,\n                    rows: 2,\n                    className: `w-full px-3 py-2 text-sm rounded-md transition-colors resize-none theme-focus ${isDarkMode ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: field.key,\n                    value: formData[field.key],\n                    onChange: handleChange,\n                    placeholder: field.placeholder,\n                    className: `w-full px-3 py-2 text-sm rounded-md transition-colors theme-focus ${isDarkMode ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 25\n                  }, this)]\n                }, field.key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: generatePost,\n            disabled: isLoading || !isFormValid(),\n            className: `w-full py-3 px-4 text-sm rounded-lg font-semibold transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 focus:outline-none ${isLoading || !isFormValid() ? 'bg-gray-300 text-gray-500 cursor-not-allowed transform-none' : isDarkMode ? 'theme-button-dark' : 'theme-button-light'}`,\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  className: \"opacity-25\",\n                  cx: \"12\",\n                  cy: \"12\",\n                  r: \"10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  className: \"opacity-75\",\n                  fill: \"currentColor\",\n                  d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this), taskId ? 'Processing...' : 'Generating...']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-1\",\n                children: \"\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), \"Generate Post\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), isErrorVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `mb-4 rounded-md border-l-4 p-3 ${isDarkMode ? 'bg-red-900 border-red-500' : 'bg-red-50 border-red-500'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: `h-4 w-4 mr-2 ${isDarkMode ? 'text-red-400' : 'text-red-400'}`,\n              xmlns: \"http://www.w3.org/2000/svg\",\n              viewBox: \"0 0 20 20\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xs ${isDarkMode ? 'text-red-300' : 'text-red-700'}`,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `rounded-lg shadow-sm border overflow-hidden ${isDarkMode ? 'bg-slate-700 border-slate-600' : 'bg-white border-gray-200'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `px-4 py-3 border-b ${isDarkMode ? 'bg-slate-600 border-slate-500' : 'bg-gradient-to-r from-pink-50 to-purple-50 border-gray-200'}`,\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: `text-sm font-semibold flex items-center ${isDarkMode ? 'text-white' : 'text-gray-800'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1\",\n                  children: \"\\uD83D\\uDCDD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 19\n                }, this), \"Generated Post\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this), isOutputVisible ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `rounded-md p-3 mb-3 border ${isDarkMode ? 'bg-slate-600 border-slate-500' : 'theme-accent border-gray-200'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `whitespace-pre-wrap text-xs leading-relaxed font-mono ${isDarkMode ? 'text-gray-200' : 'theme-accent-text'}`,\n                  children: rawMarkdown\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: copyToClipboard,\n                className: `w-full py-2 px-3 text-sm rounded-md font-medium transition-all duration-200 border focus:outline-none ${isCopied ? 'bg-green-600 text-white border-green-500' : isDarkMode ? 'theme-button-dark border-slate-500' : 'theme-button-light theme-border'}`,\n                children: isCopied ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-1\",\n                    children: \"\\u2705\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 25\n                  }, this), \"Copied!\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-1\",\n                    children: \"\\uD83D\\uDCCB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 25\n                  }, this), \"Copy to Clipboard\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-8 text-center text-gray-400\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl mb-2\",\n                children: \"\\uD83D\\uDCC4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: \"Your generated post will appear here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs mt-1\",\n                children: \"Fill in the form and click generate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `rounded-lg p-4 border ${isDarkMode ? 'bg-gradient-to-br from-slate-700 to-slate-600 border-slate-500' : 'bg-gradient-to-br from-pink-50 to-purple-50 theme-border'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: `text-sm font-semibold mb-2 flex items-center ${isDarkMode ? 'text-white' : 'text-gray-800'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-1\",\n                children: \"\\uD83D\\uDCA1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 17\n              }, this), \"Pro Tips\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: `space-y-1 text-xs ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1 theme-primary\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this), \"Keep your project description concise but impactful\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1 theme-primary\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this), \"Mention specific technologies and innovations\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1 theme-primary\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this), \"Include gratitude to mentors and teammates\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1 theme-primary\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 19\n                }, this), \"Add relevant hashtags for better reach\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n}\n_s(LinkedInPostGenerator, \"HQ1QoiwrCJtABUF0gcSrQi436vM=\");\n_c = LinkedInPostGenerator;\nexport default LinkedInPostGenerator;\n\n// import React, { useState, useEffect } from 'react';\n// import { marked } from 'marked';\n\n// function LinkedInPostGenerator() {\n//   const [formData, setFormData] = useState({\n//     name: \"Mohnish Mythreya\",\n//     role: \"Full Stack Developer\",\n//     college: \"VIT Vellore\",\n//     year_of_study: 2,\n//     team_name: \"HackHelp Squad\",\n//     team_members: \"Aryan, Sneha, Riya\",\n//     hackathon_name: \"InnoVerse 2025\",\n//     hackathon_theme: \"Healthcare Tech + AI\",\n//     hackathon_mode: \"Offline\",\n//     location: \"IIT Bombay\",\n//     duration: \"48 hours\",\n//     project_name: \"HackHelp\",\n//     problem_solved: \"Lack of centralized assistance and support for hackathon participants\",\n//     tech_stack: \"React, FastAPI, Gemini Pro, Docker, Redis, TailwindCSS\",\n//     features: \"LinkedIn post generator, mentor matching, API request tester, team resource hub\",\n//     innovation: \"Microservices-based AI toolchain for end-to-end hackathon support\",\n//     github_link: \"https://github.com/hackhelp-2025\",\n//     result: \"Winner 🏆\",\n//     learnings: \"Microservices, containerization, prompt engineering, real-time communication\",\n//     mentor_or_judges: \"Sundar Rajan from Microsoft AI\",\n//     gratitude_note: \"Heartfelt thanks to mentors, volunteers, and teammates for making it magical!\",\n//     fun_moments: \"Midnight dance break with teammates while debugging bugs 🤣🕺\"\n//   });\n\n//   const boldMap = {};\n//   'abcdefghijklmnopqrstuvwxyz'.split('').forEach((c, i) => {\n//     boldMap[c] = String.fromCodePoint(0x1d41a + i);\n//     boldMap[c.toUpperCase()] = String.fromCodePoint(0x1d400 + i);\n//   });\n//   '0123456789'.split('').forEach((c, i) => {\n//     boldMap[c] = String.fromCodePoint(0x1d7ce + i);\n//   });\n\n//   const toBold = (str) => str.split('').map(c => boldMap[c] || c).join('');\n\n//   const [rawMarkdown, setRawMarkdown] = useState('');\n//   const [isOutputVisible, setIsOutputVisible] = useState(false);\n//   const [isErrorVisible, setIsErrorVisible] = useState(false);\n//   const [errorMessage, setErrorMessage] = useState('');\n//   const [isLoading, setIsLoading] = useState(false);\n//   const [isCopied, setIsCopied] = useState(false);\n//   const [taskId, setTaskId] = useState(null);\n//   const [pollingInterval, setPollingInterval] = useState(null);\n\n//   useEffect(() => {\n//     if (isCopied) {\n//       const timer = setTimeout(() => setIsCopied(false), 2000);\n//       return () => clearTimeout(timer);\n//     }\n//   }, [isCopied]);\n\n//   // Clean up polling on component unmount\n//   useEffect(() => {\n//     return () => {\n//       if (pollingInterval) clearInterval(pollingInterval);\n//     };\n//   }, [pollingInterval]);\n\n//   const handleChange = (e) => {\n//     const { name, value } = e.target;\n//     setFormData(prev => ({ ...prev, [name]: value }));\n//   };\n\n//   const generatePost = () => {\n//     setIsOutputVisible(false);\n//     setIsErrorVisible(false);\n//     setIsLoading(true);\n\n//     const payload = {\n//       ...formData,\n//       team_members: formData.team_members.split(',').map(m => m.trim()),\n//       tech_stack: formData.tech_stack.split(',').map(t => t.trim()),\n//       features: formData.features.split(',').map(f => f.trim())\n//     };\n\n//     fetch(\"http://localhost:8000/api/startup/generate-linkedin-post/\", {\n//       method: \"POST\",\n//       headers: { \"Content-Type\": \"application/json\" },\n//       body: JSON.stringify(payload)\n//     })\n//       .then(response => {\n//         if (!response.ok) throw new Error(`HTTP ${response.status}`);\n//         return response.json();\n//       })\n//       .then(data => {\n//         if (data.linkedin_post) {\n//           const postContent = data.linkedin_post.replace(/\\\\#/g, '#');\n\n//           const formatted = postContent\n//             .replace(/^#+ (.*)$/gm, (_, header) => toBold(header))\n//             .replace(/\\*\\*(.*?)\\*\\*/g, (_, text) => toBold(text));\n\n//           setRawMarkdown(formatted);\n//           setIsOutputVisible(true);\n//         } else {\n//           throw new Error(\"No post content received\");\n//         }\n//       })\n//       .catch(error => {\n//         setErrorMessage(\"❌ Error: \" + error.message);\n//         setIsErrorVisible(true);\n//       })\n//       .finally(() => {\n//         setIsLoading(false);\n//       });\n//   };\n\n//   const copyToClipboard = () => {\n//     navigator.clipboard.writeText(rawMarkdown).then(() => setIsCopied(true));\n//   };\n\n//   return (\n//     <div className=\"min-h-screen bg-gradient-to-b from-white to-gray-100 py-12 px-4 sm:px-6 lg:px-8\">\n//       <div className=\"max-w-4xl mx-auto\">\n//         <div className=\"text-center mb-10\">\n//           <h1 className=\"text-4xl font-bold text-black mb-3\">🚀 LinkedIn Post Generator</h1>\n//           <p className=\"text-gray-600\">Fill out the form below and click generate.</p>\n//         </div>\n\n//         <div className=\"bg-white shadow-lg rounded-xl p-6 mb-8\">\n//           <div className=\"grid grid-cols-1 md:grid-cols-2 gap-5\">\n//             {Object.entries(formData).map(([key, val]) => (\n//               <div key={key} className=\"form-group\">\n//                 <label htmlFor={key} className=\"block text-sm font-medium text-gray-700 mb-1 capitalize\">\n//                   {key.replace(/_/g, ' ')}:\n//                 </label>\n//                 <textarea\n//                   id={key}\n//                   name={key}\n//                   value={val}\n//                   onChange={handleChange}\n//                   rows={key === 'gratitude_note' || key === 'fun_moments' || key === 'problem_solved' ? 2 : 1}\n//                   className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-sm\"\n//                 />\n//               </div>\n//             ))}\n//           </div>\n//         </div>\n\n//         <div className=\"flex flex-col sm:flex-row justify-center gap-4 mb-8\">\n//           <button\n//             className={`px-6 py-3 rounded-md text-white font-medium transition duration-200 ${\n//               isLoading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'\n//             } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center`}\n//             onClick={generatePost}\n//             disabled={isLoading}\n//           >\n//             {isLoading ? (\n//               <>\n//                 <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n//                   <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n//                   <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n//                 </svg>\n//                 {taskId ? 'Processing...' : 'Generating...'}\n//               </>\n//             ) : (\n//               'Generate Post'\n//             )}\n//           </button>\n\n//           {isOutputVisible && (\n//             <button\n//               className={`px-6 py-3 rounded-md font-medium transition duration-200 ${\n//                 isCopied ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'\n//               } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 flex items-center justify-center`}\n//               onClick={copyToClipboard}\n//             >\n//               {isCopied ? '✓ Copied!' : 'Copy to Clipboard'}\n//             </button>\n//           )}\n//         </div>\n\n//         {isErrorVisible && (\n//           <div className=\"bg-red-50 border-l-4 border-red-500 p-4 mb-8 rounded\">\n//             <div className=\"flex\">\n//               <div className=\"flex-shrink-0\">\n//                 <svg className=\"h-5 w-5 text-red-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n//                   <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n//                 </svg>\n//               </div>\n//               <div className=\"ml-3\">\n//                 <p className=\"text-sm text-red-700\">\n//                   {errorMessage}\n//                 </p>\n//               </div>\n//             </div>\n//           </div>\n//         )}\n\n//         {isOutputVisible && (\n//           <div className=\"bg-f3efec rounded-xl shadow-md p-6 mb-8 prose max-w-none\">\n//             <h3 className=\"text-lg font-medium mb-4 text-gray-800\">Generated LinkedIn Post</h3>\n//             <div \n//               className=\"whitespace-pre-wrap text-gray-800 border border-gray-200 rounded-md p-5 bg-white\"\n//               dangerouslySetInnerHTML={{ __html: marked.parse(rawMarkdown) }}\n//             />\n//           </div>\n//         )}\n//       </div>\n//     </div>\n//   );\n// }\n\n// export default LinkedInPostGenerator;\nvar _c;\n$RefreshReg$(_c, \"LinkedInPostGenerator\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "LinkedInPostGenerator", "isDarkMode", "_s", "currentTab", "setCurrentTab", "formData", "setFormData", "name", "role", "college", "year_of_study", "team_name", "team_members", "hackathon_name", "hackathon_theme", "hackathon_mode", "location", "duration", "project_name", "problem_solved", "tech_stack", "features", "innovation", "github_link", "result", "learnings", "mentor_or_judges", "gratitude_note", "fun_moments", "rawMarkdown", "setRawMarkdown", "isOutputVisible", "setIsOutputVisible", "isErrorVisible", "setIsErrorVisible", "errorMessage", "setErrorMessage", "isLoading", "setIsLoading", "isCopied", "setIsCopied", "taskId", "setTaskId", "pollingInterval", "setPollingInterval", "boldMap", "split", "for<PERSON>ach", "c", "i", "String", "fromCodePoint", "toUpperCase", "toBold", "str", "map", "join", "timer", "setTimeout", "clearTimeout", "clearInterval", "handleChange", "e", "value", "target", "prev", "tabs", "id", "label", "icon", "getFieldsForTab", "tabId", "key", "placeholder", "type", "generatePost", "payload", "m", "trim", "t", "f", "fetch", "method", "headers", "body", "JSON", "stringify", "then", "response", "ok", "Error", "status", "json", "data", "linkedin_post", "postContent", "replace", "formatted", "_", "header", "text", "catch", "error", "message", "finally", "copyToClipboard", "navigator", "clipboard", "writeText", "isFormValid", "className", "children", "jsx", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "tab", "onClick", "field", "onChange", "rows", "disabled", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d", "xmlns", "fillRule", "clipRule", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Hackhelp/frontend/src/Pages/LinkedIn.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nfunction LinkedInPostGenerator({ isDarkMode = false }) {\n  const [currentTab, setCurrentTab] = useState('basic');\n  const [formData, setFormData] = useState({\n    // Basic Info\n    name: \"\",\n    role: \"\",\n    college: \"\",\n    year_of_study: \"\",\n    \n    // Team & Event\n    team_name: \"\",\n    team_members: \"\",\n    hackathon_name: \"\",\n    hackathon_theme: \"\",\n    hackathon_mode: \"\",\n    location: \"\",\n    duration: \"\",\n    \n    // Project Details\n    project_name: \"\",\n    problem_solved: \"\",\n    tech_stack: \"\",\n    features: \"\",\n    innovation: \"\",\n    github_link: \"\",\n    \n    // Results & Reflection\n    result: \"\",\n    learnings: \"\",\n    mentor_or_judges: \"\",\n    gratitude_note: \"\",\n    fun_moments: \"\"\n  });\n\n  const [rawMarkdown, setRawMarkdown] = useState('');\n  const [isOutputVisible, setIsOutputVisible] = useState(false);\n  const [isErrorVisible, setIsErrorVisible] = useState(false);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isCopied, setIsCopied] = useState(false);\n  const [taskId, setTaskId] = useState(null);\n  const [pollingInterval, setPollingInterval] = useState(null);\n\n  const boldMap = {};\n  'abcdefghijklmnopqrstuvwxyz'.split('').forEach((c, i) => {\n    boldMap[c] = String.fromCodePoint(0x1d41a + i);\n    boldMap[c.toUpperCase()] = String.fromCodePoint(0x1d400 + i);\n  });\n  '0123456789'.split('').forEach((c, i) => {\n    boldMap[c] = String.fromCodePoint(0x1d7ce + i);\n  });\n\n  const toBold = (str) => str.split('').map(c => boldMap[c] || c).join('');\n\n  useEffect(() => {\n    if (isCopied) {\n      const timer = setTimeout(() => setIsCopied(false), 2000);\n      return () => clearTimeout(timer);\n    }\n  }, [isCopied]);\n\n  // Clean up polling on component unmount\n  useEffect(() => {\n    return () => {\n      if (pollingInterval) clearInterval(pollingInterval);\n    };\n  }, [pollingInterval]);\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const tabs = [\n    { id: 'basic', label: 'Basic Info', icon: '👤' },\n    { id: 'event', label: 'Event & Team', icon: '🎯' },\n    { id: 'project', label: 'Project', icon: '💡' },\n    { id: 'results', label: 'Results', icon: '🏆' }\n  ];\n\n  const getFieldsForTab = (tabId) => {\n    switch (tabId) {\n      case 'basic':\n        return [\n          { key: 'name', label: 'Full Name', placeholder: 'Alex Johnson', type: 'input' },\n          { key: 'role', label: 'Role/Title', placeholder: 'Software Engineer', type: 'input' },\n          { key: 'college', label: 'College/University', placeholder: 'Stanford University', type: 'input' },\n          { key: 'year_of_study', label: 'Year of Study', placeholder: '3', type: 'input' }\n        ];\n      case 'event':\n        return [\n          { key: 'hackathon_name', label: 'Hackathon Name', placeholder: 'TechCrunch Disrupt 2025', type: 'input' },\n          { key: 'hackathon_theme', label: 'Theme/Track', placeholder: 'Fintech & Blockchain', type: 'input' },\n          { key: 'hackathon_mode', label: 'Mode', placeholder: 'Hybrid', type: 'input' },\n          { key: 'location', label: 'Location', placeholder: 'San Francisco, CA', type: 'input' },\n          { key: 'duration', label: 'Duration', placeholder: '36 hours', type: 'input' },\n          { key: 'team_name', label: 'Team Name', placeholder: 'Code Innovators', type: 'input' },\n          { key: 'team_members', label: 'Team Members', placeholder: 'Sarah Chen, Mike Rodriguez, Emma Davis', type: 'input' }\n        ];\n      case 'project':\n        return [\n          { key: 'project_name', label: 'Project Name', placeholder: 'CryptoWallet Pro', type: 'input' },\n          { key: 'problem_solved', label: 'Problem Solved', placeholder: 'Simplifying cryptocurrency transactions for everyday users with enhanced security', type: 'textarea' },\n          { key: 'tech_stack', label: 'Tech Stack', placeholder: 'Next.js, Python, PostgreSQL, Web3.js, AWS', type: 'input' },\n          { key: 'features', label: 'Key Features', placeholder: 'Multi-currency support, AI fraud detection, simplified UI, hardware wallet integration', type: 'textarea' },\n          { key: 'innovation', label: 'Innovation/USP', placeholder: 'First-of-its-kind AI-powered transaction security with one-click DeFi integration', type: 'textarea' },\n          { key: 'github_link', label: 'GitHub/Demo Link', placeholder: 'https://github.com/alexj/cryptowallet-pro', type: 'input' }\n        ];\n      case 'results':\n        return [\n          { key: 'result', label: 'Result/Achievement', placeholder: '2nd Place 🥈', type: 'input' },\n          { key: 'learnings', label: 'Key Learnings', placeholder: 'Blockchain development, team collaboration, user experience design, pitch presentation', type: 'textarea' },\n          { key: 'mentor_or_judges', label: 'Mentors/Judges', placeholder: 'Lisa Park from Coinbase Ventures', type: 'input' },\n          { key: 'gratitude_note', label: 'Gratitude Message', placeholder: 'Huge thanks to the organizers, mentors, and our amazing team for this incredible journey!', type: 'textarea' },\n          { key: 'fun_moments', label: 'Fun Moments', placeholder: 'Pizza-fueled coding marathon at 3 AM with spontaneous karaoke breaks! 🍕🎤', type: 'textarea' }\n        ];\n      default:\n        return [];\n    }\n  };\n\n  const generatePost = () => {\n    setIsOutputVisible(false);\n    setIsErrorVisible(false);\n    setIsLoading(true);\n\n    const payload = {\n      ...formData,\n      team_members: formData.team_members.split(',').map(m => m.trim()),\n      tech_stack: formData.tech_stack.split(',').map(t => t.trim()),\n      features: formData.features.split(',').map(f => f.trim())\n    };\n\n    fetch(\"http://localhost:8000/api/startup/generate-linkedin-post/\", {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\" },\n      body: JSON.stringify(payload)\n    })\n      .then(response => {\n        if (!response.ok) throw new Error(`HTTP ${response.status}`);\n        return response.json();\n      })\n      .then(data => {\n        if (data.linkedin_post) {\n          const postContent = data.linkedin_post.replace(/\\\\#/g, '#');\n          \n          const formatted = postContent\n            .replace(/^#+ (.*)$/gm, (_, header) => toBold(header))\n            .replace(/\\*\\*(.*?)\\*\\*/g, (_, text) => toBold(text));\n          \n          setRawMarkdown(formatted);\n          setIsOutputVisible(true);\n        } else {\n          throw new Error(\"No post content received\");\n        }\n      })\n      .catch(error => {\n        setErrorMessage(\"❌ Error: \" + error.message);\n        setIsErrorVisible(true);\n      })\n      .finally(() => {\n        setIsLoading(false);\n      });\n  };\n\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(rawMarkdown).then(() => setIsCopied(true));\n  };\n\n  const isFormValid = () => {\n    return formData.name && formData.hackathon_name && formData.project_name;\n  };\n\n  return (\n    <div className={`min-h-screen p-4 transition-colors duration-200 ${\n      isDarkMode ? 'bg-[#111827]' : 'bg-gray-50'\n    }`}>\n      {/* Custom CSS for theme colors */}\n      <style jsx>{`\n        .theme-primary { color: #621639; }\n        .theme-primary-light { color: #831843; }\n        .theme-accent { background-color: #fce7f3; }\n        .theme-accent-text { color: #831843; }\n        .theme-border { border-color: #621639; }\n        .theme-focus:focus { \n          --tw-ring-color: #621639; \n          border-color: #621639; \n        }\n        .theme-button-light {\n          background-color: #fce7f3;\n          color: #831843;\n        }\n        .theme-button-light:hover {\n          background-color: #621639;\n          color: white;\n        }\n        .theme-button-dark {\n          background-color: #4c1d3b;\n          color: #fce7f3;\n        }\n        .theme-button-dark:hover {\n          background-color: #621639;\n        }\n        .theme-tab-active-light {\n          background-color: white;\n          color: #831843;\n          border-bottom: 2px solid #621639;\n        }\n        .theme-tab-active-dark {\n          background-color: #475569;\n          color: white;\n          border-bottom: 2px solid #621639;\n        }\n        .theme-tab-inactive-light {\n          color: #831843;\n        }\n        .theme-tab-inactive-light:hover {\n          background-color: #f9fafb;\n        }\n        .theme-tab-inactive-dark {\n          color: #d1d5db;\n        }\n        .theme-tab-inactive-dark:hover {\n          background-color: #4b5563;\n        }\n      `}</style>\n\n      <div className=\"max-w-5xl mx-auto\">\n        {/* Header */}\n        <div className=\"text-center mb-6\">\n          <h1 className={`text-xl font-bold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>\n            ✨ LinkedIn Post Generator\n          </h1>\n          <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n            Create professional hackathon celebration posts in minutes\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-6\">\n          {/* Form Section */}\n          <div className=\"space-y-4\">\n            {/* Tab Navigation */}\n            <div className={`rounded-lg shadow-sm border overflow-hidden ${\n              isDarkMode \n                ? 'bg-[#1f2937] border-slate-600' \n                : 'bg-white border-gray-200'\n            }`}>\n              <div className={`flex border-b ${\n                isDarkMode \n                  ? 'bg-slate-600 border-slate-500' \n                  : 'theme-accent border-gray-200'\n              }`}>\n                {tabs.map((tab) => (\n                  <button\n                    key={tab.id}\n                    onClick={() => setCurrentTab(tab.id)}\n                    className={`flex-1 px-3 py-2 text-xs font-medium transition-all duration-200 focus:outline-none ${\n                      currentTab === tab.id\n                        ? isDarkMode ? 'theme-tab-active-dark' : 'theme-tab-active-light'\n                        : isDarkMode ? 'theme-tab-inactive-dark' : 'theme-tab-inactive-light'\n                    }`}\n                  >\n                    <span className=\"mr-1\">{tab.icon}</span>\n                    {tab.label}\n                  </button>\n                ))}\n              </div>\n\n              {/* Form Content */}\n              <div className=\"p-4\">\n                <div className=\"space-y-3\">\n                  {getFieldsForTab(currentTab).map((field) => (\n                    <div key={field.key}>\n                      <label className={`block text-xs font-medium mb-1 ${\n                        isDarkMode ? 'text-gray-200' : 'theme-accent-text'\n                      }`}>\n                        {field.label}\n                      </label>\n                      {field.type === 'textarea' ? (\n                        <textarea\n                          name={field.key}\n                          value={formData[field.key]}\n                          onChange={handleChange}\n                          placeholder={field.placeholder}\n                          rows={2}\n                          className={`w-full px-3 py-2 text-sm rounded-md transition-colors resize-none theme-focus ${\n                            isDarkMode \n                              ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' \n                              : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'\n                          }`}\n                        />\n                      ) : (\n                        <input\n                          type=\"text\"\n                          name={field.key}\n                          value={formData[field.key]}\n                          onChange={handleChange}\n                          placeholder={field.placeholder}\n                          className={`w-full px-3 py-2 text-sm rounded-md transition-colors theme-focus ${\n                            isDarkMode \n                              ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' \n                              : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'\n                          }`}\n                        />\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Action Button */}\n            <button\n              onClick={generatePost}\n              disabled={isLoading || !isFormValid()}\n              className={`w-full py-3 px-4 text-sm rounded-lg font-semibold transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 focus:outline-none ${\n                isLoading || !isFormValid()\n                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed transform-none'\n                  : isDarkMode\n                    ? 'theme-button-dark'\n                    : 'theme-button-light'\n              }`}\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center\">\n                  <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  {taskId ? 'Processing...' : 'Generating...'}\n                </div>\n              ) : (\n                <div className=\"flex items-center justify-center\">\n                  <span className=\"mr-1\">🚀</span>\n                  Generate Post\n                </div>\n              )}\n            </button>\n          </div>\n\n          {/* Error Message */}\n          {isErrorVisible && (\n            <div className={`mb-4 rounded-md border-l-4 p-3 ${\n              isDarkMode \n                ? 'bg-red-900 border-red-500' \n                : 'bg-red-50 border-red-500'\n            }`}>\n              <div className=\"flex items-center\">\n                <svg className={`h-4 w-4 mr-2 ${\n                  isDarkMode ? 'text-red-400' : 'text-red-400'\n                }`} xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n                <p className={`text-xs ${\n                  isDarkMode ? 'text-red-300' : 'text-red-700'\n                }`}>\n                  {errorMessage}\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Output Section */}\n          <div className=\"space-y-4\">\n            {/* Preview Card */}\n            <div className={`rounded-lg shadow-sm border overflow-hidden ${\n              isDarkMode \n                ? 'bg-slate-700 border-slate-600' \n                : 'bg-white border-gray-200'\n            }`}>\n              <div className={`px-4 py-3 border-b ${\n                isDarkMode \n                  ? 'bg-slate-600 border-slate-500' \n                  : 'bg-gradient-to-r from-pink-50 to-purple-50 border-gray-200'\n              }`}>\n                <h3 className={`text-sm font-semibold flex items-center ${\n                  isDarkMode ? 'text-white' : 'text-gray-800'\n                }`}>\n                  <span className=\"mr-1\">📝</span>\n                  Generated Post\n                </h3>\n              </div>\n\n              {isOutputVisible ? (\n                <div className=\"p-4\">\n                  <div className={`rounded-md p-3 mb-3 border ${\n                    isDarkMode \n                      ? 'bg-slate-600 border-slate-500' \n                      : 'theme-accent border-gray-200'\n                  }`}>\n                    <div className={`whitespace-pre-wrap text-xs leading-relaxed font-mono ${\n                      isDarkMode ? 'text-gray-200' : 'theme-accent-text'\n                    }`}>\n                      {rawMarkdown}\n                    </div>\n                  </div>\n                  \n                  <button\n                    onClick={copyToClipboard}\n                    className={`w-full py-2 px-3 text-sm rounded-md font-medium transition-all duration-200 border focus:outline-none ${\n                      isCopied\n                        ? 'bg-green-600 text-white border-green-500'\n                        : isDarkMode\n                          ? 'theme-button-dark border-slate-500'\n                          : 'theme-button-light theme-border'\n                    }`}\n                  >\n                    {isCopied ? (\n                      <div className=\"flex items-center justify-center\">\n                        <span className=\"mr-1\">✅</span>\n                        Copied!\n                      </div>\n                    ) : (\n                      <div className=\"flex items-center justify-center\">\n                        <span className=\"mr-1\">📋</span>\n                        Copy to Clipboard\n                      </div>\n                    )}\n                  </button>\n                </div>\n              ) : (\n                <div className=\"p-8 text-center text-gray-400\">\n                  <div className=\"text-2xl mb-2\">📄</div>\n                  <p className=\"text-sm\">Your generated post will appear here</p>\n                  <p className=\"text-xs mt-1\">Fill in the form and click generate</p>\n                </div>\n              )}\n            </div>\n\n            {/* Tips Card */}\n            <div className={`rounded-lg p-4 border ${\n              isDarkMode \n                ? 'bg-gradient-to-br from-slate-700 to-slate-600 border-slate-500' \n                : 'bg-gradient-to-br from-pink-50 to-purple-50 theme-border'\n            }`}>\n              <h4 className={`text-sm font-semibold mb-2 flex items-center ${\n                isDarkMode ? 'text-white' : 'text-gray-800'\n              }`}>\n                <span className=\"mr-1\">💡</span>\n                Pro Tips\n              </h4>\n              <ul className={`space-y-1 text-xs ${\n                isDarkMode ? 'text-gray-300' : 'text-gray-600'\n              }`}>\n                <li className=\"flex items-start\">\n                  <span className=\"mr-1 theme-primary\">•</span>\n                  Keep your project description concise but impactful\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"mr-1 theme-primary\">•</span>\n                  Mention specific technologies and innovations\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"mr-1 theme-primary\">•</span>\n                  Include gratitude to mentors and teammates\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"mr-1 theme-primary\">•</span>\n                  Add relevant hashtags for better reach\n                </li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default LinkedInPostGenerator;\n\n\n// import React, { useState, useEffect } from 'react';\n// import { marked } from 'marked';\n\n// function LinkedInPostGenerator() {\n//   const [formData, setFormData] = useState({\n//     name: \"Mohnish Mythreya\",\n//     role: \"Full Stack Developer\",\n//     college: \"VIT Vellore\",\n//     year_of_study: 2,\n//     team_name: \"HackHelp Squad\",\n//     team_members: \"Aryan, Sneha, Riya\",\n//     hackathon_name: \"InnoVerse 2025\",\n//     hackathon_theme: \"Healthcare Tech + AI\",\n//     hackathon_mode: \"Offline\",\n//     location: \"IIT Bombay\",\n//     duration: \"48 hours\",\n//     project_name: \"HackHelp\",\n//     problem_solved: \"Lack of centralized assistance and support for hackathon participants\",\n//     tech_stack: \"React, FastAPI, Gemini Pro, Docker, Redis, TailwindCSS\",\n//     features: \"LinkedIn post generator, mentor matching, API request tester, team resource hub\",\n//     innovation: \"Microservices-based AI toolchain for end-to-end hackathon support\",\n//     github_link: \"https://github.com/hackhelp-2025\",\n//     result: \"Winner 🏆\",\n//     learnings: \"Microservices, containerization, prompt engineering, real-time communication\",\n//     mentor_or_judges: \"Sundar Rajan from Microsoft AI\",\n//     gratitude_note: \"Heartfelt thanks to mentors, volunteers, and teammates for making it magical!\",\n//     fun_moments: \"Midnight dance break with teammates while debugging bugs 🤣🕺\"\n//   });\n\n//   const boldMap = {};\n//   'abcdefghijklmnopqrstuvwxyz'.split('').forEach((c, i) => {\n//     boldMap[c] = String.fromCodePoint(0x1d41a + i);\n//     boldMap[c.toUpperCase()] = String.fromCodePoint(0x1d400 + i);\n//   });\n//   '0123456789'.split('').forEach((c, i) => {\n//     boldMap[c] = String.fromCodePoint(0x1d7ce + i);\n//   });\n\n//   const toBold = (str) => str.split('').map(c => boldMap[c] || c).join('');\n\n//   const [rawMarkdown, setRawMarkdown] = useState('');\n//   const [isOutputVisible, setIsOutputVisible] = useState(false);\n//   const [isErrorVisible, setIsErrorVisible] = useState(false);\n//   const [errorMessage, setErrorMessage] = useState('');\n//   const [isLoading, setIsLoading] = useState(false);\n//   const [isCopied, setIsCopied] = useState(false);\n//   const [taskId, setTaskId] = useState(null);\n//   const [pollingInterval, setPollingInterval] = useState(null);\n\n//   useEffect(() => {\n//     if (isCopied) {\n//       const timer = setTimeout(() => setIsCopied(false), 2000);\n//       return () => clearTimeout(timer);\n//     }\n//   }, [isCopied]);\n\n//   // Clean up polling on component unmount\n//   useEffect(() => {\n//     return () => {\n//       if (pollingInterval) clearInterval(pollingInterval);\n//     };\n//   }, [pollingInterval]);\n\n//   const handleChange = (e) => {\n//     const { name, value } = e.target;\n//     setFormData(prev => ({ ...prev, [name]: value }));\n//   };\n\n//   const generatePost = () => {\n//     setIsOutputVisible(false);\n//     setIsErrorVisible(false);\n//     setIsLoading(true);\n\n//     const payload = {\n//       ...formData,\n//       team_members: formData.team_members.split(',').map(m => m.trim()),\n//       tech_stack: formData.tech_stack.split(',').map(t => t.trim()),\n//       features: formData.features.split(',').map(f => f.trim())\n//     };\n\n//     fetch(\"http://localhost:8000/api/startup/generate-linkedin-post/\", {\n//       method: \"POST\",\n//       headers: { \"Content-Type\": \"application/json\" },\n//       body: JSON.stringify(payload)\n//     })\n//       .then(response => {\n//         if (!response.ok) throw new Error(`HTTP ${response.status}`);\n//         return response.json();\n//       })\n//       .then(data => {\n//         if (data.linkedin_post) {\n//           const postContent = data.linkedin_post.replace(/\\\\#/g, '#');\n          \n//           const formatted = postContent\n//             .replace(/^#+ (.*)$/gm, (_, header) => toBold(header))\n//             .replace(/\\*\\*(.*?)\\*\\*/g, (_, text) => toBold(text));\n          \n//           setRawMarkdown(formatted);\n//           setIsOutputVisible(true);\n//         } else {\n//           throw new Error(\"No post content received\");\n//         }\n//       })\n//       .catch(error => {\n//         setErrorMessage(\"❌ Error: \" + error.message);\n//         setIsErrorVisible(true);\n//       })\n//       .finally(() => {\n//         setIsLoading(false);\n//       });\n//   };\n\n//   const copyToClipboard = () => {\n//     navigator.clipboard.writeText(rawMarkdown).then(() => setIsCopied(true));\n//   };\n\n//   return (\n//     <div className=\"min-h-screen bg-gradient-to-b from-white to-gray-100 py-12 px-4 sm:px-6 lg:px-8\">\n//       <div className=\"max-w-4xl mx-auto\">\n//         <div className=\"text-center mb-10\">\n//           <h1 className=\"text-4xl font-bold text-black mb-3\">🚀 LinkedIn Post Generator</h1>\n//           <p className=\"text-gray-600\">Fill out the form below and click generate.</p>\n//         </div>\n\n//         <div className=\"bg-white shadow-lg rounded-xl p-6 mb-8\">\n//           <div className=\"grid grid-cols-1 md:grid-cols-2 gap-5\">\n//             {Object.entries(formData).map(([key, val]) => (\n//               <div key={key} className=\"form-group\">\n//                 <label htmlFor={key} className=\"block text-sm font-medium text-gray-700 mb-1 capitalize\">\n//                   {key.replace(/_/g, ' ')}:\n//                 </label>\n//                 <textarea\n//                   id={key}\n//                   name={key}\n//                   value={val}\n//                   onChange={handleChange}\n//                   rows={key === 'gratitude_note' || key === 'fun_moments' || key === 'problem_solved' ? 2 : 1}\n//                   className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-sm\"\n//                 />\n//               </div>\n//             ))}\n//           </div>\n//         </div>\n\n//         <div className=\"flex flex-col sm:flex-row justify-center gap-4 mb-8\">\n//           <button\n//             className={`px-6 py-3 rounded-md text-white font-medium transition duration-200 ${\n//               isLoading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'\n//             } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center`}\n//             onClick={generatePost}\n//             disabled={isLoading}\n//           >\n//             {isLoading ? (\n//               <>\n//                 <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n//                   <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n//                   <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n//                 </svg>\n//                 {taskId ? 'Processing...' : 'Generating...'}\n//               </>\n//             ) : (\n//               'Generate Post'\n//             )}\n//           </button>\n\n//           {isOutputVisible && (\n//             <button\n//               className={`px-6 py-3 rounded-md font-medium transition duration-200 ${\n//                 isCopied ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'\n//               } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 flex items-center justify-center`}\n//               onClick={copyToClipboard}\n//             >\n//               {isCopied ? '✓ Copied!' : 'Copy to Clipboard'}\n//             </button>\n//           )}\n//         </div>\n\n//         {isErrorVisible && (\n//           <div className=\"bg-red-50 border-l-4 border-red-500 p-4 mb-8 rounded\">\n//             <div className=\"flex\">\n//               <div className=\"flex-shrink-0\">\n//                 <svg className=\"h-5 w-5 text-red-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n//                   <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n//                 </svg>\n//               </div>\n//               <div className=\"ml-3\">\n//                 <p className=\"text-sm text-red-700\">\n//                   {errorMessage}\n//                 </p>\n//               </div>\n//             </div>\n//           </div>\n//         )}\n\n//         {isOutputVisible && (\n//           <div className=\"bg-f3efec rounded-xl shadow-md p-6 mb-8 prose max-w-none\">\n//             <h3 className=\"text-lg font-medium mb-4 text-gray-800\">Generated LinkedIn Post</h3>\n//             <div \n//               className=\"whitespace-pre-wrap text-gray-800 border border-gray-200 rounded-md p-5 bg-white\"\n//               dangerouslySetInnerHTML={{ __html: marked.parse(rawMarkdown) }}\n//             />\n//           </div>\n//         )}\n//       </div>\n//     </div>\n//   );\n// }\n\n// export default LinkedInPostGenerator;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,qBAAqBA,CAAC;EAAEC,UAAU,GAAG;AAAM,CAAC,EAAE;EAAAC,EAAA;EACrD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,OAAO,CAAC;EACrD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvC;IACAW,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IAEjB;IACAC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IAEZ;IACAC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IAEf;IACAC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAMiD,OAAO,GAAG,CAAC,CAAC;EAClB,4BAA4B,CAACC,KAAK,CAAC,EAAE,CAAC,CAACC,OAAO,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACvDJ,OAAO,CAACG,CAAC,CAAC,GAAGE,MAAM,CAACC,aAAa,CAAC,OAAO,GAAGF,CAAC,CAAC;IAC9CJ,OAAO,CAACG,CAAC,CAACI,WAAW,CAAC,CAAC,CAAC,GAAGF,MAAM,CAACC,aAAa,CAAC,OAAO,GAAGF,CAAC,CAAC;EAC9D,CAAC,CAAC;EACF,YAAY,CAACH,KAAK,CAAC,EAAE,CAAC,CAACC,OAAO,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACvCJ,OAAO,CAACG,CAAC,CAAC,GAAGE,MAAM,CAACC,aAAa,CAAC,OAAO,GAAGF,CAAC,CAAC;EAChD,CAAC,CAAC;EAEF,MAAMI,MAAM,GAAIC,GAAG,IAAKA,GAAG,CAACR,KAAK,CAAC,EAAE,CAAC,CAACS,GAAG,CAACP,CAAC,IAAIH,OAAO,CAACG,CAAC,CAAC,IAAIA,CAAC,CAAC,CAACQ,IAAI,CAAC,EAAE,CAAC;EAExE3D,SAAS,CAAC,MAAM;IACd,IAAI0C,QAAQ,EAAE;MACZ,MAAMkB,KAAK,GAAGC,UAAU,CAAC,MAAMlB,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MACxD,OAAO,MAAMmB,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAClB,QAAQ,CAAC,CAAC;;EAEd;EACA1C,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAI8C,eAAe,EAAEiB,aAAa,CAACjB,eAAe,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMkB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEvD,IAAI;MAAEwD;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC1D,WAAW,CAAC2D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC1D,IAAI,GAAGwD;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMG,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAK,CAAC,EAChD;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAK,CAAC,EAClD;IAAEF,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC/C;IAAEF,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAK,CAAC,CAChD;EAED,MAAMC,eAAe,GAAIC,KAAK,IAAK;IACjC,QAAQA,KAAK;MACX,KAAK,OAAO;QACV,OAAO,CACL;UAAEC,GAAG,EAAE,MAAM;UAAEJ,KAAK,EAAE,WAAW;UAAEK,WAAW,EAAE,cAAc;UAAEC,IAAI,EAAE;QAAQ,CAAC,EAC/E;UAAEF,GAAG,EAAE,MAAM;UAAEJ,KAAK,EAAE,YAAY;UAAEK,WAAW,EAAE,mBAAmB;UAAEC,IAAI,EAAE;QAAQ,CAAC,EACrF;UAAEF,GAAG,EAAE,SAAS;UAAEJ,KAAK,EAAE,oBAAoB;UAAEK,WAAW,EAAE,qBAAqB;UAAEC,IAAI,EAAE;QAAQ,CAAC,EAClG;UAAEF,GAAG,EAAE,eAAe;UAAEJ,KAAK,EAAE,eAAe;UAAEK,WAAW,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAQ,CAAC,CAClF;MACH,KAAK,OAAO;QACV,OAAO,CACL;UAAEF,GAAG,EAAE,gBAAgB;UAAEJ,KAAK,EAAE,gBAAgB;UAAEK,WAAW,EAAE,yBAAyB;UAAEC,IAAI,EAAE;QAAQ,CAAC,EACzG;UAAEF,GAAG,EAAE,iBAAiB;UAAEJ,KAAK,EAAE,aAAa;UAAEK,WAAW,EAAE,sBAAsB;UAAEC,IAAI,EAAE;QAAQ,CAAC,EACpG;UAAEF,GAAG,EAAE,gBAAgB;UAAEJ,KAAK,EAAE,MAAM;UAAEK,WAAW,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAQ,CAAC,EAC9E;UAAEF,GAAG,EAAE,UAAU;UAAEJ,KAAK,EAAE,UAAU;UAAEK,WAAW,EAAE,mBAAmB;UAAEC,IAAI,EAAE;QAAQ,CAAC,EACvF;UAAEF,GAAG,EAAE,UAAU;UAAEJ,KAAK,EAAE,UAAU;UAAEK,WAAW,EAAE,UAAU;UAAEC,IAAI,EAAE;QAAQ,CAAC,EAC9E;UAAEF,GAAG,EAAE,WAAW;UAAEJ,KAAK,EAAE,WAAW;UAAEK,WAAW,EAAE,iBAAiB;UAAEC,IAAI,EAAE;QAAQ,CAAC,EACvF;UAAEF,GAAG,EAAE,cAAc;UAAEJ,KAAK,EAAE,cAAc;UAAEK,WAAW,EAAE,wCAAwC;UAAEC,IAAI,EAAE;QAAQ,CAAC,CACrH;MACH,KAAK,SAAS;QACZ,OAAO,CACL;UAAEF,GAAG,EAAE,cAAc;UAAEJ,KAAK,EAAE,cAAc;UAAEK,WAAW,EAAE,kBAAkB;UAAEC,IAAI,EAAE;QAAQ,CAAC,EAC9F;UAAEF,GAAG,EAAE,gBAAgB;UAAEJ,KAAK,EAAE,gBAAgB;UAAEK,WAAW,EAAE,mFAAmF;UAAEC,IAAI,EAAE;QAAW,CAAC,EACtK;UAAEF,GAAG,EAAE,YAAY;UAAEJ,KAAK,EAAE,YAAY;UAAEK,WAAW,EAAE,2CAA2C;UAAEC,IAAI,EAAE;QAAQ,CAAC,EACnH;UAAEF,GAAG,EAAE,UAAU;UAAEJ,KAAK,EAAE,cAAc;UAAEK,WAAW,EAAE,wFAAwF;UAAEC,IAAI,EAAE;QAAW,CAAC,EACnK;UAAEF,GAAG,EAAE,YAAY;UAAEJ,KAAK,EAAE,gBAAgB;UAAEK,WAAW,EAAE,mFAAmF;UAAEC,IAAI,EAAE;QAAW,CAAC,EAClK;UAAEF,GAAG,EAAE,aAAa;UAAEJ,KAAK,EAAE,kBAAkB;UAAEK,WAAW,EAAE,2CAA2C;UAAEC,IAAI,EAAE;QAAQ,CAAC,CAC3H;MACH,KAAK,SAAS;QACZ,OAAO,CACL;UAAEF,GAAG,EAAE,QAAQ;UAAEJ,KAAK,EAAE,oBAAoB;UAAEK,WAAW,EAAE,cAAc;UAAEC,IAAI,EAAE;QAAQ,CAAC,EAC1F;UAAEF,GAAG,EAAE,WAAW;UAAEJ,KAAK,EAAE,eAAe;UAAEK,WAAW,EAAE,wFAAwF;UAAEC,IAAI,EAAE;QAAW,CAAC,EACrK;UAAEF,GAAG,EAAE,kBAAkB;UAAEJ,KAAK,EAAE,gBAAgB;UAAEK,WAAW,EAAE,kCAAkC;UAAEC,IAAI,EAAE;QAAQ,CAAC,EACpH;UAAEF,GAAG,EAAE,gBAAgB;UAAEJ,KAAK,EAAE,mBAAmB;UAAEK,WAAW,EAAE,2FAA2F;UAAEC,IAAI,EAAE;QAAW,CAAC,EACjL;UAAEF,GAAG,EAAE,aAAa;UAAEJ,KAAK,EAAE,aAAa;UAAEK,WAAW,EAAE,4EAA4E;UAAEC,IAAI,EAAE;QAAW,CAAC,CAC1J;MACH;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB3C,kBAAkB,CAAC,KAAK,CAAC;IACzBE,iBAAiB,CAAC,KAAK,CAAC;IACxBI,YAAY,CAAC,IAAI,CAAC;IAElB,MAAMsC,OAAO,GAAG;MACd,GAAGvE,QAAQ;MACXO,YAAY,EAAEP,QAAQ,CAACO,YAAY,CAACkC,KAAK,CAAC,GAAG,CAAC,CAACS,GAAG,CAACsB,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;MACjE1D,UAAU,EAAEf,QAAQ,CAACe,UAAU,CAAC0B,KAAK,CAAC,GAAG,CAAC,CAACS,GAAG,CAACwB,CAAC,IAAIA,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC;MAC7DzD,QAAQ,EAAEhB,QAAQ,CAACgB,QAAQ,CAACyB,KAAK,CAAC,GAAG,CAAC,CAACS,GAAG,CAACyB,CAAC,IAAIA,CAAC,CAACF,IAAI,CAAC,CAAC;IAC1D,CAAC;IAEDG,KAAK,CAAC,2DAA2D,EAAE;MACjEC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACV,OAAO;IAC9B,CAAC,CAAC,CACCW,IAAI,CAACC,QAAQ,IAAI;MAChB,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,QAAQF,QAAQ,CAACG,MAAM,EAAE,CAAC;MAC5D,OAAOH,QAAQ,CAACI,IAAI,CAAC,CAAC;IACxB,CAAC,CAAC,CACDL,IAAI,CAACM,IAAI,IAAI;MACZ,IAAIA,IAAI,CAACC,aAAa,EAAE;QACtB,MAAMC,WAAW,GAAGF,IAAI,CAACC,aAAa,CAACE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;QAE3D,MAAMC,SAAS,GAAGF,WAAW,CAC1BC,OAAO,CAAC,aAAa,EAAE,CAACE,CAAC,EAAEC,MAAM,KAAK9C,MAAM,CAAC8C,MAAM,CAAC,CAAC,CACrDH,OAAO,CAAC,gBAAgB,EAAE,CAACE,CAAC,EAAEE,IAAI,KAAK/C,MAAM,CAAC+C,IAAI,CAAC,CAAC;QAEvDtE,cAAc,CAACmE,SAAS,CAAC;QACzBjE,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC,MAAM;QACL,MAAM,IAAI0D,KAAK,CAAC,0BAA0B,CAAC;MAC7C;IACF,CAAC,CAAC,CACDW,KAAK,CAACC,KAAK,IAAI;MACdlE,eAAe,CAAC,WAAW,GAAGkE,KAAK,CAACC,OAAO,CAAC;MAC5CrE,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,CACDsE,OAAO,CAAC,MAAM;MACblE,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACN,CAAC;EAED,MAAMmE,eAAe,GAAGA,CAAA,KAAM;IAC5BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC/E,WAAW,CAAC,CAAC0D,IAAI,CAAC,MAAM/C,WAAW,CAAC,IAAI,CAAC,CAAC;EAC1E,CAAC;EAED,MAAMqE,WAAW,GAAGA,CAAA,KAAM;IACxB,OAAOxG,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACQ,cAAc,IAAIR,QAAQ,CAACa,YAAY;EAC1E,CAAC;EAED,oBACEnB,OAAA;IAAK+G,SAAS,EAAE,mDACd7G,UAAU,GAAG,cAAc,GAAG,YAAY,EACzC;IAAA8G,QAAA,gBAEDhH,OAAA;MAAOiH,GAAG;MAAAD,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEVrH,OAAA;MAAK+G,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhChH,OAAA;QAAK+G,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhH,OAAA;UAAI+G,SAAS,EAAE,0BAA0B7G,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;UAAA8G,QAAA,EAAC;QAExF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrH,OAAA;UAAG+G,SAAS,EAAE,WAAW7G,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;UAAA8G,QAAA,EAAC;QAE3E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENrH,OAAA;QAAK+G,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAExChH,OAAA;UAAK+G,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBhH,OAAA;YAAK+G,SAAS,EAAE,+CACd7G,UAAU,GACN,+BAA+B,GAC/B,0BAA0B,EAC7B;YAAA8G,QAAA,gBACDhH,OAAA;cAAK+G,SAAS,EAAE,iBACd7G,UAAU,GACN,+BAA+B,GAC/B,8BAA8B,EACjC;cAAA8G,QAAA,EACA7C,IAAI,CAACX,GAAG,CAAE8D,GAAG,iBACZtH,OAAA;gBAEEuH,OAAO,EAAEA,CAAA,KAAMlH,aAAa,CAACiH,GAAG,CAAClD,EAAE,CAAE;gBACrC2C,SAAS,EAAE,uFACT3G,UAAU,KAAKkH,GAAG,CAAClD,EAAE,GACjBlE,UAAU,GAAG,uBAAuB,GAAG,wBAAwB,GAC/DA,UAAU,GAAG,yBAAyB,GAAG,0BAA0B,EACtE;gBAAA8G,QAAA,gBAEHhH,OAAA;kBAAM+G,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEM,GAAG,CAAChD;gBAAI;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACvCC,GAAG,CAACjD,KAAK;cAAA,GATLiD,GAAG,CAAClD,EAAE;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUL,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNrH,OAAA;cAAK+G,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClBhH,OAAA;gBAAK+G,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBzC,eAAe,CAACnE,UAAU,CAAC,CAACoD,GAAG,CAAEgE,KAAK,iBACrCxH,OAAA;kBAAAgH,QAAA,gBACEhH,OAAA;oBAAO+G,SAAS,EAAE,kCAChB7G,UAAU,GAAG,eAAe,GAAG,mBAAmB,EACjD;oBAAA8G,QAAA,EACAQ,KAAK,CAACnD;kBAAK;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,EACPG,KAAK,CAAC7C,IAAI,KAAK,UAAU,gBACxB3E,OAAA;oBACEQ,IAAI,EAAEgH,KAAK,CAAC/C,GAAI;oBAChBT,KAAK,EAAE1D,QAAQ,CAACkH,KAAK,CAAC/C,GAAG,CAAE;oBAC3BgD,QAAQ,EAAE3D,YAAa;oBACvBY,WAAW,EAAE8C,KAAK,CAAC9C,WAAY;oBAC/BgD,IAAI,EAAE,CAAE;oBACRX,SAAS,EAAE,iFACT7G,UAAU,GACN,+DAA+D,GAC/D,iEAAiE;kBACpE;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,gBAEFrH,OAAA;oBACE2E,IAAI,EAAC,MAAM;oBACXnE,IAAI,EAAEgH,KAAK,CAAC/C,GAAI;oBAChBT,KAAK,EAAE1D,QAAQ,CAACkH,KAAK,CAAC/C,GAAG,CAAE;oBAC3BgD,QAAQ,EAAE3D,YAAa;oBACvBY,WAAW,EAAE8C,KAAK,CAAC9C,WAAY;oBAC/BqC,SAAS,EAAE,qEACT7G,UAAU,GACN,+DAA+D,GAC/D,iEAAiE;kBACpE;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACF;gBAAA,GAhCOG,KAAK,CAAC/C,GAAG;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCd,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrH,OAAA;YACEuH,OAAO,EAAE3C,YAAa;YACtB+C,QAAQ,EAAErF,SAAS,IAAI,CAACwE,WAAW,CAAC,CAAE;YACtCC,SAAS,EAAE,+JACTzE,SAAS,IAAI,CAACwE,WAAW,CAAC,CAAC,GACvB,6DAA6D,GAC7D5G,UAAU,GACR,mBAAmB,GACnB,oBAAoB,EACzB;YAAA8G,QAAA,EAEF1E,SAAS,gBACRtC,OAAA;cAAK+G,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/ChH,OAAA;gBAAK+G,SAAS,EAAC,iCAAiC;gBAACa,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAAAb,QAAA,gBAC9EhH,OAAA;kBAAQ+G,SAAS,EAAC,YAAY;kBAACe,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACrGrH,OAAA;kBAAM+G,SAAS,EAAC,YAAY;kBAACa,IAAI,EAAC,cAAc;kBAACO,CAAC,EAAC;gBAAiH;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzK,CAAC,EACL3E,MAAM,GAAG,eAAe,GAAG,eAAe;YAAA;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,gBAENrH,OAAA;cAAK+G,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/ChH,OAAA;gBAAM+G,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,iBAElC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLnF,cAAc,iBACblC,OAAA;UAAK+G,SAAS,EAAE,kCACd7G,UAAU,GACN,2BAA2B,GAC3B,0BAA0B,EAC7B;UAAA8G,QAAA,eACDhH,OAAA;YAAK+G,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChChH,OAAA;cAAK+G,SAAS,EAAE,gBACd7G,UAAU,GAAG,cAAc,GAAG,cAAc,EAC3C;cAACkI,KAAK,EAAC,4BAA4B;cAACP,OAAO,EAAC,WAAW;cAACD,IAAI,EAAC,cAAc;cAAAZ,QAAA,eAC5EhH,OAAA;gBAAMqI,QAAQ,EAAC,SAAS;gBAACF,CAAC,EAAC,yNAAyN;gBAACG,QAAQ,EAAC;cAAS;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvQ,CAAC,eACNrH,OAAA;cAAG+G,SAAS,EAAE,WACZ7G,UAAU,GAAG,cAAc,GAAG,cAAc,EAC3C;cAAA8G,QAAA,EACA5E;YAAY;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDrH,OAAA;UAAK+G,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBhH,OAAA;YAAK+G,SAAS,EAAE,+CACd7G,UAAU,GACN,+BAA+B,GAC/B,0BAA0B,EAC7B;YAAA8G,QAAA,gBACDhH,OAAA;cAAK+G,SAAS,EAAE,sBACd7G,UAAU,GACN,+BAA+B,GAC/B,4DAA4D,EAC/D;cAAA8G,QAAA,eACDhH,OAAA;gBAAI+G,SAAS,EAAE,2CACb7G,UAAU,GAAG,YAAY,GAAG,eAAe,EAC1C;gBAAA8G,QAAA,gBACDhH,OAAA;kBAAM+G,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,kBAElC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EAELrF,eAAe,gBACdhC,OAAA;cAAK+G,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBhH,OAAA;gBAAK+G,SAAS,EAAE,8BACd7G,UAAU,GACN,+BAA+B,GAC/B,8BAA8B,EACjC;gBAAA8G,QAAA,eACDhH,OAAA;kBAAK+G,SAAS,EAAE,yDACd7G,UAAU,GAAG,eAAe,GAAG,mBAAmB,EACjD;kBAAA8G,QAAA,EACAlF;gBAAW;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrH,OAAA;gBACEuH,OAAO,EAAEb,eAAgB;gBACzBK,SAAS,EAAE,yGACTvE,QAAQ,GACJ,0CAA0C,GAC1CtC,UAAU,GACR,oCAAoC,GACpC,iCAAiC,EACtC;gBAAA8G,QAAA,EAEFxE,QAAQ,gBACPxC,OAAA;kBAAK+G,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/ChH,OAAA;oBAAM+G,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,WAEjC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,gBAENrH,OAAA;kBAAK+G,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/ChH,OAAA;oBAAM+G,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,qBAElC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENrH,OAAA;cAAK+G,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC5ChH,OAAA;gBAAK+G,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCrH,OAAA;gBAAG+G,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAoC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC/DrH,OAAA;gBAAG+G,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNrH,OAAA;YAAK+G,SAAS,EAAE,yBACd7G,UAAU,GACN,gEAAgE,GAChE,0DAA0D,EAC7D;YAAA8G,QAAA,gBACDhH,OAAA;cAAI+G,SAAS,EAAE,gDACb7G,UAAU,GAAG,YAAY,GAAG,eAAe,EAC1C;cAAA8G,QAAA,gBACDhH,OAAA;gBAAM+G,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,YAElC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrH,OAAA;cAAI+G,SAAS,EAAE,qBACb7G,UAAU,GAAG,eAAe,GAAG,eAAe,EAC7C;cAAA8G,QAAA,gBACDhH,OAAA;gBAAI+G,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BhH,OAAA;kBAAM+G,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,uDAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrH,OAAA;gBAAI+G,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BhH,OAAA;kBAAM+G,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,iDAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrH,OAAA;gBAAI+G,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BhH,OAAA;kBAAM+G,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,8CAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrH,OAAA;gBAAI+G,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC9BhH,OAAA;kBAAM+G,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,0CAE/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClH,EAAA,CAndQF,qBAAqB;AAAAsI,EAAA,GAArBtI,qBAAqB;AAqd9B,eAAeA,qBAAqB;;AAGpC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAA,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}