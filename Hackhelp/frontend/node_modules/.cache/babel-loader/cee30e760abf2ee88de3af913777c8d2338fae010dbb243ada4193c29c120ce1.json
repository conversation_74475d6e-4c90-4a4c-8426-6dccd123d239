{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Hackhelp/frontend/src/Page_Components/Home/Sample2.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { ChevronRight, Code, Smartphone, Brain, Shield, Globe, Heart, GraduationCap, Leaf, Users, Lightbulb, Trophy, Zap, Clock, Target, Presentation, Award, Settings, User, Calendar, FileText, CheckCircle, Star, Search, Bell, Moon, Sun, MoreHorizontal, Grid3X3, Home, Inbox, MessageCircle, ListTodo, BarChart3, Timer, Goal, UserPlus, Plus, Circle, CheckCircle2 } from 'lucide-react';\nimport LinkedInPostGenerator from '../../Pages/LinkedIn';\nimport PowerPointerGenerator from '../../Pages/PowerPointer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HackathonDashboard = () => {\n  _s();\n  const [selectedStage, setSelectedStage] = useState('Development Phase');\n  const [selectedCategory, setSelectedCategory] = useState('Web Development');\n  const [selectedSubcategory, setSelectedSubcategory] = useState('Frontend Frameworks');\n  const [hoveredIcon, setHoveredIcon] = useState(null);\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [activeLeftPanel, setActiveLeftPanel] = useState('home');\n  const [sidebarWidth, setSidebarWidth] = useState(288); // 72 * 4 = 288px (w-72)\n  const [isResizing, setIsResizing] = useState(false);\n  const [isLeader, setIsLeader] = useState(true);\n  const [selectedMember, setSelectedMember] = useState('All Members');\n  const [chatMessages, setChatMessages] = useState([{\n    id: 1,\n    sender: 'Sarah Wilson',\n    avatar: 'SW',\n    color: 'bg-green-500',\n    message: \"Hey team! Just finished setting up the database schema. Ready to move on to the API endpoints.\",\n    timestamp: new Date(Date.now() - 1000 * 60 * 15),\n    // 15 minutes ago\n    type: 'message'\n  }, {\n    id: 2,\n    sender: 'Mike Chen',\n    avatar: 'MC',\n    color: 'bg-purple-500',\n    message: \"Great work Sarah! I'm working on the authentication system. Should have it ready by end of day.\",\n    timestamp: new Date(Date.now() - 1000 * 60 * 10),\n    // 10 minutes ago\n    type: 'message'\n  }, {\n    id: 3,\n    sender: 'John Doe',\n    avatar: 'JD',\n    color: 'bg-blue-500',\n    message: \"UI components are looking good. Added the new design system tokens. Check it out when you get a chance!\",\n    timestamp: new Date(Date.now() - 1000 * 60 * 8),\n    // 8 minutes ago\n    type: 'message'\n  }, {\n    id: 4,\n    sender: 'Emma Davis',\n    avatar: 'ED',\n    color: 'bg-pink-500',\n    message: \"📝 Just uploaded the API documentation to the shared drive\",\n    timestamp: new Date(Date.now() - 1000 * 60 * 5),\n    // 5 minutes ago\n    type: 'message'\n  }, {\n    id: 5,\n    sender: 'Alex Johnson',\n    avatar: 'AJ',\n    color: 'bg-orange-500',\n    message: \"Mobile testing is complete ✅ Everything looks responsive. Great job everyone!\",\n    timestamp: new Date(Date.now() - 1000 * 60 * 2),\n    // 2 minutes ago\n    type: 'message'\n  }]);\n  const [newMessage, setNewMessage] = useState('');\n  const resizeRef = useRef(null);\n  const sidebarRef = useRef(null);\n  const addMessage = () => {\n    if (newMessage.trim()) {\n      const message = {\n        id: chatMessages.length + 1,\n        sender: 'John Doe',\n        // Current user\n        avatar: 'JD',\n        color: 'bg-blue-500',\n        message: newMessage,\n        timestamp: new Date(),\n        type: 'message'\n      };\n      setChatMessages([...chatMessages, message]);\n      setNewMessage('');\n    }\n  };\n  const formatTime = timestamp => {\n    const now = new Date();\n    const diffMs = now - timestamp;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n    if (diffMins < 1) return 'now';\n    if (diffMins < 60) return `${diffMins}m ago`;\n    if (diffHours < 24) return `${diffHours}h ago`;\n    if (diffDays < 7) return `${diffDays}d ago`;\n    return timestamp.toLocaleDateString();\n  };\n\n  // Sample team members data\n  const teamMembers = [{\n    id: 1,\n    name: 'John Doe',\n    avatar: 'JD',\n    color: 'bg-blue-500'\n  }, {\n    id: 2,\n    name: 'Sarah Wilson',\n    avatar: 'SW',\n    color: 'bg-green-500'\n  }, {\n    id: 3,\n    name: 'Mike Chen',\n    avatar: 'MC',\n    color: 'bg-purple-500'\n  }, {\n    id: 4,\n    name: 'Emma Davis',\n    avatar: 'ED',\n    color: 'bg-pink-500'\n  }, {\n    id: 5,\n    name: 'Alex Johnson',\n    avatar: 'AJ',\n    color: 'bg-orange-500'\n  }];\n\n  // Sample tasks data\n  const allTasks = [{\n    id: 1,\n    title: 'Design UI Components',\n    assignee: 'John Doe',\n    status: 'in-progress',\n    priority: 'high',\n    dueDate: '2025-05-27',\n    description: 'Create reusable UI components for the dashboard'\n  }, {\n    id: 2,\n    title: 'Setup Database Schema',\n    assignee: 'Sarah Wilson',\n    status: 'completed',\n    priority: 'high',\n    dueDate: '2025-05-25',\n    description: 'Design and implement the database structure'\n  }, {\n    id: 3,\n    title: 'Implement Authentication',\n    assignee: 'Mike Chen',\n    status: 'todo',\n    priority: 'medium',\n    dueDate: '2025-05-28',\n    description: 'Add user login and registration functionality'\n  }, {\n    id: 4,\n    title: 'Write API Documentation',\n    assignee: 'Emma Davis',\n    status: 'in-progress',\n    priority: 'low',\n    dueDate: '2025-05-30',\n    description: 'Document all API endpoints and usage'\n  }, {\n    id: 5,\n    title: 'Test Mobile Responsiveness',\n    assignee: 'Alex Johnson',\n    status: 'completed',\n    priority: 'medium',\n    dueDate: '2025-05-24',\n    description: 'Ensure the app works well on mobile devices'\n  }, {\n    id: 6,\n    title: 'Deploy to Staging',\n    assignee: 'John Doe',\n    status: 'todo',\n    priority: 'high',\n    dueDate: '2025-05-29',\n    description: 'Deploy the application to staging environment'\n  }];\n\n  // Filter tasks based on role and selected member\n  const getFilteredTasks = () => {\n    if (!isLeader) {\n      // If member, show only their tasks (assuming current user is John Doe)\n      return allTasks.filter(task => task.assignee === 'John Doe');\n    }\n    if (selectedMember === 'All Members') {\n      return allTasks;\n    }\n    return allTasks.filter(task => task.assignee === selectedMember);\n  };\n\n  // Color themes for different stages\n  const stageThemes = {\n    'Pre-Hackathon': {\n      primary: '#2b216a',\n      // Purple\n      primaryLight: '#3730a3',\n      primaryDark: '#1e1065',\n      accent: '#e0e7ff',\n      accentText: '#3730a3'\n    },\n    'Development Phase': {\n      primary: '#0d9488',\n      // Teal (default)\n      primaryLight: '#14b8a6',\n      primaryDark: '#0f766e',\n      accent: '#ccfbf1',\n      accentText: '#0f766e'\n    },\n    'Final Preparation': {\n      primary: '#53195d',\n      // Violet\n      primaryLight: '#7c2d92',\n      primaryDark: '#4a1454',\n      accent: '#f3e8ff',\n      accentText: '#7c2d92'\n    },\n    'Judging & Awards': {\n      primary: '#621639',\n      // Pink\n      primaryLight: '#831843',\n      primaryDark: '#4c1d3b',\n      accent: '#fce7f3',\n      accentText: '#831843'\n    }\n  };\n  const currentTheme = stageThemes[selectedStage];\n  const stages = {\n    'Pre-Hackathon': {\n      icon: /*#__PURE__*/_jsxDEV(Settings, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 13\n      }, this),\n      color: 'bg-gray-500',\n      bgGradient: 'from-gray-400 to-gray-600',\n      categories: {\n        'Registration': {\n          icon: /*#__PURE__*/_jsxDEV(User, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 17\n          }, this),\n          subcategories: {\n            'Team Formation': {\n              description: 'Build your dream team with complementary skills and shared vision.',\n              topics: ['Individual Registration - Platform signup and profile creation', 'Skill Assessment - Evaluate technical and soft skills', 'Role Assignment - Define team member responsibilities', 'Team Dynamics - Establish communication and workflow'],\n              techStack: ['Communication Tools', 'Project Management', 'Git', 'Slack', 'Discord'],\n              difficulty: 'Beginner'\n            }\n          }\n        },\n        'Planning': {\n          icon: /*#__PURE__*/_jsxDEV(Calendar, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 17\n          }, this),\n          subcategories: {\n            'Strategy Development': {\n              description: 'Research and plan your approach before the hackathon begins.',\n              topics: ['Problem Analysis - Understanding challenge requirements', 'Solution Research - Investigating existing approaches', 'Technology Selection - Choosing optimal tech stack', 'Timeline Planning - Breaking down development phases'],\n              techStack: ['Research Tools', 'Mind Mapping', 'Documentation', 'Planning Software'],\n              difficulty: 'Intermediate'\n            }\n          }\n        }\n      }\n    },\n    'Development Phase': {\n      icon: /*#__PURE__*/_jsxDEV(Code, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 13\n      }, this),\n      color: 'bg-blue-500',\n      bgGradient: 'from-blue-400 to-blue-600',\n      categories: {\n        'Web Development': {\n          icon: /*#__PURE__*/_jsxDEV(Globe, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 17\n          }, this),\n          subcategories: {\n            'Frontend Frameworks': {\n              description: 'Build modern, responsive web applications using cutting-edge frontend technologies.',\n              topics: ['React Applications - Component-based UI development', 'Vue.js Projects - Progressive framework solutions', 'Angular Solutions - Enterprise-grade applications', 'Svelte Apps - Compile-time optimized frameworks'],\n              techStack: ['JavaScript', 'TypeScript', 'HTML5', 'CSS3', 'Webpack'],\n              difficulty: 'Intermediate'\n            },\n            'Backend Development': {\n              description: 'Create robust server-side applications and APIs that power modern web services.',\n              topics: ['Node.js APIs - Scalable JavaScript backends', 'Python Backends - Django/Flask applications', 'Java Services - Spring Boot microservices', 'Go Applications - High-performance web servers'],\n              techStack: ['Node.js', 'Python', 'Java', 'PostgreSQL', 'MongoDB'],\n              difficulty: 'Advanced'\n            }\n          }\n        },\n        'Mobile Development': {\n          icon: /*#__PURE__*/_jsxDEV(Smartphone, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this),\n          subcategories: {\n            'Native iOS': {\n              description: 'Develop high-performance iOS applications using Swift and native frameworks.',\n              topics: ['Swift Applications - Native iOS development', 'SwiftUI Interfaces - Declarative UI framework', 'Core Data Integration - Local data persistence', 'iOS Widgets - Home screen extensions'],\n              techStack: ['Swift', 'SwiftUI', 'Xcode', 'Core Data', 'UIKit'],\n              difficulty: 'Intermediate'\n            },\n            'Cross-Platform': {\n              description: 'Create applications that run on multiple platforms with shared codebases.',\n              topics: ['React Native Apps - JavaScript-based mobile development', 'Flutter Applications - Dart-based cross-platform framework', 'Xamarin Solutions - C# cross-platform development', 'Ionic Apps - Web-based mobile applications'],\n              techStack: ['React Native', 'Flutter', 'Dart', 'JavaScript', 'Cordova'],\n              difficulty: 'Advanced'\n            }\n          }\n        },\n        'AI & Machine Learning': {\n          icon: /*#__PURE__*/_jsxDEV(Brain, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 17\n          }, this),\n          subcategories: {\n            'Natural Language Processing': {\n              description: 'Develop intelligent systems that understand and process human language.',\n              topics: ['Text Analysis Tools - Sentiment and emotion analysis', 'Language Translation - Real-time translation services', 'Chatbots & Virtual Assistants - Conversational AI systems', 'Content Generation - AI-powered writing tools'],\n              techStack: ['Python', 'TensorFlow', 'spaCy', 'NLTK', 'Transformers'],\n              difficulty: 'Advanced'\n            },\n            'Computer Vision': {\n              description: 'Build systems that can interpret and understand visual information.',\n              topics: ['Image Recognition - Object and pattern detection', 'Facial Recognition - Identity verification systems', 'Medical Imaging - Healthcare diagnostic tools', 'Augmented Reality - Real-world overlay applications'],\n              techStack: ['OpenCV', 'TensorFlow', 'PyTorch', 'YOLO', 'MediaPipe'],\n              difficulty: 'Expert'\n            }\n          }\n        }\n      }\n    },\n    'Final Preparation': {\n      icon: /*#__PURE__*/_jsxDEV(FileText, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 13\n      }, this),\n      color: 'bg-orange-500',\n      bgGradient: 'from-orange-400 to-orange-600',\n      categories: {\n        'Documentation': {\n          icon: /*#__PURE__*/_jsxDEV(FileText, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 17\n          }, this),\n          subcategories: {\n            'Project Documentation': {\n              description: 'Create comprehensive documentation for your hackathon project.',\n              topics: ['README Creation - Project overview and setup instructions', 'API Documentation - Endpoint descriptions and examples', 'User Guides - Step-by-step usage instructions', 'Technical Architecture - System design documentation'],\n              techStack: ['Markdown', 'GitBook', 'Swagger', 'Docusaurus', 'Notion'],\n              difficulty: 'Intermediate'\n            }\n          }\n        }\n      }\n    },\n    'Judging & Awards': {\n      icon: /*#__PURE__*/_jsxDEV(Award, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 13\n      }, this),\n      color: 'bg-purple-500',\n      bgGradient: 'from-purple-400 to-purple-600',\n      categories: {\n        'Presentation': {\n          icon: /*#__PURE__*/_jsxDEV(Presentation, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 17\n          }, this),\n          subcategories: {\n            'Live Pitching': {\n              description: 'Present your solution to judges and showcase your innovation.',\n              topics: ['Pitch Delivery - Confident presentation techniques', 'Demo Execution - Flawless product demonstration', 'Judge Interaction - Professional Q&A handling', 'Time Management - Effective use of presentation time'],\n              techStack: ['Presentation Tools', 'Screen Sharing', 'Video Conferencing', 'Demo Environments'],\n              difficulty: 'Advanced'\n            },\n            'PowerPoint Presentation': {\n              \"component\": /*#__PURE__*/_jsxDEV(PowerPointerGenerator, {\n                isDarkMode: isDarkMode\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 27\n              }, this)\n            }\n          }\n        },\n        'Post Hackathon': {\n          icon: /*#__PURE__*/_jsxDEV(Presentation, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 17\n          }, this),\n          subcategories: {\n            'LinkedIn Post': {\n              \"component\": /*#__PURE__*/_jsxDEV(LinkedInPostGenerator, {\n                isDarkMode: isDarkMode\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 27\n              }, this)\n            }\n          }\n        }\n      }\n    }\n  };\n  const currentStage = stages[selectedStage];\n  const currentCategory = currentStage === null || currentStage === void 0 ? void 0 : currentStage.categories[selectedCategory];\n  const currentSubcategory = currentCategory === null || currentCategory === void 0 ? void 0 : currentCategory.subcategories[selectedSubcategory];\n\n  // Resize functionality\n  useEffect(() => {\n    const handleMouseMove = e => {\n      if (!isResizing) return;\n      const newWidth = e.clientX - 56; // 56px is the width of the leftmost sidebar (w-14)\n      const minWidth = 200;\n      const maxWidth = 500;\n      if (newWidth >= minWidth && newWidth <= maxWidth) {\n        setSidebarWidth(newWidth);\n      }\n    };\n    const handleMouseUp = () => {\n      setIsResizing(false);\n      document.body.style.cursor = 'default';\n      document.body.style.userSelect = 'auto';\n    };\n    if (isResizing) {\n      document.body.style.cursor = 'col-resize';\n      document.body.style.userSelect = 'none';\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n    }\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n  }, [isResizing]);\n  const handleResizeStart = e => {\n    e.preventDefault();\n    setIsResizing(true);\n  };\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'Beginner':\n        return 'bg-green-100 text-green-800';\n      case 'Intermediate':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'Advanced':\n        return 'bg-orange-100 text-orange-800';\n      case 'Expert':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const handleStageChange = stageName => {\n    setSelectedStage(stageName);\n    setActiveLeftPanel('home'); // Auto-navigate to home when stage changes\n    const firstCategory = Object.keys(stages[stageName].categories)[0];\n    setSelectedCategory(firstCategory);\n    const firstSubcategory = Object.keys(stages[stageName].categories[firstCategory].subcategories)[0];\n    setSelectedSubcategory(firstSubcategory);\n  };\n  const handleCategoryChange = categoryName => {\n    setSelectedCategory(categoryName);\n    const firstSubcategory = Object.keys(currentStage.categories[categoryName].subcategories)[0];\n    setSelectedSubcategory(firstSubcategory);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `h-screen flex flex-col overflow-hidden ${isDarkMode ? 'bg-gray-900' : 'bg-white'} transition-all duration-300 font-sans`,\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .custom-scrollbar {\n          scrollbar-width: none;\n          -ms-overflow-style: none;\n        }\n        .custom-scrollbar::-webkit-scrollbar {\n          display: none;\n        }\n        .resize-handle {\n          position: absolute;\n          top: 0;\n          right: 0;\n          width: 4px;\n          height: 100%;\n          background: transparent;\n          cursor: col-resize;\n          z-index: 10;\n        }\n        .resize-handle:hover {\n          background: rgba(59, 130, 246, 0.5);\n        }\n        .resize-handle.resizing {\n          background: rgba(59, 130, 246, 0.8);\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${isDarkMode ? 'border-gray-700' : 'border-b'} px-6 py-3 shadow-sm flex-shrink-0`,\n      style: {\n        backgroundColor: isDarkMode ? '#1f2937' : currentTheme.primary,\n        borderColor: isDarkMode ? '#374151' : currentTheme.primaryDark\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-8 h-8 ${isDarkMode ? 'bg-gray-700' : 'bg-white'} rounded-lg flex items-center justify-center`,\n            children: /*#__PURE__*/_jsxDEV(Trophy, {\n              className: `w-5 h-5`,\n              style: {\n                color: isDarkMode ? '#9ca3af' : currentTheme.primary\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-white'}`,\n            children: \"HackHelp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: `text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-white'}`,\n              children: \"Hackathon Project Guide\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              className: `w-5 h-5 ${isDarkMode ? 'text-gray-400' : 'text-white/70'} absolute left-3 top-1/2 transform -translate-y-1/2`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search everything...\",\n              className: `pl-10 pr-4 py-2 rounded-lg border text-sm w-64 focus:outline-none focus:ring-2 ${isDarkMode ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-gray-500 focus:ring-gray-500/20' : 'border-white/20 text-white placeholder-white/70 focus:border-white focus:ring-white/20'}`,\n              style: {\n                backgroundColor: isDarkMode ? '#374151' : `${currentTheme.primary}dd`,\n                borderColor: isDarkMode ? '#4b5563' : 'rgba(255,255,255,0.2)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `absolute right-3 top-1/2 transform -translate-y-1/2 text-xs ${isDarkMode ? 'text-gray-500' : 'text-white/60'}`,\n              children: \"Ctrl + K\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowNotifications(!showNotifications),\n              className: `p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' : 'text-white/80 hover:text-white'}`,\n              onMouseEnter: e => {\n                if (!isDarkMode) {\n                  e.target.style.backgroundColor = currentTheme.primaryDark;\n                }\n              },\n              onMouseLeave: e => {\n                if (!isDarkMode) {\n                  e.target.style.backgroundColor = 'transparent';\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Bell, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this), showNotifications && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `absolute right-0 top-full mt-2 w-80 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg shadow-lg border z-50`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`,\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: `font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                  children: \"Notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                    children: \"New hackathon starting soon!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                    children: \"AI/ML Challenge 2025 registration open\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                    children: \"Team invitation received\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                    children: \"You've been invited to join \\\"Code Warriors\\\"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsDarkMode(!isDarkMode),\n            className: `p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' : 'text-white/80 hover:text-white'}`,\n            onMouseEnter: e => {\n              if (!isDarkMode) {\n                e.target.style.backgroundColor = currentTheme.primaryDark;\n              }\n            },\n            onMouseLeave: e => {\n              if (!isDarkMode) {\n                e.target.style.backgroundColor = 'transparent';\n              }\n            },\n            children: isDarkMode ? /*#__PURE__*/_jsxDEV(Sun, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 29\n            }, this) : /*#__PURE__*/_jsxDEV(Moon, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' : 'text-white/80 hover:text-white'}`,\n            onMouseEnter: e => {\n              if (!isDarkMode) {\n                e.target.style.backgroundColor = currentTheme.primaryDark;\n              }\n            },\n            onMouseLeave: e => {\n              if (!isDarkMode) {\n                e.target.style.backgroundColor = 'transparent';\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid3X3, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' : 'text-white/80 hover:text-white'}`,\n            onMouseEnter: e => {\n              if (!isDarkMode) {\n                e.target.style.backgroundColor = currentTheme.primaryDark;\n              }\n            },\n            onMouseLeave: e => {\n              if (!isDarkMode) {\n                e.target.style.backgroundColor = 'transparent';\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Settings, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm font-medium\",\n                children: \"PY\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-1 overflow-hidden \",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-14 border-r flex flex-col items-center py-3 space-y-1 flex-shrink-0 m-2 rounded-xl`,\n        style: {\n          backgroundColor: isDarkMode ? '#111827' : currentTheme.primaryDark,\n          borderColor: isDarkMode ? '#374151' : currentTheme.primaryDark\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-8 h-8 ${isDarkMode ? 'bg-gray-700' : 'bg-white'} rounded-lg flex items-center justify-center mb-3`,\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-bold text-sm\",\n            style: {\n              color: isDarkMode ? '#9ca3af' : currentTheme.primary\n            },\n            children: \"H\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveLeftPanel('home'),\n              onMouseEnter: () => setHoveredIcon('sidebar-home'),\n              onMouseLeave: () => setHoveredIcon(null),\n              className: `w-10 h-10 flex flex-col items-center justify-center transition-colors ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${activeLeftPanel === 'home' ? isDarkMode ? 'bg-white/20' : 'bg-white' : 'hover:bg-white/10'}`,\n                children: /*#__PURE__*/_jsxDEV(Home, {\n                  className: \"w-4 h-4\",\n                  style: activeLeftPanel === 'home' && !isDarkMode ? {\n                    color: currentTheme.primary\n                  } : {}\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xs mt-0.5 ${activeLeftPanel === 'home' ? 'font-semibold' : ''}`,\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this), hoveredIcon === 'sidebar-home' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\",\n              children: [\"Home\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveLeftPanel('inbox'),\n              onMouseEnter: () => setHoveredIcon('sidebar-inbox'),\n              onMouseLeave: () => setHoveredIcon(null),\n              className: `w-10 h-10 flex flex-col items-center justify-center transition-colors ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${activeLeftPanel === 'inbox' ? isDarkMode ? 'bg-white/20' : 'bg-white' : 'hover:bg-white/10'}`,\n                children: /*#__PURE__*/_jsxDEV(Inbox, {\n                  className: \"w-4 h-4\",\n                  style: activeLeftPanel === 'inbox' && !isDarkMode ? {\n                    color: currentTheme.primary\n                  } : {}\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xs mt-0.5 ${activeLeftPanel === 'inbox' ? 'font-semibold' : ''}`,\n                children: \"Inbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this), hoveredIcon === 'sidebar-inbox' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\",\n              children: [\"Inbox\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveLeftPanel('calendar'),\n              onMouseEnter: () => setHoveredIcon('sidebar-calendar'),\n              onMouseLeave: () => setHoveredIcon(null),\n              className: `w-10 h-10 flex flex-col items-center justify-center transition-colors ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${activeLeftPanel === 'calendar' ? isDarkMode ? 'bg-white/20' : 'bg-white' : 'hover:bg-white/10'}`,\n                children: /*#__PURE__*/_jsxDEV(Calendar, {\n                  className: \"w-4 h-4\",\n                  style: activeLeftPanel === 'calendar' && !isDarkMode ? {\n                    color: currentTheme.primary\n                  } : {}\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xs mt-0.5 ${activeLeftPanel === 'calendar' ? 'font-semibold' : ''}`,\n                children: \"Calendar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 15\n            }, this), hoveredIcon === 'sidebar-calendar' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\",\n              children: [\"Calendar\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveLeftPanel('chat'),\n              onMouseEnter: () => setHoveredIcon('sidebar-brain'),\n              onMouseLeave: () => setHoveredIcon(null),\n              className: `w-10 h-10 flex flex-col items-center justify-center transition-colors relative ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${activeLeftPanel === 'chat' ? isDarkMode ? 'bg-white/20' : 'bg-white' : 'hover:bg-white/10'}`,\n                children: /*#__PURE__*/_jsxDEV(Brain, {\n                  className: \"w-4 h-4\",\n                  style: activeLeftPanel === 'chat' && !isDarkMode ? {\n                    color: currentTheme.primary\n                  } : {}\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xs mt-0.5 ${activeLeftPanel === 'chat' ? 'font-semibold' : ''}`,\n                children: \"Brain\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-0 right-0 w-3 h-3 bg-green-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 15\n            }, this), hoveredIcon === 'sidebar-brain' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\",\n              children: [\"Brain (AI Chat)\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveLeftPanel('teamchat'),\n              onMouseEnter: () => setHoveredIcon('sidebar-teamchat'),\n              onMouseLeave: () => setHoveredIcon(null),\n              className: `w-10 h-10 flex flex-col items-center justify-center transition-colors ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-lg flex items-center justify-center transition-colors relative ${activeLeftPanel === 'teamchat' ? isDarkMode ? 'bg-white/20' : 'bg-white' : 'hover:bg-white/10'}`,\n                children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n                  className: \"w-4 h-4\",\n                  style: activeLeftPanel === 'teamchat' && !isDarkMode ? {\n                    color: currentTheme.primary\n                  } : {}\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-xs font-bold\",\n                    children: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xs mt-0.5 ${activeLeftPanel === 'teamchat' ? 'font-semibold' : ''}`,\n                children: \"Chat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 834,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 15\n            }, this), hoveredIcon === 'sidebar-teamchat' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\",\n              children: [\"Team Chat\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveLeftPanel('tasks'),\n              onMouseEnter: () => setHoveredIcon('sidebar-tasks'),\n              onMouseLeave: () => setHoveredIcon(null),\n              className: `w-10 h-10 flex flex-col items-center justify-center transition-colors ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${activeLeftPanel === 'tasks' ? isDarkMode ? 'bg-white/20' : 'bg-white' : 'hover:bg-white/10'}`,\n                children: /*#__PURE__*/_jsxDEV(ListTodo, {\n                  className: \"w-4 h-4\",\n                  style: activeLeftPanel === 'tasks' && !isDarkMode ? {\n                    color: currentTheme.primary\n                  } : {}\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xs mt-0.5 ${activeLeftPanel === 'tasks' ? 'font-semibold' : ''}`,\n                children: \"Tasks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 15\n            }, this), hoveredIcon === 'sidebar-tasks' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\",\n              children: [\"Tasks\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveLeftPanel('goals'),\n              onMouseEnter: () => setHoveredIcon('sidebar-goals'),\n              onMouseLeave: () => setHoveredIcon(null),\n              className: `w-10 h-10 flex flex-col items-center justify-center transition-colors ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${activeLeftPanel === 'goals' ? isDarkMode ? 'bg-white/20' : 'bg-white' : 'hover:bg-white/10'}`,\n                children: /*#__PURE__*/_jsxDEV(Goal, {\n                  className: \"w-4 h-4\",\n                  style: activeLeftPanel === 'goals' && !isDarkMode ? {\n                    color: currentTheme.primary\n                  } : {}\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xs mt-0.5 ${activeLeftPanel === 'goals' ? 'font-semibold' : ''}`,\n                children: \"Goals\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 15\n            }, this), hoveredIcon === 'sidebar-goals' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\",\n              children: [\"Goals\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 899,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 897,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveLeftPanel('timer'),\n              onMouseEnter: () => setHoveredIcon('sidebar-timesheet'),\n              onMouseLeave: () => setHoveredIcon(null),\n              className: `w-10 h-10 flex flex-col items-center justify-center transition-colors ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${activeLeftPanel === 'timer' ? isDarkMode ? 'bg-white/20' : 'bg-white' : 'hover:bg-white/10'}`,\n                children: /*#__PURE__*/_jsxDEV(Timer, {\n                  className: \"w-4 h-4\",\n                  style: activeLeftPanel === 'timer' && !isDarkMode ? {\n                    color: currentTheme.primary\n                  } : {}\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xs mt-0.5 ${activeLeftPanel === 'timer' ? 'font-semibold' : ''}`,\n                children: \"Timesheet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 924,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 15\n            }, this), hoveredIcon === 'sidebar-timesheet' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\",\n              children: [\"Timesheet\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 929,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 905,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onMouseEnter: () => setHoveredIcon('sidebar-more'),\n              onMouseLeave: () => setHoveredIcon(null),\n              className: `w-10 h-10 flex flex-col items-center justify-center transition-colors ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 rounded-lg flex items-center justify-center transition-colors hover:bg-white/10\",\n                children: /*#__PURE__*/_jsxDEV(MoreHorizontal, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs mt-0.5\",\n                children: \"More\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 936,\n              columnNumber: 15\n            }, this), hoveredIcon === 'sidebar-more' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\",\n              children: [\"More Apps\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 949,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 935,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 958,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative group\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onMouseEnter: () => setHoveredIcon('sidebar-invite'),\n            onMouseLeave: () => setHoveredIcon(null),\n            className: `w-10 h-10 flex flex-col items-center justify-center transition-colors ${isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 rounded-lg flex items-center justify-center transition-colors hover:bg-white/10\",\n              children: /*#__PURE__*/_jsxDEV(UserPlus, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs mt-0.5\",\n              children: \"Invite\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 972,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 13\n          }, this), hoveredIcon === 'sidebar-invite' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\",\n            children: [\"Invite Members\", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 961,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 673,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          ref: sidebarRef,\n          className: `border-r flex flex-col flex-shrink-0 relative`,\n          style: {\n            width: `${sidebarWidth}px`,\n            backgroundColor: isDarkMode ? '#1f2937' : '#f9fafb',\n            borderColor: isDarkMode ? '#374151' : '#e5e7eb'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            ref: resizeRef,\n            className: `resize-handle ${isResizing ? 'resizing' : ''}`,\n            onMouseDown: handleResizeStart\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 13\n          }, this), activeLeftPanel === 'home' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-y-auto custom-scrollbar\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: `text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`,\n                  children: \"Hackathon Stages\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `${isDarkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`,\n                  children: /*#__PURE__*/_jsxDEV(MoreHorizontal, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex justify-between text-xs mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Progress\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1016,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [Math.round((Object.keys(stages).indexOf(selectedStage) + 1) / Object.keys(stages).length * 100), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1017,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1015,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-full rounded-full h-2 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-2 rounded-full transition-all duration-700 ease-out\",\n                    style: {\n                      backgroundColor: currentTheme.primary,\n                      width: `${(Object.keys(stages).indexOf(selectedStage) + 1) / Object.keys(stages).length * 100}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1020,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-1\",\n                children: Object.entries(stages).map(([stageName, stageData]) => {\n                  const isActive = selectedStage === stageName;\n                  const isPast = Object.keys(stages).indexOf(selectedStage) > Object.keys(stages).indexOf(stageName);\n                  return /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleStageChange(stageName),\n                    className: `w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-3 ${isActive ? 'font-medium' : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'}`,\n                    style: {\n                      backgroundColor: isActive ? currentTheme.accent : 'transparent',\n                      color: isActive ? currentTheme.accentText : isDarkMode ? '#d1d5db' : '#374151'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-center w-5 h-5\",\n                      children: isPast && !isActive ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n                        className: \"w-4 h-4 text-green-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1051,\n                        columnNumber: 31\n                      }, this) : (/*#__PURE__*/React.cloneElement(stageData.icon, {\n                        className: `w-4 h-4`,\n                        style: {\n                          color: isActive ? currentTheme.accentText : '#6b7280'\n                        }\n                      }))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1049,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: stageName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1061,\n                      columnNumber: 27\n                    }, this)]\n                  }, stageName, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1030,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: `text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`,\n                  children: \"Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1071,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `${isDarkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`,\n                  children: /*#__PURE__*/_jsxDEV(MoreHorizontal, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1073,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1072,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1070,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-1\",\n                children: Object.entries((currentStage === null || currentStage === void 0 ? void 0 : currentStage.categories) || {}).map(([categoryName, categoryData]) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleCategoryChange(categoryName),\n                  className: `w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-3 ${selectedCategory === categoryName ? 'font-medium' : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'}`,\n                  style: {\n                    backgroundColor: selectedCategory === categoryName ? currentTheme.accent : 'transparent',\n                    color: selectedCategory === categoryName ? currentTheme.accentText : isDarkMode ? '#d1d5db' : '#374151'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-center w-5 h-5\",\n                    children: categoryData.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1091,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: categoryName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1094,\n                    columnNumber: 25\n                  }, this)]\n                }, categoryName, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1078,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1069,\n              columnNumber: 17\n            }, this), currentCategory && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `border-t p-4 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: `text-xs font-semibold uppercase tracking-wide mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                children: \"Subcategories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1103,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-1\",\n                children: Object.keys(currentCategory.subcategories).map(subcategoryName => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setSelectedSubcategory(subcategoryName),\n                  className: `w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center justify-between ${selectedSubcategory === subcategoryName ? 'font-medium' : isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'}`,\n                  style: {\n                    backgroundColor: selectedSubcategory === subcategoryName ? currentTheme.accent : 'transparent',\n                    color: selectedSubcategory === subcategoryName ? currentTheme.accentText : isDarkMode ? '#9ca3af' : '#4b5563'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: subcategoryName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1121,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(ChevronRight, {\n                    className: \"w-3 h-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1122,\n                    columnNumber: 27\n                  }, this)]\n                }, subcategoryName, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1108,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1106,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1102,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-y-auto custom-scrollbar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [activeLeftPanel === 'inbox' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-sm font-semibold text-gray-900 mb-4\",\n                  children: \"Inbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1135,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-lg bg-white border\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1139,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: \"New Task Assignment\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1140,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"2h ago\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1141,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1138,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-700\",\n                      children: \"You've been assigned to work on the frontend components.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1143,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1137,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-lg bg-white border\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1149,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: \"Code Review Completed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1150,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"4h ago\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1151,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1148,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-700\",\n                      children: \"Your pull request has been approved and merged.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1153,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1147,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1136,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1134,\n                columnNumber: 21\n              }, this), activeLeftPanel === 'chat' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-sm font-semibold text-gray-900 mb-4\",\n                  children: \"AI Brain Chat\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1163,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-lg bg-white border\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(Brain, {\n                          className: \"w-3 h-3 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1168,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1167,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: \"AI Assistant\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1170,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"now\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1171,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1166,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-700\",\n                      children: \"How can I help you with your hackathon project today?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1173,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1165,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-lg bg-blue-50 border border-blue-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-white text-xs\",\n                          children: \"You\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1180,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1179,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: \"You\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1182,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"1m ago\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1183,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1178,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-700\",\n                      children: \"I need help with React component optimization.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1185,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1177,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1164,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    placeholder: \"Ask AI Brain anything...\",\n                    className: \"w-full px-3 py-2 rounded border text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1191,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1190,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1162,\n                columnNumber: 21\n              }, this), activeLeftPanel === 'calendar' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-sm font-semibold text-gray-900 mb-4\",\n                  children: \"Calendar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1202,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded bg-white border\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-blue-600\",\n                      children: \"Today - 10:00 AM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1205,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-900 mt-1\",\n                      children: \"Team Standup Meeting\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1206,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"15 minutes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1207,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1204,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded bg-white border\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-green-600\",\n                      children: \"Today - 2:00 PM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1210,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-900 mt-1\",\n                      children: \"Code Review Session\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1211,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"1 hour\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1212,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1209,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded bg-white border\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-purple-600\",\n                      children: \"Tomorrow - 9:00 AM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1215,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-900 mt-1\",\n                      children: \"Hackathon Presentation\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1216,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"30 minutes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1217,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1214,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1203,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1201,\n                columnNumber: 21\n              }, this), activeLeftPanel === 'timer' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-sm font-semibold text-gray-900 mb-4\",\n                  children: \"Time Tracker\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1225,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-mono text-gray-900 mb-2\",\n                    children: \"02:34:18\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1227,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Working on: Frontend Development\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1228,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1226,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full py-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors text-sm\",\n                    children: \"Start Timer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1231,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors text-sm\",\n                    children: \"Stop Timer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1234,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1230,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xs font-semibold text-gray-500 mb-3\",\n                    children: \"Today's Sessions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1239,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded bg-white border\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-900\",\n                        children: \"Backend Development\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1242,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"1h 23m\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1243,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1241,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded bg-white border\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-900\",\n                        children: \"Code Review\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1246,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"45m\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1247,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1245,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1240,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1238,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1224,\n                columnNumber: 21\n              }, this), activeLeftPanel === 'teamchat' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: `text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`,\n                    children: \"Team Chat\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1257,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1260,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                        children: [teamMembers.length, \" online\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1261,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1259,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1258,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1256,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                      children: \"Team Members\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1271,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1270,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap gap-1\",\n                    children: teamMembers.map(member => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-6 h-6 rounded-full flex items-center justify-center text-white text-xs relative ${member.color}`,\n                        children: [member.avatar, /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-green-500 rounded-full border border-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1278,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1276,\n                        columnNumber: 31\n                      }, this)\n                    }, member.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1275,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1273,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1269,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4 grid grid-cols-2 gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"px-3 py-2 rounded text-xs font-medium transition-colors text-white\",\n                    style: {\n                      backgroundColor: currentTheme.primary\n                    },\n                    children: \"\\uD83D\\uDCE2 Announce\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1287,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `px-3 py-2 rounded text-xs font-medium transition-colors ${isDarkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    children: \"\\uD83D\\uDCCE Share File\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1293,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1286,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: `text-xs font-semibold uppercase tracking-wide mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                    children: \"Recent Activity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1302,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-xs p-2 rounded ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`,\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                        children: \"Sarah\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1307,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                        children: \" completed \\\"Setup Database\\\"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1308,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1306,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-xs p-2 rounded ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`,\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                        children: \"Mike\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1311,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                        children: \" shared a file\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1312,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1310,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1305,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1301,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} text-center`,\n                  children: [chatMessages.length, \" messages today\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1318,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1255,\n                columnNumber: 21\n              }, this), activeLeftPanel === 'tasks' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: `text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`,\n                    children: \"Tasks\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1327,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setIsLeader(!isLeader),\n                    className: `px-3 py-1 rounded text-xs font-medium transition-colors ${isLeader ? 'text-white' : isDarkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    style: isLeader ? {\n                      backgroundColor: currentTheme.primary\n                    } : {},\n                    children: isLeader ? 'Leader' : 'Member'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1328,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1326,\n                  columnNumber: 23\n                }, this), isLeader && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                      children: \"Filter by:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1345,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1344,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setSelectedMember('All Members'),\n                      className: `w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-2 ${selectedMember === 'All Members' ? 'text-white font-medium' : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'}`,\n                      style: selectedMember === 'All Members' ? {\n                        backgroundColor: currentTheme.accent,\n                        color: currentTheme.accentText\n                      } : {},\n                      children: [/*#__PURE__*/_jsxDEV(Users, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1357,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"All Members\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1358,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1348,\n                      columnNumber: 29\n                    }, this), teamMembers.map(member => /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setSelectedMember(member.name),\n                      className: `w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-2 ${selectedMember === member.name ? 'text-white font-medium' : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'}`,\n                      style: selectedMember === member.name ? {\n                        backgroundColor: currentTheme.accent,\n                        color: currentTheme.accentText\n                      } : {},\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-5 h-5 rounded-full flex items-center justify-center text-white text-xs ${member.color}`,\n                        children: member.avatar\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1371,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: member.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1374,\n                        columnNumber: 33\n                      }, this)]\n                    }, member.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1361,\n                      columnNumber: 31\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1347,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1343,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4 grid grid-cols-3 gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-2 rounded border text-center ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border'}`,\n                    style: !isDarkMode ? {\n                      borderColor: currentTheme.primaryLight + '30'\n                    } : {},\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"To Do\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1385,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-sm font-semibold`,\n                      style: {\n                        color: currentTheme.primary\n                      },\n                      children: getFilteredTasks().filter(t => t.status === 'todo').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1386,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1383,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-2 rounded border text-center ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border'}`,\n                    style: !isDarkMode ? {\n                      borderColor: currentTheme.primaryLight + '30'\n                    } : {},\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"In Progress\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1392,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-sm font-semibold`,\n                      style: {\n                        color: currentTheme.primary\n                      },\n                      children: getFilteredTasks().filter(t => t.status === 'in-progress').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1393,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1390,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-2 rounded border text-center ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border'}`,\n                    style: !isDarkMode ? {\n                      borderColor: currentTheme.primaryLight + '30'\n                    } : {},\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"Done\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1399,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-sm font-semibold`,\n                      style: {\n                        color: currentTheme.primary\n                      },\n                      children: getFilteredTasks().filter(t => t.status === 'completed').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1400,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1397,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1382,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full py-2 rounded border border-dashed border-gray-300 text-gray-500 hover:bg-gray-50 transition-colors text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(Plus, {\n                      className: \"w-4 h-4 inline mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1409,\n                      columnNumber: 27\n                    }, this), \"Add Task\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1408,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} text-center`,\n                    children: isLeader && selectedMember !== 'All Members' ? `Showing ${selectedMember}'s tasks` : `${getFilteredTasks().length} tasks total`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1413,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1407,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1325,\n                columnNumber: 21\n              }, this), activeLeftPanel === 'goals' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-sm font-semibold text-gray-900 mb-4\",\n                  children: \"Goals\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1425,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded border-l-4 border-green-500 bg-white border-t border-r border-b\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: \"Complete MVP\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1428,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"Due: End of hackathon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1429,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mt-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-1.5 mr-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-green-500 h-1.5 rounded-full\",\n                          style: {\n                            width: '75%'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1432,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1431,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-green-600\",\n                        children: \"75%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1434,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1430,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1427,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded border-l-4 border-blue-500 bg-white border-t border-r border-b\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: \"Learn React Hooks\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1438,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"Personal goal\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1439,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mt-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-1.5 mr-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-blue-500 h-1.5 rounded-full\",\n                          style: {\n                            width: '60%'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1442,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1441,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-blue-600\",\n                        children: \"60%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1444,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1440,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1437,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded border-l-4 border-purple-500 bg-white border-t border-r border-b\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: \"Team Collaboration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1448,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"Soft skill development\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1449,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mt-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-1.5 mr-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-purple-500 h-1.5 rounded-full\",\n                          style: {\n                            width: '90%'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1452,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1451,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-purple-600\",\n                        children: \"90%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1454,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1450,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1447,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1426,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"mt-4 w-full py-2 rounded border border-dashed border-gray-300 text-gray-500 hover:bg-gray-50 transition-colors text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(Plus, {\n                    className: \"w-4 h-4 inline mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1459,\n                    columnNumber: 25\n                  }, this), \"Add Goal\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1458,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1424,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1131,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1130,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 986,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex-1 overflow-y-auto custom-scrollbar ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`,\n          children: [activeLeftPanel === 'teamchat' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 border-b ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200'}`,\n              style: !isDarkMode ? {\n                backgroundColor: currentTheme.accent\n              } : {},\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 rounded-lg flex items-center justify-center text-white\",\n                    style: {\n                      backgroundColor: currentTheme.primary\n                    },\n                    children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1483,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1479,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                      className: `text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                      children: \"Team Chat\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1486,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                      children: [teamMembers.length, \" members \\u2022 \", teamMembers.length, \" online\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1489,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1485,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1478,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-white/50 text-gray-600'}`,\n                    children: /*#__PURE__*/_jsxDEV(Search, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1500,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1495,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-white/50 text-gray-600'}`,\n                    children: /*#__PURE__*/_jsxDEV(MoreHorizontal, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1507,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1502,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1494,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1477,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1475,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex-1 overflow-y-auto p-4 space-y-4 ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`,\n              children: chatMessages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-8 h-8 rounded-full flex items-center justify-center text-white text-xs flex-shrink-0 ${msg.color}`,\n                  children: msg.avatar\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1517,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                      children: msg.sender\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1522,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                      children: formatTime(msg.timestamp)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1525,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1521,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `inline-block px-3 py-2 rounded-lg max-w-md ${msg.sender === 'John Doe' ? 'text-white ml-auto' : isDarkMode ? 'bg-gray-800 text-gray-200' : 'bg-gray-100 text-gray-900'}`,\n                    style: msg.sender === 'John Doe' ? {\n                      backgroundColor: currentTheme.primary\n                    } : {},\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm\",\n                      children: msg.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1537,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1529,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1520,\n                  columnNumber: 23\n                }, this)]\n              }, msg.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1516,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1514,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 border-t ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `p-2 rounded-lg transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-200 text-gray-600'}`,\n                    children: /*#__PURE__*/_jsxDEV(Plus, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1553,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1548,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1547,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 relative\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: newMessage,\n                    onChange: e => setNewMessage(e.target.value),\n                    onKeyPress: e => e.key === 'Enter' && addMessage(),\n                    placeholder: \"Type a message...\",\n                    className: `w-full px-4 py-2 rounded-lg border focus:outline-none focus:ring-2 ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-gray-500' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'}`,\n                    style: !isDarkMode ? {\n                      focusRingColor: currentTheme.primary\n                    } : {}\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1557,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1556,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: addMessage,\n                  disabled: !newMessage.trim(),\n                  className: `p-2 rounded-lg transition-colors text-white ${newMessage.trim() ? 'opacity-100' : 'opacity-50 cursor-not-allowed'}`,\n                  style: {\n                    backgroundColor: currentTheme.primary\n                  },\n                  children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1581,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1571,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1546,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `mt-2 text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-1 h-1 bg-gray-400 rounded-full animate-bounce\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1589,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-1 h-1 bg-gray-400 rounded-full animate-bounce\",\n                      style: {\n                        animationDelay: '0.1s'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1590,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-1 h-1 bg-gray-400 rounded-full animate-bounce\",\n                      style: {\n                        animationDelay: '0.2s'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1591,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1588,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Sarah is typing...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1593,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1587,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1586,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1545,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1473,\n            columnNumber: 15\n          }, this), activeLeftPanel === 'tasks' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-6xl\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                      className: `text-2xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                      children: isLeader && selectedMember !== 'All Members' ? `${selectedMember}'s Tasks` : 'All Tasks'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1608,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                      children: isLeader ? selectedMember === 'All Members' ? 'Manage all team tasks and assignments' : `Tasks assigned to ${selectedMember}` : 'Your personal task list'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1611,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1607,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `px-4 py-2 rounded-lg border transition-colors ${isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'}`,\n                      children: \"Sort by Due Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1621,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"px-4 py-2 text-white rounded-lg transition-colors flex items-center space-x-2\",\n                      style: {\n                        backgroundColor: currentTheme.primary,\n                        '&:hover': {\n                          backgroundColor: currentTheme.primaryDark\n                        }\n                      },\n                      onMouseEnter: e => e.target.style.backgroundColor = currentTheme.primaryDark,\n                      onMouseLeave: e => e.target.style.backgroundColor = currentTheme.primary,\n                      children: [/*#__PURE__*/_jsxDEV(Plus, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1635,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"New Task\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1636,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1626,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1620,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1606,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1605,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `rounded-lg p-4`,\n                  style: {\n                    backgroundColor: isDarkMode ? '#1f2937' : currentTheme.accent\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: `font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                      children: \"To Do\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1647,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 rounded text-xs font-medium text-white\",\n                      style: {\n                        backgroundColor: currentTheme.primary\n                      },\n                      children: getFilteredTasks().filter(t => t.status === 'todo').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1650,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1646,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3\",\n                    children: [getFilteredTasks().filter(task => task.status === 'todo').map(task => {\n                      var _teamMembers$find, _teamMembers$find2;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${isDarkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-200 hover:shadow-lg'}`,\n                        style: !isDarkMode ? {\n                          borderColor: currentTheme.primaryLight + '20'\n                        } : {},\n                        onMouseEnter: e => {\n                          if (!isDarkMode) {\n                            e.target.style.borderColor = currentTheme.primaryLight + '40';\n                            e.target.style.transform = 'translateY(-1px)';\n                          }\n                        },\n                        onMouseLeave: e => {\n                          if (!isDarkMode) {\n                            e.target.style.borderColor = currentTheme.primaryLight + '20';\n                            e.target.style.transform = 'translateY(0)';\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-start justify-between mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            className: `font-medium text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                            children: task.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1677,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `px-2 py-1 rounded text-xs font-medium ${task.priority === 'high' ? 'bg-red-100 text-red-700' : task.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' : 'bg-gray-100 text-gray-700'}`,\n                            children: task.priority\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1680,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1676,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: `text-xs mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                          children: task.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1690,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between\",\n                          children: [isLeader && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `w-6 h-6 rounded-full flex items-center justify-center text-white text-xs ${((_teamMembers$find = teamMembers.find(m => m.name === task.assignee)) === null || _teamMembers$find === void 0 ? void 0 : _teamMembers$find.color) || 'bg-gray-500'}`,\n                              children: ((_teamMembers$find2 = teamMembers.find(m => m.name === task.assignee)) === null || _teamMembers$find2 === void 0 ? void 0 : _teamMembers$find2.avatar) || 'U'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1696,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                              children: task.assignee\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1701,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1695,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                            children: new Date(task.dueDate).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1706,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1693,\n                          columnNumber: 29\n                        }, this)]\n                      }, task.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1659,\n                        columnNumber: 27\n                      }, this);\n                    }), getFilteredTasks().filter(t => t.status === 'todo').length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-center py-8 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`,\n                      children: [/*#__PURE__*/_jsxDEV(Circle, {\n                        className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1714,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm\",\n                        children: \"No tasks to do\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1715,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1713,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1657,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1645,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `rounded-lg p-4`,\n                  style: {\n                    backgroundColor: isDarkMode ? '#1f2937' : currentTheme.accent\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: `font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                      children: \"In Progress\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1724,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 rounded text-xs font-medium text-white\",\n                      style: {\n                        backgroundColor: currentTheme.primary\n                      },\n                      children: getFilteredTasks().filter(t => t.status === 'in-progress').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1727,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1723,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3\",\n                    children: [getFilteredTasks().filter(task => task.status === 'in-progress').map(task => {\n                      var _teamMembers$find3, _teamMembers$find4;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${isDarkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-200 hover:shadow-lg'}`,\n                        style: !isDarkMode ? {\n                          borderColor: currentTheme.primaryLight + '20'\n                        } : {},\n                        onMouseEnter: e => {\n                          if (!isDarkMode) {\n                            e.target.style.borderColor = currentTheme.primaryLight + '40';\n                            e.target.style.transform = 'translateY(-1px)';\n                          }\n                        },\n                        onMouseLeave: e => {\n                          if (!isDarkMode) {\n                            e.target.style.borderColor = currentTheme.primaryLight + '20';\n                            e.target.style.transform = 'translateY(0)';\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-start justify-between mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            className: `font-medium text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                            children: task.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1754,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `px-2 py-1 rounded text-xs font-medium ${task.priority === 'high' ? 'bg-red-100 text-red-700' : task.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' : 'bg-gray-100 text-gray-700'}`,\n                            children: task.priority\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1757,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1753,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: `text-xs mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                          children: task.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1767,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between\",\n                          children: [isLeader && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `w-6 h-6 rounded-full flex items-center justify-center text-white text-xs ${((_teamMembers$find3 = teamMembers.find(m => m.name === task.assignee)) === null || _teamMembers$find3 === void 0 ? void 0 : _teamMembers$find3.color) || 'bg-gray-500'}`,\n                              children: ((_teamMembers$find4 = teamMembers.find(m => m.name === task.assignee)) === null || _teamMembers$find4 === void 0 ? void 0 : _teamMembers$find4.avatar) || 'U'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1773,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                              children: task.assignee\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1778,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1772,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                            children: new Date(task.dueDate).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1783,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1770,\n                          columnNumber: 29\n                        }, this)]\n                      }, task.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1736,\n                        columnNumber: 27\n                      }, this);\n                    }), getFilteredTasks().filter(t => t.status === 'in-progress').length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-center py-8 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 rounded-full border-2 border-gray-300 mx-auto mb-2 opacity-50\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1791,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm\",\n                        children: \"No tasks in progress\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1792,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1790,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1734,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1722,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `rounded-lg p-4`,\n                  style: {\n                    backgroundColor: isDarkMode ? '#1f2937' : currentTheme.accent\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: `font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                      children: \"Done\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1801,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 rounded text-xs font-medium text-white\",\n                      style: {\n                        backgroundColor: currentTheme.primary\n                      },\n                      children: getFilteredTasks().filter(t => t.status === 'completed').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1804,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1800,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3\",\n                    children: [getFilteredTasks().filter(task => task.status === 'completed').map(task => {\n                      var _teamMembers$find5, _teamMembers$find6;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${isDarkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-200 hover:shadow-lg'}`,\n                        style: !isDarkMode ? {\n                          borderColor: currentTheme.primaryLight + '20'\n                        } : {},\n                        onMouseEnter: e => {\n                          if (!isDarkMode) {\n                            e.target.style.borderColor = currentTheme.primaryLight + '40';\n                            e.target.style.transform = 'translateY(-1px)';\n                          }\n                        },\n                        onMouseLeave: e => {\n                          if (!isDarkMode) {\n                            e.target.style.borderColor = currentTheme.primaryLight + '20';\n                            e.target.style.transform = 'translateY(0)';\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-start justify-between mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            className: `font-medium text-sm line-through ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`,\n                            children: task.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1831,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(CheckCircle2, {\n                            className: \"w-5 h-5\",\n                            style: {\n                              color: currentTheme.primary\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1834,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1830,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: `text-xs mb-3 ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`,\n                          children: task.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1839,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-between\",\n                          children: [isLeader && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `w-6 h-6 rounded-full flex items-center justify-center text-white text-xs ${((_teamMembers$find5 = teamMembers.find(m => m.name === task.assignee)) === null || _teamMembers$find5 === void 0 ? void 0 : _teamMembers$find5.color) || 'bg-gray-500'}`,\n                              children: ((_teamMembers$find6 = teamMembers.find(m => m.name === task.assignee)) === null || _teamMembers$find6 === void 0 ? void 0 : _teamMembers$find6.avatar) || 'U'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1845,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: `text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`,\n                              children: task.assignee\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1850,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1844,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`,\n                            children: new Date(task.dueDate).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1855,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1842,\n                          columnNumber: 29\n                        }, this)]\n                      }, task.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1813,\n                        columnNumber: 27\n                      }, this);\n                    }), getFilteredTasks().filter(t => t.status === 'completed').length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-center py-8 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`,\n                      children: [/*#__PURE__*/_jsxDEV(CheckCircle2, {\n                        className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1863,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm\",\n                        children: \"No completed tasks\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1864,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1862,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1811,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1799,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1643,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1603,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1602,\n            columnNumber: 15\n          }, this), activeLeftPanel === 'home' && currentSubcategory && (currentSubcategory.component ? currentSubcategory.component : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-4xl\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex items-center space-x-2 text-xs mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: selectedStage\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1883,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(ChevronRight, {\n                    className: \"w-3 h-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1884,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: selectedCategory\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1885,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(ChevronRight, {\n                    className: \"w-3 h-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1886,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: selectedSubcategory\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1887,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1882,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: `text-2xl font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                  children: selectedSubcategory\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1889,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                  children: currentSubcategory.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1890,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(currentSubcategory.difficulty)}`,\n                    children: currentSubcategory.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1893,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                      children: \"Tech Stack:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1897,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-wrap gap-2\",\n                      children: [currentSubcategory.techStack.slice(0, 3).map((tech, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-2 py-1 rounded text-xs border ${isDarkMode ? 'bg-gray-800 text-gray-300 border-gray-700' : 'bg-gray-100 text-gray-700 border'}`,\n                        children: tech\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1900,\n                        columnNumber: 29\n                      }, this)), currentSubcategory.techStack.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-2 py-1 rounded text-xs border ${isDarkMode ? 'bg-gray-800 text-gray-300 border-gray-700' : 'bg-gray-100 text-gray-700 border'}`,\n                        children: [\"+\", currentSubcategory.techStack.length - 3, \" more\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1907,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1898,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1896,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1892,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1881,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: `text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                  children: \"Project Ideas & Topics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1920,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid gap-4\",\n                  children: currentSubcategory.topics.map((topic, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `rounded-lg p-4 border transition-colors ${isDarkMode ? 'bg-gray-800 border-gray-700 hover:bg-gray-750' : 'bg-white border hover:bg-gray-50'}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start space-x-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-6 h-6 rounded flex items-center justify-center flex-shrink-0 ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-blue-100 text-blue-600'}`,\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-semibold text-xs\",\n                          children: index + 1\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1930,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1927,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: `font-medium mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                          children: topic.split(' - ')[0]\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1933,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: `text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                          children: topic.split(' - ')[1]\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1936,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1932,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1926,\n                      columnNumber: 27\n                    }, this)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1923,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1921,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1919,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `rounded-lg p-4 border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: `text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`,\n                  children: \"Recommended Technology Stack\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1948,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\",\n                  children: currentSubcategory.techStack.map((tech, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `px-4 py-2 rounded border text-center transition-colors ${isDarkMode ? 'bg-gray-700 border-gray-600 hover:bg-gray-650' : 'bg-white border hover:bg-gray-50'}`,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-sm font-medium ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`,\n                      children: tech\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1954,\n                      columnNumber: 27\n                    }, this)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1951,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1949,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1947,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1879,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1878,\n            columnNumber: 15\n          }, this)), activeLeftPanel !== 'home' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-4xl\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-center py-16 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: [activeLeftPanel === 'inbox' && /*#__PURE__*/_jsxDEV(Inbox, {\n                    className: \"w-12 h-12 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1969,\n                    columnNumber: 55\n                  }, this), activeLeftPanel === 'chat' && /*#__PURE__*/_jsxDEV(Brain, {\n                    className: \"w-12 h-12 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1970,\n                    columnNumber: 54\n                  }, this), activeLeftPanel === 'calendar' && /*#__PURE__*/_jsxDEV(Calendar, {\n                    className: \"w-12 h-12 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1971,\n                    columnNumber: 58\n                  }, this), activeLeftPanel === 'timer' && /*#__PURE__*/_jsxDEV(Timer, {\n                    className: \"w-12 h-12 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1972,\n                    columnNumber: 55\n                  }, this), activeLeftPanel === 'goals' && /*#__PURE__*/_jsxDEV(Goal, {\n                    className: \"w-12 h-12 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1973,\n                    columnNumber: 55\n                  }, this), activeLeftPanel === 'tasks' && /*#__PURE__*/_jsxDEV(ListTodo, {\n                    className: \"w-12 h-12 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1974,\n                    columnNumber: 55\n                  }, this), activeLeftPanel === 'teamchat' && /*#__PURE__*/_jsxDEV(MessageCircle, {\n                    className: \"w-12 h-12 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1975,\n                    columnNumber: 58\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1968,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: `text-xl font-semibold mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`,\n                  children: [activeLeftPanel === 'inbox' && 'Inbox Management', activeLeftPanel === 'chat' && 'AI Brain Assistant', activeLeftPanel === 'calendar' && 'Calendar View', activeLeftPanel === 'timer' && 'Time Tracking', activeLeftPanel === 'goals' && 'Goal Management', activeLeftPanel === 'tasks' && 'Task Management']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1977,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`,\n                  children: [\"Manage your \", activeLeftPanel, \" from the sidebar. Click \\\"Home\\\" to return to hackathon stages.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1985,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1967,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1966,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1965,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1470,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 984,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 470,\n    columnNumber: 5\n  }, this);\n};\n_s(HackathonDashboard, \"b7VR0XZMKHLRr29RWmRZdTqoAVc=\");\n_c = HackathonDashboard;\nexport default HackathonDashboard;\nvar _c;\n$RefreshReg$(_c, \"HackathonDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "ChevronRight", "Code", "Smartphone", "Brain", "Shield", "Globe", "Heart", "GraduationCap", "Leaf", "Users", "Lightbulb", "Trophy", "Zap", "Clock", "Target", "Presentation", "Award", "Settings", "User", "Calendar", "FileText", "CheckCircle", "Star", "Search", "Bell", "Moon", "Sun", "MoreHorizontal", "Grid3X3", "Home", "Inbox", "MessageCircle", "ListTodo", "BarChart3", "Timer", "Goal", "UserPlus", "Plus", "Circle", "CheckCircle2", "LinkedInPostGenerator", "PowerPointerGenerator", "jsxDEV", "_jsxDEV", "HackathonDashboard", "_s", "selectedStage", "setSelectedStage", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubcategory", "setSelectedSubcategory", "hoveredIcon", "setHoveredIcon", "isDarkMode", "setIsDarkMode", "showNotifications", "setShowNotifications", "activeLeftPanel", "setActiveLeftPanel", "sidebarWidth", "setSidebarWidth", "isResizing", "setIsResizing", "<PERSON><PERSON><PERSON><PERSON>", "setIsLeader", "selected<PERSON><PERSON>ber", "setSelectedMember", "chatMessages", "setChatMessages", "id", "sender", "avatar", "color", "message", "timestamp", "Date", "now", "type", "newMessage", "setNewMessage", "resizeRef", "sidebarRef", "addMessage", "trim", "length", "formatTime", "diffMs", "diffMins", "Math", "floor", "diffHours", "diffDays", "toLocaleDateString", "teamMembers", "name", "allTasks", "title", "assignee", "status", "priority", "dueDate", "description", "getFilteredTasks", "filter", "task", "stageThemes", "primary", "primaryLight", "primaryDark", "accent", "accentText", "currentTheme", "stages", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "bgGradient", "categories", "subcategories", "topics", "techStack", "difficulty", "currentStage", "currentCategory", "currentSubcategory", "handleMouseMove", "e", "newWidth", "clientX", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "handleMouseUp", "document", "body", "style", "cursor", "userSelect", "addEventListener", "removeEventListener", "handleResizeStart", "preventDefault", "getDifficultyColor", "handleStageChange", "stageName", "firstCategory", "Object", "keys", "firstSubcategory", "handleCategoryChange", "categoryName", "children", "jsx", "backgroundColor", "borderColor", "placeholder", "onClick", "onMouseEnter", "target", "onMouseLeave", "ref", "width", "onMouseDown", "round", "indexOf", "entries", "map", "stageData", "isActive", "isPast", "cloneElement", "categoryData", "subcategoryName", "member", "t", "msg", "index", "value", "onChange", "onKeyPress", "key", "focusRingColor", "disabled", "animationDelay", "_teamMembers$find", "_teamMembers$find2", "transform", "find", "m", "_teamMembers$find3", "_teamMembers$find4", "_teamMembers$find5", "_teamMembers$find6", "component", "slice", "tech", "topic", "split", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Hackhelp/frontend/src/Page_Components/Home/Sample2.js"], "sourcesContent": ["import React,{ useState, useRef, useEffect } from 'react';\nimport { ChevronRight, Code, Smartphone, Brain, Shield, Globe, Heart, GraduationCap, Leaf, Users, Lightbulb, Trophy, Zap, Clock, Target, Presentation, Award, Settings, User, Calendar, FileText, CheckCircle, Star, Search, Bell, Moon, Sun, MoreHorizontal, Grid3X3, Home, Inbox, MessageCircle, ListTodo, BarChart3, Timer, Goal, UserPlus, Plus, Circle, CheckCircle2 } from 'lucide-react';\nimport LinkedInPostGenerator from '../../Pages/LinkedIn';\nimport PowerPointerGenerator from '../../Pages/PowerPointer';\n\nconst HackathonDashboard = () => {\n  const [selectedStage, setSelectedStage] = useState('Development Phase');\n  const [selectedCategory, setSelectedCategory] = useState('Web Development');\n  const [selectedSubcategory, setSelectedSubcategory] = useState('Frontend Frameworks');\n  const [hoveredIcon, setHoveredIcon] = useState(null);\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [activeLeftPanel, setActiveLeftPanel] = useState('home');\n  const [sidebarWidth, setSidebarWidth] = useState(288); // 72 * 4 = 288px (w-72)\n  const [isResizing, setIsResizing] = useState(false);\n  const [isLeader, setIsLeader] = useState(true);\n  const [selectedMember, setSelectedMember] = useState('All Members');\n  const [chatMessages, setChatMessages] = useState([\n    {\n      id: 1,\n      sender: 'Sarah Wilson',\n      avatar: 'SW',\n      color: 'bg-green-500',\n      message: \"Hey team! Just finished setting up the database schema. Ready to move on to the API endpoints.\",\n      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago\n      type: 'message'\n    },\n    {\n      id: 2,\n      sender: 'Mike Chen',\n      avatar: 'MC', \n      color: 'bg-purple-500',\n      message: \"Great work Sarah! I'm working on the authentication system. Should have it ready by end of day.\",\n      timestamp: new Date(Date.now() - 1000 * 60 * 10), // 10 minutes ago\n      type: 'message'\n    },\n    {\n      id: 3,\n      sender: 'John Doe',\n      avatar: 'JD',\n      color: 'bg-blue-500', \n      message: \"UI components are looking good. Added the new design system tokens. Check it out when you get a chance!\",\n      timestamp: new Date(Date.now() - 1000 * 60 * 8), // 8 minutes ago\n      type: 'message'\n    },\n    {\n      id: 4,\n      sender: 'Emma Davis',\n      avatar: 'ED',\n      color: 'bg-pink-500',\n      message: \"📝 Just uploaded the API documentation to the shared drive\",\n      timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago\n      type: 'message'\n    },\n    {\n      id: 5,\n      sender: 'Alex Johnson',\n      avatar: 'AJ',\n      color: 'bg-orange-500',\n      message: \"Mobile testing is complete ✅ Everything looks responsive. Great job everyone!\",\n      timestamp: new Date(Date.now() - 1000 * 60 * 2), // 2 minutes ago\n      type: 'message'\n    }\n  ]);\n  const [newMessage, setNewMessage] = useState('');\n  \n  const resizeRef = useRef(null);\n  const sidebarRef = useRef(null);\n\n  const addMessage = () => {\n    if (newMessage.trim()) {\n      const message = {\n        id: chatMessages.length + 1,\n        sender: 'John Doe', // Current user\n        avatar: 'JD',\n        color: 'bg-blue-500',\n        message: newMessage,\n        timestamp: new Date(),\n        type: 'message'\n      };\n      setChatMessages([...chatMessages, message]);\n      setNewMessage('');\n    }\n  };\n\n  const formatTime = (timestamp) => {\n    const now = new Date();\n    const diffMs = now - timestamp;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n\n    if (diffMins < 1) return 'now';\n    if (diffMins < 60) return `${diffMins}m ago`;\n    if (diffHours < 24) return `${diffHours}h ago`;\n    if (diffDays < 7) return `${diffDays}d ago`;\n    return timestamp.toLocaleDateString();\n  };\n\n  // Sample team members data\n  const teamMembers = [\n    { id: 1, name: 'John Doe', avatar: 'JD', color: 'bg-blue-500' },\n    { id: 2, name: 'Sarah Wilson', avatar: 'SW', color: 'bg-green-500' },\n    { id: 3, name: 'Mike Chen', avatar: 'MC', color: 'bg-purple-500' },\n    { id: 4, name: 'Emma Davis', avatar: 'ED', color: 'bg-pink-500' },\n    { id: 5, name: 'Alex Johnson', avatar: 'AJ', color: 'bg-orange-500' }\n  ];\n\n  // Sample tasks data\n  const allTasks = [\n    { \n      id: 1, \n      title: 'Design UI Components', \n      assignee: 'John Doe', \n      status: 'in-progress', \n      priority: 'high',\n      dueDate: '2025-05-27',\n      description: 'Create reusable UI components for the dashboard'\n    },\n    { \n      id: 2, \n      title: 'Setup Database Schema', \n      assignee: 'Sarah Wilson', \n      status: 'completed', \n      priority: 'high',\n      dueDate: '2025-05-25',\n      description: 'Design and implement the database structure'\n    },\n    { \n      id: 3, \n      title: 'Implement Authentication', \n      assignee: 'Mike Chen', \n      status: 'todo', \n      priority: 'medium',\n      dueDate: '2025-05-28',\n      description: 'Add user login and registration functionality'\n    },\n    { \n      id: 4, \n      title: 'Write API Documentation', \n      assignee: 'Emma Davis', \n      status: 'in-progress', \n      priority: 'low',\n      dueDate: '2025-05-30',\n      description: 'Document all API endpoints and usage'\n    },\n    { \n      id: 5, \n      title: 'Test Mobile Responsiveness', \n      assignee: 'Alex Johnson', \n      status: 'completed', \n      priority: 'medium',\n      dueDate: '2025-05-24',\n      description: 'Ensure the app works well on mobile devices'\n    },\n    { \n      id: 6, \n      title: 'Deploy to Staging', \n      assignee: 'John Doe', \n      status: 'todo', \n      priority: 'high',\n      dueDate: '2025-05-29',\n      description: 'Deploy the application to staging environment'\n    }\n  ];\n\n  // Filter tasks based on role and selected member\n  const getFilteredTasks = () => {\n    if (!isLeader) {\n      // If member, show only their tasks (assuming current user is John Doe)\n      return allTasks.filter(task => task.assignee === 'John Doe');\n    }\n    \n    if (selectedMember === 'All Members') {\n      return allTasks;\n    }\n    \n    return allTasks.filter(task => task.assignee === selectedMember);\n  };\n\n  // Color themes for different stages\n  const stageThemes = {\n    'Pre-Hackathon': {\n      primary: '#2b216a', // Purple\n      primaryLight: '#3730a3',\n      primaryDark: '#1e1065',\n      accent: '#e0e7ff',\n      accentText: '#3730a3'\n    },\n    'Development Phase': {\n      primary: '#0d9488', // Teal (default)\n      primaryLight: '#14b8a6',\n      primaryDark: '#0f766e',\n      accent: '#ccfbf1',\n      accentText: '#0f766e'\n    },\n    'Final Preparation': {\n      primary: '#53195d', // Violet\n      primaryLight: '#7c2d92',\n      primaryDark: '#4a1454',\n      accent: '#f3e8ff',\n      accentText: '#7c2d92'\n    },\n    'Judging & Awards': {\n      primary: '#621639', // Pink\n      primaryLight: '#831843',\n      primaryDark: '#4c1d3b',\n      accent: '#fce7f3',\n      accentText: '#831843'\n    }\n  };\n\n  const currentTheme = stageThemes[selectedStage];\n\n  const stages = {\n    'Pre-Hackathon': {\n      icon: <Settings className=\"w-6 h-6\" />,\n      color: 'bg-gray-500',\n      bgGradient: 'from-gray-400 to-gray-600',\n      categories: {\n        'Registration': {\n          icon: <User className=\"w-5 h-5\" />,\n          subcategories: {\n            'Team Formation': {\n              description: 'Build your dream team with complementary skills and shared vision.',\n              topics: [\n                'Individual Registration - Platform signup and profile creation',\n                'Skill Assessment - Evaluate technical and soft skills',\n                'Role Assignment - Define team member responsibilities',\n                'Team Dynamics - Establish communication and workflow'\n              ],\n              techStack: ['Communication Tools', 'Project Management', 'Git', 'Slack', 'Discord'],\n              difficulty: 'Beginner'\n            }\n          }\n        },\n        'Planning': {\n          icon: <Calendar className=\"w-5 h-5\" />,\n          subcategories: {\n            'Strategy Development': {\n              description: 'Research and plan your approach before the hackathon begins.',\n              topics: [\n                'Problem Analysis - Understanding challenge requirements',\n                'Solution Research - Investigating existing approaches',\n                'Technology Selection - Choosing optimal tech stack',\n                'Timeline Planning - Breaking down development phases'\n              ],\n              techStack: ['Research Tools', 'Mind Mapping', 'Documentation', 'Planning Software'],\n              difficulty: 'Intermediate'\n            }\n          }\n        }\n      }\n    },\n    'Development Phase': {\n      icon: <Code className=\"w-6 h-6\" />,\n      color: 'bg-blue-500',\n      bgGradient: 'from-blue-400 to-blue-600',\n      categories: {\n        'Web Development': {\n          icon: <Globe className=\"w-5 h-5\" />,\n          subcategories: {\n            'Frontend Frameworks': {\n              description: 'Build modern, responsive web applications using cutting-edge frontend technologies.',\n              topics: [\n                'React Applications - Component-based UI development',\n                'Vue.js Projects - Progressive framework solutions',\n                'Angular Solutions - Enterprise-grade applications',\n                'Svelte Apps - Compile-time optimized frameworks'\n              ],\n              techStack: ['JavaScript', 'TypeScript', 'HTML5', 'CSS3', 'Webpack'],\n              difficulty: 'Intermediate'\n            },\n            'Backend Development': {\n              description: 'Create robust server-side applications and APIs that power modern web services.',\n              topics: [\n                'Node.js APIs - Scalable JavaScript backends',\n                'Python Backends - Django/Flask applications',\n                'Java Services - Spring Boot microservices',\n                'Go Applications - High-performance web servers'\n              ],\n              techStack: ['Node.js', 'Python', 'Java', 'PostgreSQL', 'MongoDB'],\n              difficulty: 'Advanced'\n            }\n          }\n        },\n        'Mobile Development': {\n          icon: <Smartphone className=\"w-5 h-5\" />,\n          subcategories: {\n            'Native iOS': {\n              description: 'Develop high-performance iOS applications using Swift and native frameworks.',\n              topics: [\n                'Swift Applications - Native iOS development',\n                'SwiftUI Interfaces - Declarative UI framework',\n                'Core Data Integration - Local data persistence',\n                'iOS Widgets - Home screen extensions'\n              ],\n              techStack: ['Swift', 'SwiftUI', 'Xcode', 'Core Data', 'UIKit'],\n              difficulty: 'Intermediate'\n            },\n            'Cross-Platform': {\n              description: 'Create applications that run on multiple platforms with shared codebases.',\n              topics: [\n                'React Native Apps - JavaScript-based mobile development',\n                'Flutter Applications - Dart-based cross-platform framework',\n                'Xamarin Solutions - C# cross-platform development',\n                'Ionic Apps - Web-based mobile applications'\n              ],\n              techStack: ['React Native', 'Flutter', 'Dart', 'JavaScript', 'Cordova'],\n              difficulty: 'Advanced'\n            }\n          }\n        },\n        'AI & Machine Learning': {\n          icon: <Brain className=\"w-5 h-5\" />,\n          subcategories: {\n            'Natural Language Processing': {\n              description: 'Develop intelligent systems that understand and process human language.',\n              topics: [\n                'Text Analysis Tools - Sentiment and emotion analysis',\n                'Language Translation - Real-time translation services',\n                'Chatbots & Virtual Assistants - Conversational AI systems',\n                'Content Generation - AI-powered writing tools'\n              ],\n              techStack: ['Python', 'TensorFlow', 'spaCy', 'NLTK', 'Transformers'],\n              difficulty: 'Advanced'\n            },\n            'Computer Vision': {\n              description: 'Build systems that can interpret and understand visual information.',\n              topics: [\n                'Image Recognition - Object and pattern detection',\n                'Facial Recognition - Identity verification systems',\n                'Medical Imaging - Healthcare diagnostic tools',\n                'Augmented Reality - Real-world overlay applications'\n              ],\n              techStack: ['OpenCV', 'TensorFlow', 'PyTorch', 'YOLO', 'MediaPipe'],\n              difficulty: 'Expert'\n            }\n          }\n        }\n      }\n    },\n    'Final Preparation': {\n      icon: <FileText className=\"w-6 h-6\" />,\n      color: 'bg-orange-500',\n      bgGradient: 'from-orange-400 to-orange-600',\n      categories: {\n        'Documentation': {\n          icon: <FileText className=\"w-5 h-5\" />,\n          subcategories: {\n            'Project Documentation': {\n              description: 'Create comprehensive documentation for your hackathon project.',\n              topics: [\n                'README Creation - Project overview and setup instructions',\n                'API Documentation - Endpoint descriptions and examples',\n                'User Guides - Step-by-step usage instructions',\n                'Technical Architecture - System design documentation'\n              ],\n              techStack: ['Markdown', 'GitBook', 'Swagger', 'Docusaurus', 'Notion'],\n              difficulty: 'Intermediate'\n            }\n          }\n        }\n      }\n    },\n    'Judging & Awards': {\n      icon: <Award className=\"w-6 h-6\" />,\n      color: 'bg-purple-500',\n      bgGradient: 'from-purple-400 to-purple-600',\n      categories: {\n        'Presentation': {\n          icon: <Presentation className=\"w-5 h-5\" />,\n          subcategories: {\n            'Live Pitching': {\n              description: 'Present your solution to judges and showcase your innovation.',\n              topics: [\n                'Pitch Delivery - Confident presentation techniques',\n                'Demo Execution - Flawless product demonstration',\n                'Judge Interaction - Professional Q&A handling',\n                'Time Management - Effective use of presentation time'\n              ],\n              techStack: ['Presentation Tools', 'Screen Sharing', 'Video Conferencing', 'Demo Environments'],\n              difficulty: 'Advanced'\n            },\n            'PowerPoint Presentation': {\n              \"component\":<PowerPointerGenerator isDarkMode={isDarkMode}/>\n            }\n          }\n        },\n        'Post Hackathon': {\n          icon: <Presentation className=\"w-5 h-5\" />,\n          subcategories: {\n            'LinkedIn Post': {\n              \"component\":<LinkedInPostGenerator isDarkMode={isDarkMode}/>\n            }\n          }\n        }\n      }\n    }\n  };\n\n  const currentStage = stages[selectedStage];\n  const currentCategory = currentStage?.categories[selectedCategory];\n  const currentSubcategory = currentCategory?.subcategories[selectedSubcategory];\n\n  // Resize functionality\n  useEffect(() => {\n    const handleMouseMove = (e) => {\n      if (!isResizing) return;\n      \n      const newWidth = e.clientX - 56; // 56px is the width of the leftmost sidebar (w-14)\n      const minWidth = 200;\n      const maxWidth = 500;\n      \n      if (newWidth >= minWidth && newWidth <= maxWidth) {\n        setSidebarWidth(newWidth);\n      }\n    };\n\n    const handleMouseUp = () => {\n      setIsResizing(false);\n      document.body.style.cursor = 'default';\n      document.body.style.userSelect = 'auto';\n    };\n\n    if (isResizing) {\n      document.body.style.cursor = 'col-resize';\n      document.body.style.userSelect = 'none';\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n    }\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n  }, [isResizing]);\n\n  const handleResizeStart = (e) => {\n    e.preventDefault();\n    setIsResizing(true);\n  };\n\n  const getDifficultyColor = (difficulty) => {\n    switch(difficulty) {\n      case 'Beginner': return 'bg-green-100 text-green-800';\n      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';\n      case 'Advanced': return 'bg-orange-100 text-orange-800';\n      case 'Expert': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const handleStageChange = (stageName) => {\n    setSelectedStage(stageName);\n    setActiveLeftPanel('home'); // Auto-navigate to home when stage changes\n    const firstCategory = Object.keys(stages[stageName].categories)[0];\n    setSelectedCategory(firstCategory);\n    const firstSubcategory = Object.keys(stages[stageName].categories[firstCategory].subcategories)[0];\n    setSelectedSubcategory(firstSubcategory);\n  };\n\n  const handleCategoryChange = (categoryName) => {\n    setSelectedCategory(categoryName);\n    const firstSubcategory = Object.keys(currentStage.categories[categoryName].subcategories)[0];\n    setSelectedSubcategory(firstSubcategory);\n  };\n\n  return (\n    <div className={`h-screen flex flex-col overflow-hidden ${isDarkMode ? 'bg-gray-900' : 'bg-white'} transition-all duration-300 font-sans`}>\n      <style jsx>{`\n        .custom-scrollbar {\n          scrollbar-width: none;\n          -ms-overflow-style: none;\n        }\n        .custom-scrollbar::-webkit-scrollbar {\n          display: none;\n        }\n        .resize-handle {\n          position: absolute;\n          top: 0;\n          right: 0;\n          width: 4px;\n          height: 100%;\n          background: transparent;\n          cursor: col-resize;\n          z-index: 10;\n        }\n        .resize-handle:hover {\n          background: rgba(59, 130, 246, 0.5);\n        }\n        .resize-handle.resizing {\n          background: rgba(59, 130, 246, 0.8);\n        }\n      `}</style>\n      \n      {/* Main Header */}\n      <div \n        className={`${isDarkMode ? 'border-gray-700' : 'border-b'} px-6 py-3 shadow-sm flex-shrink-0`}\n        style={{\n          backgroundColor: isDarkMode ? '#1f2937' : currentTheme.primary,\n          borderColor: isDarkMode ? '#374151' : currentTheme.primaryDark\n        }}\n      >\n        <div className=\"flex items-center justify-between\">\n          {/* Left - HackHelp Logo */}\n          <div className=\"flex items-center space-x-3\">\n            <div \n              className={`w-8 h-8 ${isDarkMode ? 'bg-gray-700' : 'bg-white'} rounded-lg flex items-center justify-center`}\n            >\n              <Trophy \n                className={`w-5 h-5`}\n                style={{\n                  color: isDarkMode ? '#9ca3af' : currentTheme.primary\n                }}\n              />\n            </div>\n            <span className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-white'}`}>HackHelp</span>\n          </div>\n\n          {/* Center - Project Name */}\n          <div className=\"flex-1 flex justify-center\">\n            <div className=\"flex items-center space-x-2\">\n              <h1 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-white'}`}>\n                Hackathon Project Guide\n              </h1>\n            </div>\n          </div>\n\n          {/* Right - Settings, Toggle, Profile */}\n          <div className=\"flex items-center space-x-3\">\n            {/* Search */}\n            <div className=\"relative\">\n              <Search className={`w-5 h-5 ${isDarkMode ? 'text-gray-400' : 'text-white/70'} absolute left-3 top-1/2 transform -translate-y-1/2`} />\n              <input\n                type=\"text\"\n                placeholder=\"Search everything...\"\n                className={`pl-10 pr-4 py-2 rounded-lg border text-sm w-64 focus:outline-none focus:ring-2 ${\n                  isDarkMode \n                    ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-gray-500 focus:ring-gray-500/20' \n                    : 'border-white/20 text-white placeholder-white/70 focus:border-white focus:ring-white/20'\n                }`}\n                style={{\n                  backgroundColor: isDarkMode ? '#374151' : `${currentTheme.primary}dd`,\n                  borderColor: isDarkMode ? '#4b5563' : 'rgba(255,255,255,0.2)'\n                }}\n              />\n              <div className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-xs ${isDarkMode ? 'text-gray-500' : 'text-white/60'}`}>\n                Ctrl + K\n              </div>\n            </div>\n\n            {/* Notifications */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowNotifications(!showNotifications)}\n                className={`p-2 rounded-lg transition-colors ${\n                  isDarkMode \n                    ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' \n                    : 'text-white/80 hover:text-white'\n                }`}\n                onMouseEnter={(e) => {\n                  if (!isDarkMode) {\n                    e.target.style.backgroundColor = currentTheme.primaryDark;\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  if (!isDarkMode) {\n                    e.target.style.backgroundColor = 'transparent';\n                  }\n                }}\n              >\n                <Bell className=\"w-5 h-5\" />\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"></div>\n              </button>\n              \n              {/* Notification Tooltip */}\n              {showNotifications && (\n                <div className={`absolute right-0 top-full mt-2 w-80 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg shadow-lg border z-50`}>\n                  <div className={`p-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>\n                    <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Notifications</h3>\n                  </div>\n                  <div className=\"p-4 space-y-3\">\n                    <div className=\"text-sm\">\n                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>New hackathon starting soon!</div>\n                      <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>AI/ML Challenge 2025 registration open</div>\n                    </div>\n                    <div className=\"text-sm\">\n                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Team invitation received</div>\n                      <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>You've been invited to join \"Code Warriors\"</div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Dark Mode Toggle */}\n            <button\n              onClick={() => setIsDarkMode(!isDarkMode)}\n              className={`p-2 rounded-lg transition-colors ${\n                isDarkMode \n                  ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' \n                  : 'text-white/80 hover:text-white'\n              }`}\n              onMouseEnter={(e) => {\n                if (!isDarkMode) {\n                  e.target.style.backgroundColor = currentTheme.primaryDark;\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (!isDarkMode) {\n                  e.target.style.backgroundColor = 'transparent';\n                }\n              }}\n            >\n              {isDarkMode ? <Sun className=\"w-5 h-5\" /> : <Moon className=\"w-5 h-5\" />}\n            </button>\n\n            {/* Apps Grid */}\n            <button \n              className={`p-2 rounded-lg transition-colors ${\n                isDarkMode \n                  ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' \n                  : 'text-white/80 hover:text-white'\n              }`}\n              onMouseEnter={(e) => {\n                if (!isDarkMode) {\n                  e.target.style.backgroundColor = currentTheme.primaryDark;\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (!isDarkMode) {\n                  e.target.style.backgroundColor = 'transparent';\n                }\n              }}\n            >\n              <Grid3X3 className=\"w-5 h-5\" />\n            </button>\n\n            {/* Settings */}\n            <button \n              className={`p-2 rounded-lg transition-colors ${\n                isDarkMode \n                  ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' \n                  : 'text-white/80 hover:text-white'\n              }`}\n              onMouseEnter={(e) => {\n                if (!isDarkMode) {\n                  e.target.style.backgroundColor = currentTheme.primaryDark;\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (!isDarkMode) {\n                  e.target.style.backgroundColor = 'transparent';\n                }\n              }}\n            >\n              <Settings className=\"w-5 h-5\" />\n            </button>\n\n            {/* Profile */}\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-sm font-medium\">PY</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex flex-1 overflow-hidden \">\n        {/* Leftmost Sidebar - Always Available Features */}\n        <div \n          className={`w-14 border-r flex flex-col items-center py-3 space-y-1 flex-shrink-0 m-2 rounded-xl`}\n          style={{\n            backgroundColor: isDarkMode ? '#111827' : currentTheme.primaryDark,\n            borderColor: isDarkMode ? '#374151' : currentTheme.primaryDark\n          }}\n        >\n          {/* HackHelp Logo */}\n          <div className={`w-8 h-8 ${isDarkMode ? 'bg-gray-700' : 'bg-white'} rounded-lg flex items-center justify-center mb-3`}>\n            <span \n              className=\"font-bold text-sm\"\n              style={{ color: isDarkMode ? '#9ca3af' : currentTheme.primary }}\n            >H</span>\n          </div>\n\n          {/* Main Navigation Icons */}\n          <div className=\"flex flex-col space-y-2\">\n            {/* Home */}\n            <div className=\"relative group\">\n              <button\n                onClick={() => setActiveLeftPanel('home')}\n                onMouseEnter={() => setHoveredIcon('sidebar-home')}\n                onMouseLeave={() => setHoveredIcon(null)}\n                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${\n                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'\n                }`}\n              >\n                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${\n                  activeLeftPanel === 'home'\n                    ? isDarkMode ? 'bg-white/20' : 'bg-white'\n                    : 'hover:bg-white/10'\n                }`}>\n                  <Home \n                    className=\"w-4 h-4\" \n                    style={activeLeftPanel === 'home' && !isDarkMode ? { color: currentTheme.primary } : {}}\n                  />\n                </div>\n                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'home' ? 'font-semibold' : ''}`}>Home</span>\n              </button>\n              {hoveredIcon === 'sidebar-home' && (\n                <div className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\">\n                  Home\n                  <div className=\"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"></div>\n                </div>\n              )}\n            </div>\n\n            {/* Inbox */}\n            <div className=\"relative group\">\n              <button\n                onClick={() => setActiveLeftPanel('inbox')}\n                onMouseEnter={() => setHoveredIcon('sidebar-inbox')}\n                onMouseLeave={() => setHoveredIcon(null)}\n                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${\n                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'\n                }`}\n              >\n                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${\n                  activeLeftPanel === 'inbox'\n                    ? isDarkMode ? 'bg-white/20' : 'bg-white'\n                    : 'hover:bg-white/10'\n                }`}>\n                  <Inbox \n                    className=\"w-4 h-4\" \n                    style={activeLeftPanel === 'inbox' && !isDarkMode ? { color: currentTheme.primary } : {}}\n                  />\n                </div>\n                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'inbox' ? 'font-semibold' : ''}`}>Inbox</span>\n              </button>\n              {hoveredIcon === 'sidebar-inbox' && (\n                <div className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\">\n                  Inbox\n                  <div className=\"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"></div>\n                </div>\n              )}\n            </div>\n\n            {/* Calendar */}\n            <div className=\"relative group\">\n              <button\n                onClick={() => setActiveLeftPanel('calendar')}\n                onMouseEnter={() => setHoveredIcon('sidebar-calendar')}\n                onMouseLeave={() => setHoveredIcon(null)}\n                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${\n                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'\n                }`}\n              >\n                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${\n                  activeLeftPanel === 'calendar'\n                    ? isDarkMode ? 'bg-white/20' : 'bg-white'\n                    : 'hover:bg-white/10'\n                }`}>\n                  <Calendar \n                    className=\"w-4 h-4\" \n                    style={activeLeftPanel === 'calendar' && !isDarkMode ? { color: currentTheme.primary } : {}}\n                  />\n                </div>\n                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'calendar' ? 'font-semibold' : ''}`}>Calendar</span>\n              </button>\n              {hoveredIcon === 'sidebar-calendar' && (\n                <div className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\">\n                  Calendar\n                  <div className=\"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"></div>\n                </div>\n              )}\n            </div>\n\n            {/* Brain (AI Chat) */}\n            <div className=\"relative group\">\n              <button\n                onClick={() => setActiveLeftPanel('chat')}\n                onMouseEnter={() => setHoveredIcon('sidebar-brain')}\n                onMouseLeave={() => setHoveredIcon(null)}\n                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors relative ${\n                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'\n                }`}\n              >\n                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${\n                  activeLeftPanel === 'chat'\n                    ? isDarkMode ? 'bg-white/20' : 'bg-white'\n                    : 'hover:bg-white/10'\n                }`}>\n                  <Brain \n                    className=\"w-4 h-4\" \n                    style={activeLeftPanel === 'chat' && !isDarkMode ? { color: currentTheme.primary } : {}}\n                  />\n                </div>\n                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'chat' ? 'font-semibold' : ''}`}>Brain</span>\n                <div className=\"absolute top-0 right-0 w-3 h-3 bg-green-500 rounded-full\"></div>\n              </button>\n              {hoveredIcon === 'sidebar-brain' && (\n                <div className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\">\n                  Brain (AI Chat)\n                  <div className=\"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"></div>\n                </div>\n              )}\n            </div>\n\n            {/* Team Chat */}\n            <div className=\"relative group\">\n              <button\n                onClick={() => setActiveLeftPanel('teamchat')}\n                onMouseEnter={() => setHoveredIcon('sidebar-teamchat')}\n                onMouseLeave={() => setHoveredIcon(null)}\n                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${\n                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'\n                }`}\n              >\n                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors relative ${\n                  activeLeftPanel === 'teamchat'\n                    ? isDarkMode ? 'bg-white/20' : 'bg-white'\n                    : 'hover:bg-white/10'\n                }`}>\n                  <MessageCircle \n                    className=\"w-4 h-4\" \n                    style={activeLeftPanel === 'teamchat' && !isDarkMode ? { color: currentTheme.primary } : {}}\n                  />\n                  <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xs font-bold\">3</span>\n                  </div>\n                </div>\n                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'teamchat' ? 'font-semibold' : ''}`}>Chat</span>\n              </button>\n              {hoveredIcon === 'sidebar-teamchat' && (\n                <div className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\">\n                  Team Chat\n                  <div className=\"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"></div>\n                </div>\n              )}\n            </div>\n\n            {/* Tasks */}\n            <div className=\"relative group\">\n              <button\n                onClick={() => setActiveLeftPanel('tasks')}\n                onMouseEnter={() => setHoveredIcon('sidebar-tasks')}\n                onMouseLeave={() => setHoveredIcon(null)}\n                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${\n                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'\n                }`}\n              >\n                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${\n                  activeLeftPanel === 'tasks'\n                    ? isDarkMode ? 'bg-white/20' : 'bg-white'\n                    : 'hover:bg-white/10'\n                }`}>\n                  <ListTodo \n                    className=\"w-4 h-4\" \n                    style={activeLeftPanel === 'tasks' && !isDarkMode ? { color: currentTheme.primary } : {}}\n                  />\n                </div>\n                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'tasks' ? 'font-semibold' : ''}`}>Tasks</span>\n              </button>\n              {hoveredIcon === 'sidebar-tasks' && (\n                <div className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\">\n                  Tasks\n                  <div className=\"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"></div>\n                </div>\n              )}\n            </div>\n\n            {/* Goals */}\n            <div className=\"relative group\">\n              <button\n                onClick={() => setActiveLeftPanel('goals')}\n                onMouseEnter={() => setHoveredIcon('sidebar-goals')}\n                onMouseLeave={() => setHoveredIcon(null)}\n                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${\n                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'\n                }`}\n              >\n                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${\n                  activeLeftPanel === 'goals'\n                    ? isDarkMode ? 'bg-white/20' : 'bg-white'\n                    : 'hover:bg-white/10'\n                }`}>\n                  <Goal \n                    className=\"w-4 h-4\" \n                    style={activeLeftPanel === 'goals' && !isDarkMode ? { color: currentTheme.primary } : {}}\n                  />\n                </div>\n                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'goals' ? 'font-semibold' : ''}`}>Goals</span>\n              </button>\n              {hoveredIcon === 'sidebar-goals' && (\n                <div className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\">\n                  Goals\n                  <div className=\"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"></div>\n                </div>\n              )}\n            </div>\n\n            {/* Timesheet */}\n            <div className=\"relative group\">\n              <button\n                onClick={() => setActiveLeftPanel('timer')}\n                onMouseEnter={() => setHoveredIcon('sidebar-timesheet')}\n                onMouseLeave={() => setHoveredIcon(null)}\n                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${\n                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'\n                }`}\n              >\n                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${\n                  activeLeftPanel === 'timer'\n                    ? isDarkMode ? 'bg-white/20' : 'bg-white'\n                    : 'hover:bg-white/10'\n                }`}>\n                  <Timer \n                    className=\"w-4 h-4\" \n                    style={activeLeftPanel === 'timer' && !isDarkMode ? { color: currentTheme.primary } : {}}\n                  />\n                </div>\n                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'timer' ? 'font-semibold' : ''}`}>Timesheet</span>\n              </button>\n              {hoveredIcon === 'sidebar-timesheet' && (\n                <div className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\">\n                  Timesheet\n                  <div className=\"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"></div>\n                </div>\n              )}\n            </div>\n\n            {/* More */}\n            <div className=\"relative group\">\n              <button\n                onMouseEnter={() => setHoveredIcon('sidebar-more')}\n                onMouseLeave={() => setHoveredIcon(null)}\n                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${\n                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'\n                }`}\n              >\n                <div className=\"w-8 h-8 rounded-lg flex items-center justify-center transition-colors hover:bg-white/10\">\n                  <MoreHorizontal className=\"w-4 h-4\" />\n                </div>\n                <span className=\"text-xs mt-0.5\">More</span>\n              </button>\n              {hoveredIcon === 'sidebar-more' && (\n                <div className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\">\n                  More Apps\n                  <div className=\"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"></div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Bottom Section */}\n          <div className=\"flex-1\"></div>\n          \n          {/* Invite */}\n          <div className=\"relative group\">\n            <button\n              onMouseEnter={() => setHoveredIcon('sidebar-invite')}\n              onMouseLeave={() => setHoveredIcon(null)}\n              className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${\n                isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'\n              }`}\n            >\n              <div className=\"w-8 h-8 rounded-lg flex items-center justify-center transition-colors hover:bg-white/10\">\n                <UserPlus className=\"w-4 h-4\" />\n              </div>\n              <span className=\"text-xs mt-0.5\">Invite</span>\n            </button>\n            {hoveredIcon === 'sidebar-invite' && (\n              <div className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50\">\n                Invite Members\n                <div className=\"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45\"></div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Main Content Area */}\n        <div className=\"flex-1 flex overflow-hidden\">\n          {/* Categories/Stages Sidebar - Now Resizable */}\n          <div \n            ref={sidebarRef}\n            className={`border-r flex flex-col flex-shrink-0 relative`}\n            style={{ \n              width: `${sidebarWidth}px`,\n              backgroundColor: isDarkMode ? '#1f2937' : '#f9fafb',\n              borderColor: isDarkMode ? '#374151' : '#e5e7eb'\n            }}\n          >\n            {/* Resize Handle */}\n            <div\n              ref={resizeRef}\n              className={`resize-handle ${isResizing ? 'resizing' : ''}`}\n              onMouseDown={handleResizeStart}\n            />\n\n            {activeLeftPanel === 'home' ? (\n              <div className=\"flex-1 overflow-y-auto custom-scrollbar\">\n                {/* Hackathon Stages Section */}\n                <div className={`p-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h2 className={`text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>Hackathon Stages</h2>\n                    <button className={`${isDarkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`}>\n                      <MoreHorizontal className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                  \n                  {/* Progress Bar */}\n                  <div className=\"mb-4\">\n                    <div className={`flex justify-between text-xs mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                      <span>Progress</span>\n                      <span>{Math.round(((Object.keys(stages).indexOf(selectedStage) + 1) / Object.keys(stages).length) * 100)}%</span>\n                    </div>\n                    <div className={`w-full rounded-full h-2 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>\n                      <div \n                        className=\"h-2 rounded-full transition-all duration-700 ease-out\"\n                        style={{\n                          backgroundColor: currentTheme.primary,\n                          width: `${((Object.keys(stages).indexOf(selectedStage) + 1) / Object.keys(stages).length) * 100}%`\n                        }}\n                      ></div>\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-1\">\n                    {Object.entries(stages).map(([stageName, stageData]) => {\n                      const isActive = selectedStage === stageName;\n                      const isPast = Object.keys(stages).indexOf(selectedStage) > Object.keys(stages).indexOf(stageName);\n                      \n                      return (\n                        <button\n                          key={stageName}\n                          onClick={() => handleStageChange(stageName)}\n                          className={`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-3 ${\n                            isActive\n                              ? 'font-medium'\n                              : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'\n                          }`}\n                          style={{\n                            backgroundColor: isActive ? currentTheme.accent : 'transparent',\n                            color: isActive ? currentTheme.accentText : (isDarkMode ? '#d1d5db' : '#374151')\n                          }}\n                        >\n                          <div className=\"flex items-center justify-center w-5 h-5\">\n                            {isPast && !isActive ? (\n                              <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                            ) : (\n                              React.cloneElement(stageData.icon, { \n                                className: `w-4 h-4`,\n                                style: {\n                                  color: isActive ? currentTheme.accentText : '#6b7280'\n                                }\n                              })\n                            )}\n                          </div>\n                          <span>{stageName}</span>\n                        </button>\n                      );\n                    })}\n                  </div>\n                </div>\n\n                {/* Categories Section */}\n                <div className=\"p-4\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h2 className={`text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>Categories</h2>\n                    <button className={`${isDarkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`}>\n                      <MoreHorizontal className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                  <div className=\"space-y-1\">\n                    {Object.entries(currentStage?.categories || {}).map(([categoryName, categoryData]) => (\n                      <button\n                        key={categoryName}\n                        onClick={() => handleCategoryChange(categoryName)}\n                        className={`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-3 ${\n                          selectedCategory === categoryName\n                            ? 'font-medium'\n                            : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'\n                        }`}\n                        style={{\n                          backgroundColor: selectedCategory === categoryName ? currentTheme.accent : 'transparent',\n                          color: selectedCategory === categoryName ? currentTheme.accentText : (isDarkMode ? '#d1d5db' : '#374151')\n                        }}\n                      >\n                        <div className=\"flex items-center justify-center w-5 h-5\">\n                          {categoryData.icon}\n                        </div>\n                        <span>{categoryName}</span>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Subcategories */}\n                {currentCategory && (\n                  <div className={`border-t p-4 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>\n                    <h3 className={`text-xs font-semibold uppercase tracking-wide mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                      Subcategories\n                    </h3>\n                    <div className=\"space-y-1\">\n                      {Object.keys(currentCategory.subcategories).map((subcategoryName) => (\n                        <button\n                          key={subcategoryName}\n                          onClick={() => setSelectedSubcategory(subcategoryName)}\n                          className={`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center justify-between ${\n                            selectedSubcategory === subcategoryName\n                              ? 'font-medium'\n                              : isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'\n                          }`}\n                          style={{\n                            backgroundColor: selectedSubcategory === subcategoryName ? currentTheme.accent : 'transparent',\n                            color: selectedSubcategory === subcategoryName ? currentTheme.accentText : (isDarkMode ? '#9ca3af' : '#4b5563')\n                          }}\n                        >\n                          <span>{subcategoryName}</span>\n                          <ChevronRight className=\"w-3 h-3\" />\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"flex-1 overflow-y-auto custom-scrollbar\">\n                <div className=\"p-4\">\n                  {/* Dynamic Content based on activeLeftPanel */}\n                  {activeLeftPanel === 'inbox' && (\n                    <div>\n                      <h2 className=\"text-sm font-semibold text-gray-900 mb-4\">Inbox</h2>\n                      <div className=\"space-y-3\">\n                        <div className=\"p-3 rounded-lg bg-white border\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                            <span className=\"text-sm font-medium text-gray-900\">New Task Assignment</span>\n                            <span className=\"text-xs text-gray-500\">2h ago</span>\n                          </div>\n                          <p className=\"text-sm text-gray-700\">\n                            You've been assigned to work on the frontend components.\n                          </p>\n                        </div>\n                        <div className=\"p-3 rounded-lg bg-white border\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                            <span className=\"text-sm font-medium text-gray-900\">Code Review Completed</span>\n                            <span className=\"text-xs text-gray-500\">4h ago</span>\n                          </div>\n                          <p className=\"text-sm text-gray-700\">\n                            Your pull request has been approved and merged.\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {activeLeftPanel === 'chat' && (\n                    <div>\n                      <h2 className=\"text-sm font-semibold text-gray-900 mb-4\">AI Brain Chat</h2>\n                      <div className=\"space-y-3\">\n                        <div className=\"p-3 rounded-lg bg-white border\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <div className=\"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\">\n                              <Brain className=\"w-3 h-3 text-white\" />\n                            </div>\n                            <span className=\"text-sm font-medium text-gray-900\">AI Assistant</span>\n                            <span className=\"text-xs text-gray-500\">now</span>\n                          </div>\n                          <p className=\"text-sm text-gray-700\">\n                            How can I help you with your hackathon project today?\n                          </p>\n                        </div>\n                        <div className=\"p-3 rounded-lg bg-blue-50 border border-blue-200\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <div className=\"w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center\">\n                              <span className=\"text-white text-xs\">You</span>\n                            </div>\n                            <span className=\"text-sm font-medium text-gray-900\">You</span>\n                            <span className=\"text-xs text-gray-500\">1m ago</span>\n                          </div>\n                          <p className=\"text-sm text-gray-700\">\n                            I need help with React component optimization.\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"mt-4\">\n                        <input\n                          type=\"text\"\n                          placeholder=\"Ask AI Brain anything...\"\n                          className=\"w-full px-3 py-2 rounded border text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        />\n                      </div>\n                    </div>\n                  )}\n\n                  {activeLeftPanel === 'calendar' && (\n                    <div>\n                      <h2 className=\"text-sm font-semibold text-gray-900 mb-4\">Calendar</h2>\n                      <div className=\"space-y-3\">\n                        <div className=\"p-3 rounded bg-white border\">\n                          <div className=\"text-sm font-medium text-blue-600\">Today - 10:00 AM</div>\n                          <div className=\"text-sm text-gray-900 mt-1\">Team Standup Meeting</div>\n                          <div className=\"text-xs text-gray-500 mt-1\">15 minutes</div>\n                        </div>\n                        <div className=\"p-3 rounded bg-white border\">\n                          <div className=\"text-sm font-medium text-green-600\">Today - 2:00 PM</div>\n                          <div className=\"text-sm text-gray-900 mt-1\">Code Review Session</div>\n                          <div className=\"text-xs text-gray-500 mt-1\">1 hour</div>\n                        </div>\n                        <div className=\"p-3 rounded bg-white border\">\n                          <div className=\"text-sm font-medium text-purple-600\">Tomorrow - 9:00 AM</div>\n                          <div className=\"text-sm text-gray-900 mt-1\">Hackathon Presentation</div>\n                          <div className=\"text-xs text-gray-500 mt-1\">30 minutes</div>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {activeLeftPanel === 'timer' && (\n                    <div>\n                      <h2 className=\"text-sm font-semibold text-gray-900 mb-4\">Time Tracker</h2>\n                      <div className=\"text-center mb-6\">\n                        <div className=\"text-2xl font-mono text-gray-900 mb-2\">02:34:18</div>\n                        <div className=\"text-sm text-gray-500\">Working on: Frontend Development</div>\n                      </div>\n                      <div className=\"space-y-3\">\n                        <button className=\"w-full py-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors text-sm\">\n                          Start Timer\n                        </button>\n                        <button className=\"w-full py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors text-sm\">\n                          Stop Timer\n                        </button>\n                      </div>\n                      <div className=\"mt-6\">\n                        <h3 className=\"text-xs font-semibold text-gray-500 mb-3\">Today's Sessions</h3>\n                        <div className=\"space-y-2\">\n                          <div className=\"p-2 rounded bg-white border\">\n                            <div className=\"text-sm text-gray-900\">Backend Development</div>\n                            <div className=\"text-xs text-gray-500\">1h 23m</div>\n                          </div>\n                          <div className=\"p-2 rounded bg-white border\">\n                            <div className=\"text-sm text-gray-900\">Code Review</div>\n                            <div className=\"text-xs text-gray-500\">45m</div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {activeLeftPanel === 'teamchat' && (\n                    <div className=\"flex flex-col h-full\">\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <h2 className={`text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>Team Chat</h2>\n                        <div className=\"flex items-center space-x-2\">\n                          <div className=\"flex items-center space-x-1\">\n                            <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                            <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                              {teamMembers.length} online\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Team Members Online */}\n                      <div className=\"mb-4\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <span className={`text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Team Members</span>\n                        </div>\n                        <div className=\"flex flex-wrap gap-1\">\n                          {teamMembers.map((member) => (\n                            <div key={member.id} className=\"flex items-center space-x-1\">\n                              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs relative ${member.color}`}>\n                                {member.avatar}\n                                <div className=\"absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-green-500 rounded-full border border-white\"></div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n\n                      {/* Quick Actions */}\n                      <div className=\"mb-4 grid grid-cols-2 gap-2\">\n                        <button \n                          className=\"px-3 py-2 rounded text-xs font-medium transition-colors text-white\"\n                          style={{ backgroundColor: currentTheme.primary }}\n                        >\n                          📢 Announce\n                        </button>\n                        <button className={`px-3 py-2 rounded text-xs font-medium transition-colors ${\n                          isDarkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                        }`}>\n                          📎 Share File\n                        </button>\n                      </div>\n\n                      {/* Recent Activity */}\n                      <div className=\"mb-4\">\n                        <h3 className={`text-xs font-semibold uppercase tracking-wide mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                          Recent Activity\n                        </h3>\n                        <div className=\"space-y-2\">\n                          <div className={`text-xs p-2 rounded ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>\n                            <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Sarah</span>\n                            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}> completed \"Setup Database\"</span>\n                          </div>\n                          <div className={`text-xs p-2 rounded ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>\n                            <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Mike</span>\n                            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}> shared a file</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Message Count */}\n                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} text-center`}>\n                        {chatMessages.length} messages today\n                      </div>\n                    </div>\n                  )}\n\n                  {activeLeftPanel === 'tasks' && (\n                    <div>\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <h2 className={`text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>Tasks</h2>\n                        <button \n                          onClick={() => setIsLeader(!isLeader)}\n                          className={`px-3 py-1 rounded text-xs font-medium transition-colors ${\n                            isLeader \n                              ? 'text-white'\n                              : isDarkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                          }`}\n                          style={isLeader ? { backgroundColor: currentTheme.primary } : {}}\n                        >\n                          {isLeader ? 'Leader' : 'Member'}\n                        </button>\n                      </div>\n\n                      {/* Member filter for leaders */}\n                      {isLeader && (\n                        <div className=\"mb-4\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <span className={`text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Filter by:</span>\n                          </div>\n                          <div className=\"space-y-1\">\n                            <button\n                              onClick={() => setSelectedMember('All Members')}\n                              className={`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-2 ${\n                                selectedMember === 'All Members'\n                                  ? 'text-white font-medium'\n                                  : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'\n                              }`}\n                              style={selectedMember === 'All Members' ? { backgroundColor: currentTheme.accent, color: currentTheme.accentText } : {}}\n                            >\n                              <Users className=\"w-4 h-4\" />\n                              <span>All Members</span>\n                            </button>\n                            {teamMembers.map((member) => (\n                              <button\n                                key={member.id}\n                                onClick={() => setSelectedMember(member.name)}\n                                className={`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-2 ${\n                                  selectedMember === member.name\n                                    ? 'text-white font-medium'\n                                    : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'\n                                }`}\n                                style={selectedMember === member.name ? { backgroundColor: currentTheme.accent, color: currentTheme.accentText } : {}}\n                              >\n                                <div className={`w-5 h-5 rounded-full flex items-center justify-center text-white text-xs ${member.color}`}>\n                                  {member.avatar}\n                                </div>\n                                <span>{member.name}</span>\n                              </button>\n                            ))}\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Task Status Counts */}\n                      <div className=\"mb-4 grid grid-cols-3 gap-2\">\n                        <div className={`p-2 rounded border text-center ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border'}`}\n                             style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '30' } : {}}>\n                          <div className=\"text-xs text-gray-500\">To Do</div>\n                          <div className={`text-sm font-semibold`} style={{ color: currentTheme.primary }}>\n                            {getFilteredTasks().filter(t => t.status === 'todo').length}\n                          </div>\n                        </div>\n                        <div className={`p-2 rounded border text-center ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border'}`}\n                             style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '30' } : {}}>\n                          <div className=\"text-xs text-gray-500\">In Progress</div>\n                          <div className={`text-sm font-semibold`} style={{ color: currentTheme.primary }}>\n                            {getFilteredTasks().filter(t => t.status === 'in-progress').length}\n                          </div>\n                        </div>\n                        <div className={`p-2 rounded border text-center ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border'}`}\n                             style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '30' } : {}}>\n                          <div className=\"text-xs text-gray-500\">Done</div>\n                          <div className={`text-sm font-semibold`} style={{ color: currentTheme.primary }}>\n                            {getFilteredTasks().filter(t => t.status === 'completed').length}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Quick Actions */}\n                      <div className=\"space-y-2\">\n                        <button className=\"w-full py-2 rounded border border-dashed border-gray-300 text-gray-500 hover:bg-gray-50 transition-colors text-sm\">\n                          <Plus className=\"w-4 h-4 inline mr-2\" />\n                          Add Task\n                        </button>\n                        \n                        <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} text-center`}>\n                          {isLeader && selectedMember !== 'All Members' \n                            ? `Showing ${selectedMember}'s tasks`\n                            : `${getFilteredTasks().length} tasks total`\n                          }\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {activeLeftPanel === 'goals' && (\n                    <div>\n                      <h2 className=\"text-sm font-semibold text-gray-900 mb-4\">Goals</h2>\n                      <div className=\"space-y-3\">\n                        <div className=\"p-3 rounded border-l-4 border-green-500 bg-white border-t border-r border-b\">\n                          <div className=\"text-sm font-medium text-gray-900\">Complete MVP</div>\n                          <div className=\"text-xs text-gray-500 mt-1\">Due: End of hackathon</div>\n                          <div className=\"flex items-center mt-2\">\n                            <div className=\"w-full bg-gray-200 rounded-full h-1.5 mr-2\">\n                              <div className=\"bg-green-500 h-1.5 rounded-full\" style={{width: '75%'}}></div>\n                            </div>\n                            <span className=\"text-xs text-green-600\">75%</span>\n                          </div>\n                        </div>\n                        <div className=\"p-3 rounded border-l-4 border-blue-500 bg-white border-t border-r border-b\">\n                          <div className=\"text-sm font-medium text-gray-900\">Learn React Hooks</div>\n                          <div className=\"text-xs text-gray-500 mt-1\">Personal goal</div>\n                          <div className=\"flex items-center mt-2\">\n                            <div className=\"w-full bg-gray-200 rounded-full h-1.5 mr-2\">\n                              <div className=\"bg-blue-500 h-1.5 rounded-full\" style={{width: '60%'}}></div>\n                            </div>\n                            <span className=\"text-xs text-blue-600\">60%</span>\n                          </div>\n                        </div>\n                        <div className=\"p-3 rounded border-l-4 border-purple-500 bg-white border-t border-r border-b\">\n                          <div className=\"text-sm font-medium text-gray-900\">Team Collaboration</div>\n                          <div className=\"text-xs text-gray-500 mt-1\">Soft skill development</div>\n                          <div className=\"flex items-center mt-2\">\n                            <div className=\"w-full bg-gray-200 rounded-full h-1.5 mr-2\">\n                              <div className=\"bg-purple-500 h-1.5 rounded-full\" style={{width: '90%'}}></div>\n                            </div>\n                            <span className=\"text-xs text-purple-600\">90%</span>\n                          </div>\n                        </div>\n                      </div>\n                      <button className=\"mt-4 w-full py-2 rounded border border-dashed border-gray-300 text-gray-500 hover:bg-gray-50 transition-colors text-sm\">\n                        <Plus className=\"w-4 h-4 inline mr-2\" />\n                        Add Goal\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Main Content */}\n          <div className={`flex-1 overflow-y-auto custom-scrollbar ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}>\n            {/* Team Chat workspace content */}\n            {activeLeftPanel === 'teamchat' && (\n              <div className=\"flex flex-col h-full\">\n                {/* Chat Header */}\n                <div className={`p-4 border-b ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200'}`}\n                     style={!isDarkMode ? { backgroundColor: currentTheme.accent } : {}}>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div \n                        className=\"w-10 h-10 rounded-lg flex items-center justify-center text-white\"\n                        style={{ backgroundColor: currentTheme.primary }}\n                      >\n                        <MessageCircle className=\"w-5 h-5\" />\n                      </div>\n                      <div>\n                        <h1 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>\n                          Team Chat\n                        </h1>\n                        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                          {teamMembers.length} members • {teamMembers.length} online\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <button \n                        className={`p-2 rounded-lg transition-colors ${\n                          isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-white/50 text-gray-600'\n                        }`}\n                      >\n                        <Search className=\"w-4 h-4\" />\n                      </button>\n                      <button \n                        className={`p-2 rounded-lg transition-colors ${\n                          isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-white/50 text-gray-600'\n                        }`}\n                      >\n                        <MoreHorizontal className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Chat Messages */}\n                <div className={`flex-1 overflow-y-auto p-4 space-y-4 ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}>\n                  {chatMessages.map((msg, index) => (\n                    <div key={msg.id} className=\"flex items-start space-x-3\">\n                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs flex-shrink-0 ${msg.color}`}>\n                        {msg.avatar}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center space-x-2 mb-1\">\n                          <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>\n                            {msg.sender}\n                          </span>\n                          <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                            {formatTime(msg.timestamp)}\n                          </span>\n                        </div>\n                        <div \n                          className={`inline-block px-3 py-2 rounded-lg max-w-md ${\n                            msg.sender === 'John Doe' \n                              ? 'text-white ml-auto'\n                              : isDarkMode ? 'bg-gray-800 text-gray-200' : 'bg-gray-100 text-gray-900'\n                          }`}\n                          style={msg.sender === 'John Doe' ? { backgroundColor: currentTheme.primary } : {}}\n                        >\n                          <p className=\"text-sm\">{msg.message}</p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                {/* Message Input */}\n                <div className={`p-4 border-t ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`}>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <button \n                        className={`p-2 rounded-lg transition-colors ${\n                          isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-200 text-gray-600'\n                        }`}\n                      >\n                        <Plus className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                    <div className=\"flex-1 relative\">\n                      <input\n                        type=\"text\"\n                        value={newMessage}\n                        onChange={(e) => setNewMessage(e.target.value)}\n                        onKeyPress={(e) => e.key === 'Enter' && addMessage()}\n                        placeholder=\"Type a message...\"\n                        className={`w-full px-4 py-2 rounded-lg border focus:outline-none focus:ring-2 ${\n                          isDarkMode \n                            ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-gray-500' \n                            : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'\n                        }`}\n                        style={!isDarkMode ? { focusRingColor: currentTheme.primary } : {}}\n                      />\n                    </div>\n                    <button \n                      onClick={addMessage}\n                      disabled={!newMessage.trim()}\n                      className={`p-2 rounded-lg transition-colors text-white ${\n                        newMessage.trim() \n                          ? 'opacity-100' \n                          : 'opacity-50 cursor-not-allowed'\n                      }`}\n                      style={{ backgroundColor: currentTheme.primary }}\n                    >\n                      <MessageCircle className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                  \n                  {/* Typing indicator */}\n                  <div className={`mt-2 text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                    <span className=\"flex items-center space-x-1\">\n                      <div className=\"flex space-x-1\">\n                        <div className=\"w-1 h-1 bg-gray-400 rounded-full animate-bounce\"></div>\n                        <div className=\"w-1 h-1 bg-gray-400 rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                        <div className=\"w-1 h-1 bg-gray-400 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                      </div>\n                      <span>Sarah is typing...</span>\n                    </span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Tasks workspace content */}\n            {activeLeftPanel === 'tasks' && (\n              <div className=\"p-6\">\n                <div className=\"max-w-6xl\">\n                  {/* Header */}\n                  <div className=\"mb-6\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <div>\n                        <h1 className={`text-2xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>\n                          {isLeader && selectedMember !== 'All Members' ? `${selectedMember}'s Tasks` : 'All Tasks'}\n                        </h1>\n                        <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                          {isLeader \n                            ? selectedMember === 'All Members' \n                              ? 'Manage all team tasks and assignments'\n                              : `Tasks assigned to ${selectedMember}`\n                            : 'Your personal task list'\n                          }\n                        </p>\n                      </div>\n                      <div className=\"flex items-center space-x-3\">\n                        <button className={`px-4 py-2 rounded-lg border transition-colors ${\n                          isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'\n                        }`}>\n                          Sort by Due Date\n                        </button>\n                        <button \n                          className=\"px-4 py-2 text-white rounded-lg transition-colors flex items-center space-x-2\"\n                          style={{ \n                            backgroundColor: currentTheme.primary,\n                            '&:hover': { backgroundColor: currentTheme.primaryDark }\n                          }}\n                          onMouseEnter={(e) => e.target.style.backgroundColor = currentTheme.primaryDark}\n                          onMouseLeave={(e) => e.target.style.backgroundColor = currentTheme.primary}\n                        >\n                          <Plus className=\"w-4 h-4\" />\n                          <span>New Task</span>\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Task Columns */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    {/* To Do Column */}\n                    <div className={`rounded-lg p-4`} style={{ backgroundColor: isDarkMode ? '#1f2937' : currentTheme.accent }}>\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>\n                          To Do\n                        </h3>\n                        <span \n                          className=\"px-2 py-1 rounded text-xs font-medium text-white\"\n                          style={{ backgroundColor: currentTheme.primary }}\n                        >\n                          {getFilteredTasks().filter(t => t.status === 'todo').length}\n                        </span>\n                      </div>\n                      <div className=\"space-y-3\">\n                        {getFilteredTasks().filter(task => task.status === 'todo').map((task) => (\n                          <div key={task.id} className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${\n                            isDarkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-200 hover:shadow-lg'\n                          }`}\n                          style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '20' } : {}}\n                          onMouseEnter={(e) => {\n                            if (!isDarkMode) {\n                              e.target.style.borderColor = currentTheme.primaryLight + '40';\n                              e.target.style.transform = 'translateY(-1px)';\n                            }\n                          }}\n                          onMouseLeave={(e) => {\n                            if (!isDarkMode) {\n                              e.target.style.borderColor = currentTheme.primaryLight + '20';\n                              e.target.style.transform = 'translateY(0)';\n                            }\n                          }}\n                          >\n                            <div className=\"flex items-start justify-between mb-2\">\n                              <h4 className={`font-medium text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>\n                                {task.title}\n                              </h4>\n                              <span className={`px-2 py-1 rounded text-xs font-medium ${\n                                task.priority === 'high' \n                                  ? 'bg-red-100 text-red-700'\n                                  : task.priority === 'medium'\n                                  ? 'bg-yellow-100 text-yellow-700'\n                                  : 'bg-gray-100 text-gray-700'\n                              }`}>\n                                {task.priority}\n                              </span>\n                            </div>\n                            <p className={`text-xs mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                              {task.description}\n                            </p>\n                            <div className=\"flex items-center justify-between\">\n                              {isLeader && (\n                                <div className=\"flex items-center space-x-2\">\n                                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs ${\n                                    teamMembers.find(m => m.name === task.assignee)?.color || 'bg-gray-500'\n                                  }`}>\n                                    {teamMembers.find(m => m.name === task.assignee)?.avatar || 'U'}\n                                  </div>\n                                  <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                                    {task.assignee}\n                                  </span>\n                                </div>\n                              )}\n                              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                                {new Date(task.dueDate).toLocaleDateString()}\n                              </span>\n                            </div>\n                          </div>\n                        ))}\n                        {getFilteredTasks().filter(t => t.status === 'todo').length === 0 && (\n                          <div className={`text-center py-8 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>\n                            <Circle className=\"w-8 h-8 mx-auto mb-2 opacity-50\" />\n                            <p className=\"text-sm\">No tasks to do</p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* In Progress Column */}\n                    <div className={`rounded-lg p-4`} style={{ backgroundColor: isDarkMode ? '#1f2937' : currentTheme.accent }}>\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>\n                          In Progress\n                        </h3>\n                        <span \n                          className=\"px-2 py-1 rounded text-xs font-medium text-white\"\n                          style={{ backgroundColor: currentTheme.primary }}\n                        >\n                          {getFilteredTasks().filter(t => t.status === 'in-progress').length}\n                        </span>\n                      </div>\n                      <div className=\"space-y-3\">\n                        {getFilteredTasks().filter(task => task.status === 'in-progress').map((task) => (\n                          <div key={task.id} className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${\n                            isDarkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-200 hover:shadow-lg'\n                          }`}\n                          style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '20' } : {}}\n                          onMouseEnter={(e) => {\n                            if (!isDarkMode) {\n                              e.target.style.borderColor = currentTheme.primaryLight + '40';\n                              e.target.style.transform = 'translateY(-1px)';\n                            }\n                          }}\n                          onMouseLeave={(e) => {\n                            if (!isDarkMode) {\n                              e.target.style.borderColor = currentTheme.primaryLight + '20';\n                              e.target.style.transform = 'translateY(0)';\n                            }\n                          }}\n                          >\n                            <div className=\"flex items-start justify-between mb-2\">\n                              <h4 className={`font-medium text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>\n                                {task.title}\n                              </h4>\n                              <span className={`px-2 py-1 rounded text-xs font-medium ${\n                                task.priority === 'high' \n                                  ? 'bg-red-100 text-red-700'\n                                  : task.priority === 'medium'\n                                  ? 'bg-yellow-100 text-yellow-700'\n                                  : 'bg-gray-100 text-gray-700'\n                              }`}>\n                                {task.priority}\n                              </span>\n                            </div>\n                            <p className={`text-xs mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                              {task.description}\n                            </p>\n                            <div className=\"flex items-center justify-between\">\n                              {isLeader && (\n                                <div className=\"flex items-center space-x-2\">\n                                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs ${\n                                    teamMembers.find(m => m.name === task.assignee)?.color || 'bg-gray-500'\n                                  }`}>\n                                    {teamMembers.find(m => m.name === task.assignee)?.avatar || 'U'}\n                                  </div>\n                                  <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                                    {task.assignee}\n                                  </span>\n                                </div>\n                              )}\n                              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                                {new Date(task.dueDate).toLocaleDateString()}\n                              </span>\n                            </div>\n                          </div>\n                        ))}\n                        {getFilteredTasks().filter(t => t.status === 'in-progress').length === 0 && (\n                          <div className={`text-center py-8 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>\n                            <div className=\"w-8 h-8 rounded-full border-2 border-gray-300 mx-auto mb-2 opacity-50\"></div>\n                            <p className=\"text-sm\">No tasks in progress</p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Done Column */}\n                    <div className={`rounded-lg p-4`} style={{ backgroundColor: isDarkMode ? '#1f2937' : currentTheme.accent }}>\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>\n                          Done\n                        </h3>\n                        <span \n                          className=\"px-2 py-1 rounded text-xs font-medium text-white\"\n                          style={{ backgroundColor: currentTheme.primary }}\n                        >\n                          {getFilteredTasks().filter(t => t.status === 'completed').length}\n                        </span>\n                      </div>\n                      <div className=\"space-y-3\">\n                        {getFilteredTasks().filter(task => task.status === 'completed').map((task) => (\n                          <div key={task.id} className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${\n                            isDarkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-200 hover:shadow-lg'\n                          }`}\n                          style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '20' } : {}}\n                          onMouseEnter={(e) => {\n                            if (!isDarkMode) {\n                              e.target.style.borderColor = currentTheme.primaryLight + '40';\n                              e.target.style.transform = 'translateY(-1px)';\n                            }\n                          }}\n                          onMouseLeave={(e) => {\n                            if (!isDarkMode) {\n                              e.target.style.borderColor = currentTheme.primaryLight + '20';\n                              e.target.style.transform = 'translateY(0)';\n                            }\n                          }}\n                          >\n                            <div className=\"flex items-start justify-between mb-2\">\n                              <h4 className={`font-medium text-sm line-through ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>\n                                {task.title}\n                              </h4>\n                              <CheckCircle2 \n                                className=\"w-5 h-5\" \n                                style={{ color: currentTheme.primary }}\n                              />\n                            </div>\n                            <p className={`text-xs mb-3 ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>\n                              {task.description}\n                            </p>\n                            <div className=\"flex items-center justify-between\">\n                              {isLeader && (\n                                <div className=\"flex items-center space-x-2\">\n                                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs ${\n                                    teamMembers.find(m => m.name === task.assignee)?.color || 'bg-gray-500'\n                                  }`}>\n                                    {teamMembers.find(m => m.name === task.assignee)?.avatar || 'U'}\n                                  </div>\n                                  <span className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>\n                                    {task.assignee}\n                                  </span>\n                                </div>\n                              )}\n                              <span className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>\n                                {new Date(task.dueDate).toLocaleDateString()}\n                              </span>\n                            </div>\n                          </div>\n                        ))}\n                        {getFilteredTasks().filter(t => t.status === 'completed').length === 0 && (\n                          <div className={`text-center py-8 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>\n                            <CheckCircle2 className=\"w-8 h-8 mx-auto mb-2 opacity-50\" />\n                            <p className=\"text-sm\">No completed tasks</p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeLeftPanel === 'home' && currentSubcategory && (\n              currentSubcategory.component ? \n                currentSubcategory.component \n              : \n              <div className=\"p-6\">\n                <div className=\"max-w-4xl\">\n                  {/* Header */}\n                  <div className=\"mb-6\">\n                    <div className={`flex items-center space-x-2 text-xs mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                      <span>{selectedStage}</span>\n                      <ChevronRight className=\"w-3 h-3\" />\n                      <span>{selectedCategory}</span>\n                      <ChevronRight className=\"w-3 h-3\" />\n                      <span>{selectedSubcategory}</span>\n                    </div>\n                    <h1 className={`text-2xl font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{selectedSubcategory}</h1>\n                    <p className={`mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>{currentSubcategory.description}</p>\n                    \n                    <div className=\"flex items-center space-x-4\">\n                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(currentSubcategory.difficulty)}`}>\n                        {currentSubcategory.difficulty}\n                      </span>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Tech Stack:</span>\n                        <div className=\"flex flex-wrap gap-2\">\n                          {currentSubcategory.techStack.slice(0, 3).map((tech, index) => (\n                            <span key={index} className={`px-2 py-1 rounded text-xs border ${\n                              isDarkMode ? 'bg-gray-800 text-gray-300 border-gray-700' : 'bg-gray-100 text-gray-700 border'\n                            }`}>\n                              {tech}\n                            </span>\n                          ))}\n                          {currentSubcategory.techStack.length > 3 && (\n                            <span className={`px-2 py-1 rounded text-xs border ${\n                              isDarkMode ? 'bg-gray-800 text-gray-300 border-gray-700' : 'bg-gray-100 text-gray-700 border'\n                            }`}>\n                              +{currentSubcategory.techStack.length - 3} more\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Topics */}\n                  <div className=\"mb-6\">\n                    <h2 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Project Ideas & Topics</h2>\n                    <div className=\"grid gap-4\">\n                      {currentSubcategory.topics.map((topic, index) => (\n                        <div key={index} className={`rounded-lg p-4 border transition-colors ${\n                          isDarkMode ? 'bg-gray-800 border-gray-700 hover:bg-gray-750' : 'bg-white border hover:bg-gray-50'\n                        }`}>\n                          <div className=\"flex items-start space-x-4\">\n                            <div className={`w-6 h-6 rounded flex items-center justify-center flex-shrink-0 ${\n                              isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-blue-100 text-blue-600'\n                            }`}>\n                              <span className=\"font-semibold text-xs\">{index + 1}</span>\n                            </div>\n                            <div>\n                              <h3 className={`font-medium mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>\n                                {topic.split(' - ')[0]}\n                              </h3>\n                              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>\n                                {topic.split(' - ')[1]}\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Tech Stack Details */}\n                  <div className={`rounded-lg p-4 border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border'}`}>\n                    <h2 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Recommended Technology Stack</h2>\n                    <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\">\n                      {currentSubcategory.techStack.map((tech, index) => (\n                        <div key={index} className={`px-4 py-2 rounded border text-center transition-colors ${\n                          isDarkMode ? 'bg-gray-700 border-gray-600 hover:bg-gray-650' : 'bg-white border hover:bg-gray-50'\n                        }`}>\n                          <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>{tech}</span>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Alternative content when other panels are active */}\n            {activeLeftPanel !== 'home' && (\n              <div className=\"p-6\">\n                <div className=\"max-w-4xl\">\n                  <div className={`text-center py-16 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n                    <div className=\"mb-4\">\n                      {activeLeftPanel === 'inbox' && <Inbox className=\"w-12 h-12 mx-auto mb-4\" />}\n                      {activeLeftPanel === 'chat' && <Brain className=\"w-12 h-12 mx-auto mb-4\" />}\n                      {activeLeftPanel === 'calendar' && <Calendar className=\"w-12 h-12 mx-auto mb-4\" />}\n                      {activeLeftPanel === 'timer' && <Timer className=\"w-12 h-12 mx-auto mb-4\" />}\n                      {activeLeftPanel === 'goals' && <Goal className=\"w-12 h-12 mx-auto mb-4\" />}\n                      {activeLeftPanel === 'tasks' && <ListTodo className=\"w-12 h-12 mx-auto mb-4\" />}\n                      {activeLeftPanel === 'teamchat' && <MessageCircle className=\"w-12 h-12 mx-auto mb-4\" />}\n                    </div>\n                    <h2 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>\n                      {activeLeftPanel === 'inbox' && 'Inbox Management'}\n                      {activeLeftPanel === 'chat' && 'AI Brain Assistant'}\n                      {activeLeftPanel === 'calendar' && 'Calendar View'}\n                      {activeLeftPanel === 'timer' && 'Time Tracking'}\n                      {activeLeftPanel === 'goals' && 'Goal Management'}\n                      {activeLeftPanel === 'tasks' && 'Task Management'}\n                    </h2>\n                    <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Manage your {activeLeftPanel} from the sidebar. Click \"Home\" to return to hackathon stages.</p>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HackathonDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAGC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzD,SAASC,YAAY,EAAEC,IAAI,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,cAAc,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,YAAY,QAAQ,cAAc;AAC/X,OAAOC,qBAAqB,MAAM,sBAAsB;AACxD,OAAOC,qBAAqB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,mBAAmB,CAAC;EACvE,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,iBAAiB,CAAC;EAC3E,MAAM,CAACqD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtD,QAAQ,CAAC,qBAAqB,CAAC;EACrF,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6D,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,MAAM,CAAC;EAC9D,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;EACvD,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmE,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqE,cAAc,EAAEC,iBAAiB,CAAC,GAAGtE,QAAQ,CAAC,aAAa,CAAC;EACnE,MAAM,CAACuE,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC,CAC/C;IACEyE,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,cAAc;IACtBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE,gGAAgG;IACzGC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAAE;IAClDC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,WAAW;IACnBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,eAAe;IACtBC,OAAO,EAAE,iGAAiG;IAC1GC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAAE;IAClDC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,UAAU;IAClBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,yGAAyG;IAClHC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;IAAE;IACjDC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,4DAA4D;IACrEC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;IAAE;IACjDC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,MAAM,EAAE,cAAc;IACtBC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,eAAe;IACtBC,OAAO,EAAE,+EAA+E;IACxFC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;IAAE;IACjDC,IAAI,EAAE;EACR,CAAC,CACF,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMoF,SAAS,GAAGnF,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMoF,UAAU,GAAGpF,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMqF,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIJ,UAAU,CAACK,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMV,OAAO,GAAG;QACdJ,EAAE,EAAEF,YAAY,CAACiB,MAAM,GAAG,CAAC;QAC3Bd,MAAM,EAAE,UAAU;QAAE;QACpBC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAEK,UAAU;QACnBJ,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBE,IAAI,EAAE;MACR,CAAC;MACDT,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAEM,OAAO,CAAC,CAAC;MAC3CM,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,MAAMM,UAAU,GAAIX,SAAS,IAAK;IAChC,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMW,MAAM,GAAGV,GAAG,GAAGF,SAAS;IAC9B,MAAMa,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACjD,MAAMI,SAAS,GAAGF,IAAI,CAACC,KAAK,CAACH,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACvD,MAAMK,QAAQ,GAAGH,IAAI,CAACC,KAAK,CAACH,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE3D,IAAIC,QAAQ,GAAG,CAAC,EAAE,OAAO,KAAK;IAC9B,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGA,QAAQ,OAAO;IAC5C,IAAIG,SAAS,GAAG,EAAE,EAAE,OAAO,GAAGA,SAAS,OAAO;IAC9C,IAAIC,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,OAAO;IAC3C,OAAOjB,SAAS,CAACkB,kBAAkB,CAAC,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG,CAClB;IAAExB,EAAE,EAAE,CAAC;IAAEyB,IAAI,EAAE,UAAU;IAAEvB,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC/D;IAAEH,EAAE,EAAE,CAAC;IAAEyB,IAAI,EAAE,cAAc;IAAEvB,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAe,CAAC,EACpE;IAAEH,EAAE,EAAE,CAAC;IAAEyB,IAAI,EAAE,WAAW;IAAEvB,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAClE;IAAEH,EAAE,EAAE,CAAC;IAAEyB,IAAI,EAAE,YAAY;IAAEvB,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAc,CAAC,EACjE;IAAEH,EAAE,EAAE,CAAC;IAAEyB,IAAI,EAAE,cAAc;IAAEvB,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAgB,CAAC,CACtE;;EAED;EACA,MAAMuB,QAAQ,GAAG,CACf;IACE1B,EAAE,EAAE,CAAC;IACL2B,KAAK,EAAE,sBAAsB;IAC7BC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEhC,EAAE,EAAE,CAAC;IACL2B,KAAK,EAAE,uBAAuB;IAC9BC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEhC,EAAE,EAAE,CAAC;IACL2B,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,QAAQ;IAClBC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEhC,EAAE,EAAE,CAAC;IACL2B,KAAK,EAAE,yBAAyB;IAChCC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEhC,EAAE,EAAE,CAAC;IACL2B,KAAK,EAAE,4BAA4B;IACnCC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,QAAQ;IAClBC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEhC,EAAE,EAAE,CAAC;IACL2B,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE;EACf,CAAC,CACF;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACvC,QAAQ,EAAE;MACb;MACA,OAAOgC,QAAQ,CAACQ,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACP,QAAQ,KAAK,UAAU,CAAC;IAC9D;IAEA,IAAIhC,cAAc,KAAK,aAAa,EAAE;MACpC,OAAO8B,QAAQ;IACjB;IAEA,OAAOA,QAAQ,CAACQ,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACP,QAAQ,KAAKhC,cAAc,CAAC;EAClE,CAAC;;EAED;EACA,MAAMwC,WAAW,GAAG;IAClB,eAAe,EAAE;MACfC,OAAO,EAAE,SAAS;MAAE;MACpBC,YAAY,EAAE,SAAS;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE;IACd,CAAC;IACD,mBAAmB,EAAE;MACnBJ,OAAO,EAAE,SAAS;MAAE;MACpBC,YAAY,EAAE,SAAS;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE;IACd,CAAC;IACD,mBAAmB,EAAE;MACnBJ,OAAO,EAAE,SAAS;MAAE;MACpBC,YAAY,EAAE,SAAS;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE;IACd,CAAC;IACD,kBAAkB,EAAE;MAClBJ,OAAO,EAAE,SAAS;MAAE;MACpBC,YAAY,EAAE,SAAS;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE;IACd;EACF,CAAC;EAED,MAAMC,YAAY,GAAGN,WAAW,CAAC5D,aAAa,CAAC;EAE/C,MAAMmE,MAAM,GAAG;IACb,eAAe,EAAE;MACfC,IAAI,eAAEvE,OAAA,CAAC1B,QAAQ;QAACkG,SAAS,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtC9C,KAAK,EAAE,aAAa;MACpB+C,UAAU,EAAE,2BAA2B;MACvCC,UAAU,EAAE;QACV,cAAc,EAAE;UACdP,IAAI,eAAEvE,OAAA,CAACzB,IAAI;YAACiG,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAClCG,aAAa,EAAE;YACb,gBAAgB,EAAE;cAChBpB,WAAW,EAAE,oEAAoE;cACjFqB,MAAM,EAAE,CACN,gEAAgE,EAChE,uDAAuD,EACvD,uDAAuD,EACvD,sDAAsD,CACvD;cACDC,SAAS,EAAE,CAAC,qBAAqB,EAAE,oBAAoB,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC;cACnFC,UAAU,EAAE;YACd;UACF;QACF,CAAC;QACD,UAAU,EAAE;UACVX,IAAI,eAAEvE,OAAA,CAACxB,QAAQ;YAACgG,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UACtCG,aAAa,EAAE;YACb,sBAAsB,EAAE;cACtBpB,WAAW,EAAE,8DAA8D;cAC3EqB,MAAM,EAAE,CACN,yDAAyD,EACzD,uDAAuD,EACvD,oDAAoD,EACpD,sDAAsD,CACvD;cACDC,SAAS,EAAE,CAAC,gBAAgB,EAAE,cAAc,EAAE,eAAe,EAAE,mBAAmB,CAAC;cACnFC,UAAU,EAAE;YACd;UACF;QACF;MACF;IACF,CAAC;IACD,mBAAmB,EAAE;MACnBX,IAAI,eAAEvE,OAAA,CAAC1C,IAAI;QAACkH,SAAS,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClC9C,KAAK,EAAE,aAAa;MACpB+C,UAAU,EAAE,2BAA2B;MACvCC,UAAU,EAAE;QACV,iBAAiB,EAAE;UACjBP,IAAI,eAAEvE,OAAA,CAACtC,KAAK;YAAC8G,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UACnCG,aAAa,EAAE;YACb,qBAAqB,EAAE;cACrBpB,WAAW,EAAE,qFAAqF;cAClGqB,MAAM,EAAE,CACN,qDAAqD,EACrD,mDAAmD,EACnD,mDAAmD,EACnD,iDAAiD,CAClD;cACDC,SAAS,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;cACnEC,UAAU,EAAE;YACd,CAAC;YACD,qBAAqB,EAAE;cACrBvB,WAAW,EAAE,iFAAiF;cAC9FqB,MAAM,EAAE,CACN,6CAA6C,EAC7C,6CAA6C,EAC7C,2CAA2C,EAC3C,gDAAgD,CACjD;cACDC,SAAS,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,CAAC;cACjEC,UAAU,EAAE;YACd;UACF;QACF,CAAC;QACD,oBAAoB,EAAE;UACpBX,IAAI,eAAEvE,OAAA,CAACzC,UAAU;YAACiH,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UACxCG,aAAa,EAAE;YACb,YAAY,EAAE;cACZpB,WAAW,EAAE,8EAA8E;cAC3FqB,MAAM,EAAE,CACN,6CAA6C,EAC7C,+CAA+C,EAC/C,gDAAgD,EAChD,sCAAsC,CACvC;cACDC,SAAS,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC;cAC9DC,UAAU,EAAE;YACd,CAAC;YACD,gBAAgB,EAAE;cAChBvB,WAAW,EAAE,2EAA2E;cACxFqB,MAAM,EAAE,CACN,yDAAyD,EACzD,4DAA4D,EAC5D,mDAAmD,EACnD,4CAA4C,CAC7C;cACDC,SAAS,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,CAAC;cACvEC,UAAU,EAAE;YACd;UACF;QACF,CAAC;QACD,uBAAuB,EAAE;UACvBX,IAAI,eAAEvE,OAAA,CAACxC,KAAK;YAACgH,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UACnCG,aAAa,EAAE;YACb,6BAA6B,EAAE;cAC7BpB,WAAW,EAAE,yEAAyE;cACtFqB,MAAM,EAAE,CACN,sDAAsD,EACtD,uDAAuD,EACvD,2DAA2D,EAC3D,+CAA+C,CAChD;cACDC,SAAS,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,CAAC;cACpEC,UAAU,EAAE;YACd,CAAC;YACD,iBAAiB,EAAE;cACjBvB,WAAW,EAAE,qEAAqE;cAClFqB,MAAM,EAAE,CACN,kDAAkD,EAClD,oDAAoD,EACpD,+CAA+C,EAC/C,qDAAqD,CACtD;cACDC,SAAS,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC;cACnEC,UAAU,EAAE;YACd;UACF;QACF;MACF;IACF,CAAC;IACD,mBAAmB,EAAE;MACnBX,IAAI,eAAEvE,OAAA,CAACvB,QAAQ;QAAC+F,SAAS,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtC9C,KAAK,EAAE,eAAe;MACtB+C,UAAU,EAAE,+BAA+B;MAC3CC,UAAU,EAAE;QACV,eAAe,EAAE;UACfP,IAAI,eAAEvE,OAAA,CAACvB,QAAQ;YAAC+F,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UACtCG,aAAa,EAAE;YACb,uBAAuB,EAAE;cACvBpB,WAAW,EAAE,gEAAgE;cAC7EqB,MAAM,EAAE,CACN,2DAA2D,EAC3D,wDAAwD,EACxD,+CAA+C,EAC/C,sDAAsD,CACvD;cACDC,SAAS,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,CAAC;cACrEC,UAAU,EAAE;YACd;UACF;QACF;MACF;IACF,CAAC;IACD,kBAAkB,EAAE;MAClBX,IAAI,eAAEvE,OAAA,CAAC3B,KAAK;QAACmG,SAAS,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnC9C,KAAK,EAAE,eAAe;MACtB+C,UAAU,EAAE,+BAA+B;MAC3CC,UAAU,EAAE;QACV,cAAc,EAAE;UACdP,IAAI,eAAEvE,OAAA,CAAC5B,YAAY;YAACoG,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAC1CG,aAAa,EAAE;YACb,eAAe,EAAE;cACfpB,WAAW,EAAE,+DAA+D;cAC5EqB,MAAM,EAAE,CACN,oDAAoD,EACpD,iDAAiD,EACjD,+CAA+C,EAC/C,sDAAsD,CACvD;cACDC,SAAS,EAAE,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,mBAAmB,CAAC;cAC9FC,UAAU,EAAE;YACd,CAAC;YACD,yBAAyB,EAAE;cACzB,WAAW,eAAClF,OAAA,CAACF,qBAAqB;gBAACa,UAAU,EAAEA;cAAW;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAC7D;UACF;QACF,CAAC;QACD,gBAAgB,EAAE;UAChBL,IAAI,eAAEvE,OAAA,CAAC5B,YAAY;YAACoG,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAC1CG,aAAa,EAAE;YACb,eAAe,EAAE;cACf,WAAW,eAAC/E,OAAA,CAACH,qBAAqB;gBAACc,UAAU,EAAEA;cAAW;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAC7D;UACF;QACF;MACF;IACF;EACF,CAAC;EAED,MAAMO,YAAY,GAAGb,MAAM,CAACnE,aAAa,CAAC;EAC1C,MAAMiF,eAAe,GAAGD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEL,UAAU,CAACzE,gBAAgB,CAAC;EAClE,MAAMgF,kBAAkB,GAAGD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEL,aAAa,CAACxE,mBAAmB,CAAC;;EAE9E;EACAnD,SAAS,CAAC,MAAM;IACd,MAAMkI,eAAe,GAAIC,CAAC,IAAK;MAC7B,IAAI,CAACpE,UAAU,EAAE;MAEjB,MAAMqE,QAAQ,GAAGD,CAAC,CAACE,OAAO,GAAG,EAAE,CAAC,CAAC;MACjC,MAAMC,QAAQ,GAAG,GAAG;MACpB,MAAMC,QAAQ,GAAG,GAAG;MAEpB,IAAIH,QAAQ,IAAIE,QAAQ,IAAIF,QAAQ,IAAIG,QAAQ,EAAE;QAChDzE,eAAe,CAACsE,QAAQ,CAAC;MAC3B;IACF,CAAC;IAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;MAC1BxE,aAAa,CAAC,KAAK,CAAC;MACpByE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,SAAS;MACtCH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,UAAU,GAAG,MAAM;IACzC,CAAC;IAED,IAAI9E,UAAU,EAAE;MACd0E,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,YAAY;MACzCH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,UAAU,GAAG,MAAM;MACvCJ,QAAQ,CAACK,gBAAgB,CAAC,WAAW,EAAEZ,eAAe,CAAC;MACvDO,QAAQ,CAACK,gBAAgB,CAAC,SAAS,EAAEN,aAAa,CAAC;IACrD;IAEA,OAAO,MAAM;MACXC,QAAQ,CAACM,mBAAmB,CAAC,WAAW,EAAEb,eAAe,CAAC;MAC1DO,QAAQ,CAACM,mBAAmB,CAAC,SAAS,EAAEP,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACzE,UAAU,CAAC,CAAC;EAEhB,MAAMiF,iBAAiB,GAAIb,CAAC,IAAK;IAC/BA,CAAC,CAACc,cAAc,CAAC,CAAC;IAClBjF,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMkF,kBAAkB,GAAIpB,UAAU,IAAK;IACzC,QAAOA,UAAU;MACf,KAAK,UAAU;QAAE,OAAO,6BAA6B;MACrD,KAAK,cAAc;QAAE,OAAO,+BAA+B;MAC3D,KAAK,UAAU;QAAE,OAAO,+BAA+B;MACvD,KAAK,QAAQ;QAAE,OAAO,yBAAyB;MAC/C;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMqB,iBAAiB,GAAIC,SAAS,IAAK;IACvCpG,gBAAgB,CAACoG,SAAS,CAAC;IAC3BxF,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;IAC5B,MAAMyF,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACrC,MAAM,CAACkC,SAAS,CAAC,CAAC1B,UAAU,CAAC,CAAC,CAAC,CAAC;IAClExE,mBAAmB,CAACmG,aAAa,CAAC;IAClC,MAAMG,gBAAgB,GAAGF,MAAM,CAACC,IAAI,CAACrC,MAAM,CAACkC,SAAS,CAAC,CAAC1B,UAAU,CAAC2B,aAAa,CAAC,CAAC1B,aAAa,CAAC,CAAC,CAAC,CAAC;IAClGvE,sBAAsB,CAACoG,gBAAgB,CAAC;EAC1C,CAAC;EAED,MAAMC,oBAAoB,GAAIC,YAAY,IAAK;IAC7CxG,mBAAmB,CAACwG,YAAY,CAAC;IACjC,MAAMF,gBAAgB,GAAGF,MAAM,CAACC,IAAI,CAACxB,YAAY,CAACL,UAAU,CAACgC,YAAY,CAAC,CAAC/B,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5FvE,sBAAsB,CAACoG,gBAAgB,CAAC;EAC1C,CAAC;EAED,oBACE5G,OAAA;IAAKwE,SAAS,EAAE,0CAA0C7D,UAAU,GAAG,aAAa,GAAG,UAAU,wCAAyC;IAAAoG,QAAA,gBACxI/G,OAAA;MAAOgH,GAAG;MAAAD,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAtC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAGV5E,OAAA;MACEwE,SAAS,EAAE,GAAG7D,UAAU,GAAG,iBAAiB,GAAG,UAAU,oCAAqC;MAC9FoF,KAAK,EAAE;QACLkB,eAAe,EAAEtG,UAAU,GAAG,SAAS,GAAG0D,YAAY,CAACL,OAAO;QAC9DkD,WAAW,EAAEvG,UAAU,GAAG,SAAS,GAAG0D,YAAY,CAACH;MACrD,CAAE;MAAA6C,QAAA,eAEF/G,OAAA;QAAKwE,SAAS,EAAC,mCAAmC;QAAAuC,QAAA,gBAEhD/G,OAAA;UAAKwE,SAAS,EAAC,6BAA6B;UAAAuC,QAAA,gBAC1C/G,OAAA;YACEwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,aAAa,GAAG,UAAU,8CAA+C;YAAAoG,QAAA,eAE5G/G,OAAA,CAAChC,MAAM;cACLwG,SAAS,EAAE,SAAU;cACrBuB,KAAK,EAAE;gBACLjE,KAAK,EAAEnB,UAAU,GAAG,SAAS,GAAG0D,YAAY,CAACL;cAC/C;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5E,OAAA;YAAMwE,SAAS,EAAE,yBAAyB7D,UAAU,GAAG,YAAY,GAAG,YAAY,EAAG;YAAAoG,QAAA,EAAC;UAAQ;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,eAGN5E,OAAA;UAAKwE,SAAS,EAAC,4BAA4B;UAAAuC,QAAA,eACzC/G,OAAA;YAAKwE,SAAS,EAAC,6BAA6B;YAAAuC,QAAA,eAC1C/G,OAAA;cAAIwE,SAAS,EAAE,yBAAyB7D,UAAU,GAAG,YAAY,GAAG,YAAY,EAAG;cAAAoG,QAAA,EAAC;YAEpF;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5E,OAAA;UAAKwE,SAAS,EAAC,6BAA6B;UAAAuC,QAAA,gBAE1C/G,OAAA;YAAKwE,SAAS,EAAC,UAAU;YAAAuC,QAAA,gBACvB/G,OAAA,CAACpB,MAAM;cAAC4F,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe;YAAsD;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrI5E,OAAA;cACEmC,IAAI,EAAC,MAAM;cACXgF,WAAW,EAAC,sBAAsB;cAClC3C,SAAS,EAAE,kFACT7D,UAAU,GACN,0GAA0G,GAC1G,wFAAwF,EAC3F;cACHoF,KAAK,EAAE;gBACLkB,eAAe,EAAEtG,UAAU,GAAG,SAAS,GAAG,GAAG0D,YAAY,CAACL,OAAO,IAAI;gBACrEkD,WAAW,EAAEvG,UAAU,GAAG,SAAS,GAAG;cACxC;YAAE;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF5E,OAAA;cAAKwE,SAAS,EAAE,+DAA+D7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;cAAAoG,QAAA,EAAC;YAEjI;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5E,OAAA;YAAKwE,SAAS,EAAC,UAAU;YAAAuC,QAAA,gBACvB/G,OAAA;cACEoH,OAAO,EAAEA,CAAA,KAAMtG,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;cACxD2D,SAAS,EAAE,oCACT7D,UAAU,GACN,qDAAqD,GACrD,gCAAgC,EACnC;cACH0G,YAAY,EAAG9B,CAAC,IAAK;gBACnB,IAAI,CAAC5E,UAAU,EAAE;kBACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACkB,eAAe,GAAG5C,YAAY,CAACH,WAAW;gBAC3D;cACF,CAAE;cACFqD,YAAY,EAAGhC,CAAC,IAAK;gBACnB,IAAI,CAAC5E,UAAU,EAAE;kBACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACkB,eAAe,GAAG,aAAa;gBAChD;cACF,CAAE;cAAAF,QAAA,gBAEF/G,OAAA,CAACnB,IAAI;gBAAC2F,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5B5E,OAAA;gBAAKwE,SAAS,EAAC;cAA0D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,EAGR/D,iBAAiB,iBAChBb,OAAA;cAAKwE,SAAS,EAAE,uCAAuC7D,UAAU,GAAG,6BAA6B,GAAG,0BAA0B,mCAAoC;cAAAoG,QAAA,gBAChK/G,OAAA;gBAAKwE,SAAS,EAAE,gBAAgB7D,UAAU,GAAG,iBAAiB,GAAG,iBAAiB,EAAG;gBAAAoG,QAAA,eACnF/G,OAAA;kBAAIwE,SAAS,EAAE,iBAAiB7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;kBAAAoG,QAAA,EAAC;gBAAa;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F,CAAC,eACN5E,OAAA;gBAAKwE,SAAS,EAAC,eAAe;gBAAAuC,QAAA,gBAC5B/G,OAAA;kBAAKwE,SAAS,EAAC,SAAS;kBAAAuC,QAAA,gBACtB/G,OAAA;oBAAKwE,SAAS,EAAE,eAAe7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;oBAAAoG,QAAA,EAAC;kBAA4B;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChH5E,OAAA;oBAAKwE,SAAS,EAAE,GAAG7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;oBAAAoG,QAAA,EAAC;kBAAsC;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9G,CAAC,eACN5E,OAAA;kBAAKwE,SAAS,EAAC,SAAS;kBAAAuC,QAAA,gBACtB/G,OAAA;oBAAKwE,SAAS,EAAE,eAAe7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;oBAAAoG,QAAA,EAAC;kBAAwB;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5G5E,OAAA;oBAAKwE,SAAS,EAAE,GAAG7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;oBAAAoG,QAAA,EAAC;kBAA2C;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5E,OAAA;YACEoH,OAAO,EAAEA,CAAA,KAAMxG,aAAa,CAAC,CAACD,UAAU,CAAE;YAC1C6D,SAAS,EAAE,oCACT7D,UAAU,GACN,qDAAqD,GACrD,gCAAgC,EACnC;YACH0G,YAAY,EAAG9B,CAAC,IAAK;cACnB,IAAI,CAAC5E,UAAU,EAAE;gBACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACkB,eAAe,GAAG5C,YAAY,CAACH,WAAW;cAC3D;YACF,CAAE;YACFqD,YAAY,EAAGhC,CAAC,IAAK;cACnB,IAAI,CAAC5E,UAAU,EAAE;gBACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACkB,eAAe,GAAG,aAAa;cAChD;YACF,CAAE;YAAAF,QAAA,EAEDpG,UAAU,gBAAGX,OAAA,CAACjB,GAAG;cAACyF,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5E,OAAA,CAAClB,IAAI;cAAC0F,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eAGT5E,OAAA;YACEwE,SAAS,EAAE,oCACT7D,UAAU,GACN,qDAAqD,GACrD,gCAAgC,EACnC;YACH0G,YAAY,EAAG9B,CAAC,IAAK;cACnB,IAAI,CAAC5E,UAAU,EAAE;gBACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACkB,eAAe,GAAG5C,YAAY,CAACH,WAAW;cAC3D;YACF,CAAE;YACFqD,YAAY,EAAGhC,CAAC,IAAK;cACnB,IAAI,CAAC5E,UAAU,EAAE;gBACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACkB,eAAe,GAAG,aAAa;cAChD;YACF,CAAE;YAAAF,QAAA,eAEF/G,OAAA,CAACf,OAAO;cAACuF,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eAGT5E,OAAA;YACEwE,SAAS,EAAE,oCACT7D,UAAU,GACN,qDAAqD,GACrD,gCAAgC,EACnC;YACH0G,YAAY,EAAG9B,CAAC,IAAK;cACnB,IAAI,CAAC5E,UAAU,EAAE;gBACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACkB,eAAe,GAAG5C,YAAY,CAACH,WAAW;cAC3D;YACF,CAAE;YACFqD,YAAY,EAAGhC,CAAC,IAAK;cACnB,IAAI,CAAC5E,UAAU,EAAE;gBACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACkB,eAAe,GAAG,aAAa;cAChD;YACF,CAAE;YAAAF,QAAA,eAEF/G,OAAA,CAAC1B,QAAQ;cAACkG,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAGT5E,OAAA;YAAKwE,SAAS,EAAC,6BAA6B;YAAAuC,QAAA,eAC1C/G,OAAA;cAAKwE,SAAS,EAAC,qGAAqG;cAAAuC,QAAA,eAClH/G,OAAA;gBAAMwE,SAAS,EAAC,gCAAgC;gBAAAuC,QAAA,EAAC;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5E,OAAA;MAAKwE,SAAS,EAAC,8BAA8B;MAAAuC,QAAA,gBAE3C/G,OAAA;QACEwE,SAAS,EAAE,sFAAuF;QAClGuB,KAAK,EAAE;UACLkB,eAAe,EAAEtG,UAAU,GAAG,SAAS,GAAG0D,YAAY,CAACH,WAAW;UAClEgD,WAAW,EAAEvG,UAAU,GAAG,SAAS,GAAG0D,YAAY,CAACH;QACrD,CAAE;QAAA6C,QAAA,gBAGF/G,OAAA;UAAKwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,aAAa,GAAG,UAAU,mDAAoD;UAAAoG,QAAA,eACpH/G,OAAA;YACEwE,SAAS,EAAC,mBAAmB;YAC7BuB,KAAK,EAAE;cAAEjE,KAAK,EAAEnB,UAAU,GAAG,SAAS,GAAG0D,YAAY,CAACL;YAAQ,CAAE;YAAA+C,QAAA,EACjE;UAAC;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN5E,OAAA;UAAKwE,SAAS,EAAC,yBAAyB;UAAAuC,QAAA,gBAEtC/G,OAAA;YAAKwE,SAAS,EAAC,gBAAgB;YAAAuC,QAAA,gBAC7B/G,OAAA;cACEoH,OAAO,EAAEA,CAAA,KAAMpG,kBAAkB,CAAC,MAAM,CAAE;cAC1CqG,YAAY,EAAEA,CAAA,KAAM3G,cAAc,CAAC,cAAc,CAAE;cACnD6G,YAAY,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;cACzC8D,SAAS,EAAE,yEACT7D,UAAU,GAAG,gCAAgC,GAAG,gCAAgC,EAC/E;cAAAoG,QAAA,gBAEH/G,OAAA;gBAAKwE,SAAS,EAAE,yEACdzD,eAAe,KAAK,MAAM,GACtBJ,UAAU,GAAG,aAAa,GAAG,UAAU,GACvC,mBAAmB,EACtB;gBAAAoG,QAAA,eACD/G,OAAA,CAACd,IAAI;kBACHsF,SAAS,EAAC,SAAS;kBACnBuB,KAAK,EAAEhF,eAAe,KAAK,MAAM,IAAI,CAACJ,UAAU,GAAG;oBAAEmB,KAAK,EAAEuC,YAAY,CAACL;kBAAQ,CAAC,GAAG,CAAC;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAMwE,SAAS,EAAE,kBAAkBzD,eAAe,KAAK,MAAM,GAAG,eAAe,GAAG,EAAE,EAAG;gBAAAgG,QAAA,EAAC;cAAI;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,EACRnE,WAAW,KAAK,cAAc,iBAC7BT,OAAA;cAAKwE,SAAS,EAAC,uIAAuI;cAAAuC,QAAA,GAAC,MAErJ,eAAA/G,OAAA;gBAAKwE,SAAS,EAAC;cAAmF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5E,OAAA;YAAKwE,SAAS,EAAC,gBAAgB;YAAAuC,QAAA,gBAC7B/G,OAAA;cACEoH,OAAO,EAAEA,CAAA,KAAMpG,kBAAkB,CAAC,OAAO,CAAE;cAC3CqG,YAAY,EAAEA,CAAA,KAAM3G,cAAc,CAAC,eAAe,CAAE;cACpD6G,YAAY,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;cACzC8D,SAAS,EAAE,yEACT7D,UAAU,GAAG,gCAAgC,GAAG,gCAAgC,EAC/E;cAAAoG,QAAA,gBAEH/G,OAAA;gBAAKwE,SAAS,EAAE,yEACdzD,eAAe,KAAK,OAAO,GACvBJ,UAAU,GAAG,aAAa,GAAG,UAAU,GACvC,mBAAmB,EACtB;gBAAAoG,QAAA,eACD/G,OAAA,CAACb,KAAK;kBACJqF,SAAS,EAAC,SAAS;kBACnBuB,KAAK,EAAEhF,eAAe,KAAK,OAAO,IAAI,CAACJ,UAAU,GAAG;oBAAEmB,KAAK,EAAEuC,YAAY,CAACL;kBAAQ,CAAC,GAAG,CAAC;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAMwE,SAAS,EAAE,kBAAkBzD,eAAe,KAAK,OAAO,GAAG,eAAe,GAAG,EAAE,EAAG;gBAAAgG,QAAA,EAAC;cAAK;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,EACRnE,WAAW,KAAK,eAAe,iBAC9BT,OAAA;cAAKwE,SAAS,EAAC,uIAAuI;cAAAuC,QAAA,GAAC,OAErJ,eAAA/G,OAAA;gBAAKwE,SAAS,EAAC;cAAmF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5E,OAAA;YAAKwE,SAAS,EAAC,gBAAgB;YAAAuC,QAAA,gBAC7B/G,OAAA;cACEoH,OAAO,EAAEA,CAAA,KAAMpG,kBAAkB,CAAC,UAAU,CAAE;cAC9CqG,YAAY,EAAEA,CAAA,KAAM3G,cAAc,CAAC,kBAAkB,CAAE;cACvD6G,YAAY,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;cACzC8D,SAAS,EAAE,yEACT7D,UAAU,GAAG,gCAAgC,GAAG,gCAAgC,EAC/E;cAAAoG,QAAA,gBAEH/G,OAAA;gBAAKwE,SAAS,EAAE,yEACdzD,eAAe,KAAK,UAAU,GAC1BJ,UAAU,GAAG,aAAa,GAAG,UAAU,GACvC,mBAAmB,EACtB;gBAAAoG,QAAA,eACD/G,OAAA,CAACxB,QAAQ;kBACPgG,SAAS,EAAC,SAAS;kBACnBuB,KAAK,EAAEhF,eAAe,KAAK,UAAU,IAAI,CAACJ,UAAU,GAAG;oBAAEmB,KAAK,EAAEuC,YAAY,CAACL;kBAAQ,CAAC,GAAG,CAAC;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAMwE,SAAS,EAAE,kBAAkBzD,eAAe,KAAK,UAAU,GAAG,eAAe,GAAG,EAAE,EAAG;gBAAAgG,QAAA,EAAC;cAAQ;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,EACRnE,WAAW,KAAK,kBAAkB,iBACjCT,OAAA;cAAKwE,SAAS,EAAC,uIAAuI;cAAAuC,QAAA,GAAC,UAErJ,eAAA/G,OAAA;gBAAKwE,SAAS,EAAC;cAAmF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5E,OAAA;YAAKwE,SAAS,EAAC,gBAAgB;YAAAuC,QAAA,gBAC7B/G,OAAA;cACEoH,OAAO,EAAEA,CAAA,KAAMpG,kBAAkB,CAAC,MAAM,CAAE;cAC1CqG,YAAY,EAAEA,CAAA,KAAM3G,cAAc,CAAC,eAAe,CAAE;cACpD6G,YAAY,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;cACzC8D,SAAS,EAAE,kFACT7D,UAAU,GAAG,gCAAgC,GAAG,gCAAgC,EAC/E;cAAAoG,QAAA,gBAEH/G,OAAA;gBAAKwE,SAAS,EAAE,yEACdzD,eAAe,KAAK,MAAM,GACtBJ,UAAU,GAAG,aAAa,GAAG,UAAU,GACvC,mBAAmB,EACtB;gBAAAoG,QAAA,eACD/G,OAAA,CAACxC,KAAK;kBACJgH,SAAS,EAAC,SAAS;kBACnBuB,KAAK,EAAEhF,eAAe,KAAK,MAAM,IAAI,CAACJ,UAAU,GAAG;oBAAEmB,KAAK,EAAEuC,YAAY,CAACL;kBAAQ,CAAC,GAAG,CAAC;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAMwE,SAAS,EAAE,kBAAkBzD,eAAe,KAAK,MAAM,GAAG,eAAe,GAAG,EAAE,EAAG;gBAAAgG,QAAA,EAAC;cAAK;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpG5E,OAAA;gBAAKwE,SAAS,EAAC;cAA0D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,EACRnE,WAAW,KAAK,eAAe,iBAC9BT,OAAA;cAAKwE,SAAS,EAAC,uIAAuI;cAAAuC,QAAA,GAAC,iBAErJ,eAAA/G,OAAA;gBAAKwE,SAAS,EAAC;cAAmF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5E,OAAA;YAAKwE,SAAS,EAAC,gBAAgB;YAAAuC,QAAA,gBAC7B/G,OAAA;cACEoH,OAAO,EAAEA,CAAA,KAAMpG,kBAAkB,CAAC,UAAU,CAAE;cAC9CqG,YAAY,EAAEA,CAAA,KAAM3G,cAAc,CAAC,kBAAkB,CAAE;cACvD6G,YAAY,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;cACzC8D,SAAS,EAAE,yEACT7D,UAAU,GAAG,gCAAgC,GAAG,gCAAgC,EAC/E;cAAAoG,QAAA,gBAEH/G,OAAA;gBAAKwE,SAAS,EAAE,kFACdzD,eAAe,KAAK,UAAU,GAC1BJ,UAAU,GAAG,aAAa,GAAG,UAAU,GACvC,mBAAmB,EACtB;gBAAAoG,QAAA,gBACD/G,OAAA,CAACZ,aAAa;kBACZoF,SAAS,EAAC,SAAS;kBACnBuB,KAAK,EAAEhF,eAAe,KAAK,UAAU,IAAI,CAACJ,UAAU,GAAG;oBAAEmB,KAAK,EAAEuC,YAAY,CAACL;kBAAQ,CAAC,GAAG,CAAC;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F,CAAC,eACF5E,OAAA;kBAAKwE,SAAS,EAAC,2FAA2F;kBAAAuC,QAAA,eACxG/G,OAAA;oBAAMwE,SAAS,EAAC,8BAA8B;oBAAAuC,QAAA,EAAC;kBAAC;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5E,OAAA;gBAAMwE,SAAS,EAAE,kBAAkBzD,eAAe,KAAK,UAAU,GAAG,eAAe,GAAG,EAAE,EAAG;gBAAAgG,QAAA,EAAC;cAAI;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC,EACRnE,WAAW,KAAK,kBAAkB,iBACjCT,OAAA;cAAKwE,SAAS,EAAC,uIAAuI;cAAAuC,QAAA,GAAC,WAErJ,eAAA/G,OAAA;gBAAKwE,SAAS,EAAC;cAAmF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5E,OAAA;YAAKwE,SAAS,EAAC,gBAAgB;YAAAuC,QAAA,gBAC7B/G,OAAA;cACEoH,OAAO,EAAEA,CAAA,KAAMpG,kBAAkB,CAAC,OAAO,CAAE;cAC3CqG,YAAY,EAAEA,CAAA,KAAM3G,cAAc,CAAC,eAAe,CAAE;cACpD6G,YAAY,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;cACzC8D,SAAS,EAAE,yEACT7D,UAAU,GAAG,gCAAgC,GAAG,gCAAgC,EAC/E;cAAAoG,QAAA,gBAEH/G,OAAA;gBAAKwE,SAAS,EAAE,yEACdzD,eAAe,KAAK,OAAO,GACvBJ,UAAU,GAAG,aAAa,GAAG,UAAU,GACvC,mBAAmB,EACtB;gBAAAoG,QAAA,eACD/G,OAAA,CAACX,QAAQ;kBACPmF,SAAS,EAAC,SAAS;kBACnBuB,KAAK,EAAEhF,eAAe,KAAK,OAAO,IAAI,CAACJ,UAAU,GAAG;oBAAEmB,KAAK,EAAEuC,YAAY,CAACL;kBAAQ,CAAC,GAAG,CAAC;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAMwE,SAAS,EAAE,kBAAkBzD,eAAe,KAAK,OAAO,GAAG,eAAe,GAAG,EAAE,EAAG;gBAAAgG,QAAA,EAAC;cAAK;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,EACRnE,WAAW,KAAK,eAAe,iBAC9BT,OAAA;cAAKwE,SAAS,EAAC,uIAAuI;cAAAuC,QAAA,GAAC,OAErJ,eAAA/G,OAAA;gBAAKwE,SAAS,EAAC;cAAmF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5E,OAAA;YAAKwE,SAAS,EAAC,gBAAgB;YAAAuC,QAAA,gBAC7B/G,OAAA;cACEoH,OAAO,EAAEA,CAAA,KAAMpG,kBAAkB,CAAC,OAAO,CAAE;cAC3CqG,YAAY,EAAEA,CAAA,KAAM3G,cAAc,CAAC,eAAe,CAAE;cACpD6G,YAAY,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;cACzC8D,SAAS,EAAE,yEACT7D,UAAU,GAAG,gCAAgC,GAAG,gCAAgC,EAC/E;cAAAoG,QAAA,gBAEH/G,OAAA;gBAAKwE,SAAS,EAAE,yEACdzD,eAAe,KAAK,OAAO,GACvBJ,UAAU,GAAG,aAAa,GAAG,UAAU,GACvC,mBAAmB,EACtB;gBAAAoG,QAAA,eACD/G,OAAA,CAACR,IAAI;kBACHgF,SAAS,EAAC,SAAS;kBACnBuB,KAAK,EAAEhF,eAAe,KAAK,OAAO,IAAI,CAACJ,UAAU,GAAG;oBAAEmB,KAAK,EAAEuC,YAAY,CAACL;kBAAQ,CAAC,GAAG,CAAC;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAMwE,SAAS,EAAE,kBAAkBzD,eAAe,KAAK,OAAO,GAAG,eAAe,GAAG,EAAE,EAAG;gBAAAgG,QAAA,EAAC;cAAK;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,EACRnE,WAAW,KAAK,eAAe,iBAC9BT,OAAA;cAAKwE,SAAS,EAAC,uIAAuI;cAAAuC,QAAA,GAAC,OAErJ,eAAA/G,OAAA;gBAAKwE,SAAS,EAAC;cAAmF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5E,OAAA;YAAKwE,SAAS,EAAC,gBAAgB;YAAAuC,QAAA,gBAC7B/G,OAAA;cACEoH,OAAO,EAAEA,CAAA,KAAMpG,kBAAkB,CAAC,OAAO,CAAE;cAC3CqG,YAAY,EAAEA,CAAA,KAAM3G,cAAc,CAAC,mBAAmB,CAAE;cACxD6G,YAAY,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;cACzC8D,SAAS,EAAE,yEACT7D,UAAU,GAAG,gCAAgC,GAAG,gCAAgC,EAC/E;cAAAoG,QAAA,gBAEH/G,OAAA;gBAAKwE,SAAS,EAAE,yEACdzD,eAAe,KAAK,OAAO,GACvBJ,UAAU,GAAG,aAAa,GAAG,UAAU,GACvC,mBAAmB,EACtB;gBAAAoG,QAAA,eACD/G,OAAA,CAACT,KAAK;kBACJiF,SAAS,EAAC,SAAS;kBACnBuB,KAAK,EAAEhF,eAAe,KAAK,OAAO,IAAI,CAACJ,UAAU,GAAG;oBAAEmB,KAAK,EAAEuC,YAAY,CAACL;kBAAQ,CAAC,GAAG,CAAC;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5E,OAAA;gBAAMwE,SAAS,EAAE,kBAAkBzD,eAAe,KAAK,OAAO,GAAG,eAAe,GAAG,EAAE,EAAG;gBAAAgG,QAAA,EAAC;cAAS;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC,EACRnE,WAAW,KAAK,mBAAmB,iBAClCT,OAAA;cAAKwE,SAAS,EAAC,uIAAuI;cAAAuC,QAAA,GAAC,WAErJ,eAAA/G,OAAA;gBAAKwE,SAAS,EAAC;cAAmF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5E,OAAA;YAAKwE,SAAS,EAAC,gBAAgB;YAAAuC,QAAA,gBAC7B/G,OAAA;cACEqH,YAAY,EAAEA,CAAA,KAAM3G,cAAc,CAAC,cAAc,CAAE;cACnD6G,YAAY,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;cACzC8D,SAAS,EAAE,yEACT7D,UAAU,GAAG,gCAAgC,GAAG,gCAAgC,EAC/E;cAAAoG,QAAA,gBAEH/G,OAAA;gBAAKwE,SAAS,EAAC,yFAAyF;gBAAAuC,QAAA,eACtG/G,OAAA,CAAChB,cAAc;kBAACwF,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACN5E,OAAA;gBAAMwE,SAAS,EAAC,gBAAgB;gBAAAuC,QAAA,EAAC;cAAI;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,EACRnE,WAAW,KAAK,cAAc,iBAC7BT,OAAA;cAAKwE,SAAS,EAAC,uIAAuI;cAAAuC,QAAA,GAAC,WAErJ,eAAA/G,OAAA;gBAAKwE,SAAS,EAAC;cAAmF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5E,OAAA;UAAKwE,SAAS,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAG9B5E,OAAA;UAAKwE,SAAS,EAAC,gBAAgB;UAAAuC,QAAA,gBAC7B/G,OAAA;YACEqH,YAAY,EAAEA,CAAA,KAAM3G,cAAc,CAAC,gBAAgB,CAAE;YACrD6G,YAAY,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;YACzC8D,SAAS,EAAE,yEACT7D,UAAU,GAAG,gCAAgC,GAAG,gCAAgC,EAC/E;YAAAoG,QAAA,gBAEH/G,OAAA;cAAKwE,SAAS,EAAC,yFAAyF;cAAAuC,QAAA,eACtG/G,OAAA,CAACP,QAAQ;gBAAC+E,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACN5E,OAAA;cAAMwE,SAAS,EAAC,gBAAgB;cAAAuC,QAAA,EAAC;YAAM;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,EACRnE,WAAW,KAAK,gBAAgB,iBAC/BT,OAAA;YAAKwE,SAAS,EAAC,uIAAuI;YAAAuC,QAAA,GAAC,gBAErJ,eAAA/G,OAAA;cAAKwE,SAAS,EAAC;YAAmF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5E,OAAA;QAAKwE,SAAS,EAAC,6BAA6B;QAAAuC,QAAA,gBAE1C/G,OAAA;UACEwH,GAAG,EAAEjF,UAAW;UAChBiC,SAAS,EAAE,+CAAgD;UAC3DuB,KAAK,EAAE;YACL0B,KAAK,EAAE,GAAGxG,YAAY,IAAI;YAC1BgG,eAAe,EAAEtG,UAAU,GAAG,SAAS,GAAG,SAAS;YACnDuG,WAAW,EAAEvG,UAAU,GAAG,SAAS,GAAG;UACxC,CAAE;UAAAoG,QAAA,gBAGF/G,OAAA;YACEwH,GAAG,EAAElF,SAAU;YACfkC,SAAS,EAAE,iBAAiBrD,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;YAC3DuG,WAAW,EAAEtB;UAAkB;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,EAED7D,eAAe,KAAK,MAAM,gBACzBf,OAAA;YAAKwE,SAAS,EAAC,yCAAyC;YAAAuC,QAAA,gBAEtD/G,OAAA;cAAKwE,SAAS,EAAE,gBAAgB7D,UAAU,GAAG,iBAAiB,GAAG,iBAAiB,EAAG;cAAAoG,QAAA,gBACnF/G,OAAA;gBAAKwE,SAAS,EAAC,wCAAwC;gBAAAuC,QAAA,gBACrD/G,OAAA;kBAAIwE,SAAS,EAAE,yBAAyB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;kBAAAoG,QAAA,EAAC;gBAAgB;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/G5E,OAAA;kBAAQwE,SAAS,EAAE,GAAG7D,UAAU,GAAG,mCAAmC,GAAG,mCAAmC,EAAG;kBAAAoG,QAAA,eAC7G/G,OAAA,CAAChB,cAAc;oBAACwF,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGN5E,OAAA;gBAAKwE,SAAS,EAAC,MAAM;gBAAAuC,QAAA,gBACnB/G,OAAA;kBAAKwE,SAAS,EAAE,qCAAqC7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;kBAAAoG,QAAA,gBACpG/G,OAAA;oBAAA+G,QAAA,EAAM;kBAAQ;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrB5E,OAAA;oBAAA+G,QAAA,GAAOjE,IAAI,CAAC6E,KAAK,CAAE,CAACjB,MAAM,CAACC,IAAI,CAACrC,MAAM,CAAC,CAACsD,OAAO,CAACzH,aAAa,CAAC,GAAG,CAAC,IAAIuG,MAAM,CAACC,IAAI,CAACrC,MAAM,CAAC,CAAC5B,MAAM,GAAI,GAAG,CAAC,EAAC,GAAC;kBAAA;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9G,CAAC,eACN5E,OAAA;kBAAKwE,SAAS,EAAE,2BAA2B7D,UAAU,GAAG,aAAa,GAAG,aAAa,EAAG;kBAAAoG,QAAA,eACtF/G,OAAA;oBACEwE,SAAS,EAAC,uDAAuD;oBACjEuB,KAAK,EAAE;sBACLkB,eAAe,EAAE5C,YAAY,CAACL,OAAO;sBACrCyD,KAAK,EAAE,GAAI,CAACf,MAAM,CAACC,IAAI,CAACrC,MAAM,CAAC,CAACsD,OAAO,CAACzH,aAAa,CAAC,GAAG,CAAC,IAAIuG,MAAM,CAACC,IAAI,CAACrC,MAAM,CAAC,CAAC5B,MAAM,GAAI,GAAG;oBACjG;kBAAE;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5E,OAAA;gBAAKwE,SAAS,EAAC,WAAW;gBAAAuC,QAAA,EACvBL,MAAM,CAACmB,OAAO,CAACvD,MAAM,CAAC,CAACwD,GAAG,CAAC,CAAC,CAACtB,SAAS,EAAEuB,SAAS,CAAC,KAAK;kBACtD,MAAMC,QAAQ,GAAG7H,aAAa,KAAKqG,SAAS;kBAC5C,MAAMyB,MAAM,GAAGvB,MAAM,CAACC,IAAI,CAACrC,MAAM,CAAC,CAACsD,OAAO,CAACzH,aAAa,CAAC,GAAGuG,MAAM,CAACC,IAAI,CAACrC,MAAM,CAAC,CAACsD,OAAO,CAACpB,SAAS,CAAC;kBAElG,oBACExG,OAAA;oBAEEoH,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAACC,SAAS,CAAE;oBAC5ChC,SAAS,EAAE,4FACTwD,QAAQ,GACJ,aAAa,GACbrH,UAAU,GAAG,iCAAiC,GAAG,iCAAiC,EACrF;oBACHoF,KAAK,EAAE;sBACLkB,eAAe,EAAEe,QAAQ,GAAG3D,YAAY,CAACF,MAAM,GAAG,aAAa;sBAC/DrC,KAAK,EAAEkG,QAAQ,GAAG3D,YAAY,CAACD,UAAU,GAAIzD,UAAU,GAAG,SAAS,GAAG;oBACxE,CAAE;oBAAAoG,QAAA,gBAEF/G,OAAA;sBAAKwE,SAAS,EAAC,0CAA0C;sBAAAuC,QAAA,EACtDkB,MAAM,IAAI,CAACD,QAAQ,gBAClBhI,OAAA,CAACtB,WAAW;wBAAC8F,SAAS,EAAC;sBAAwB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,iBAElD3H,KAAK,CAACiL,YAAY,CAACH,SAAS,CAACxD,IAAI,EAAE;wBACjCC,SAAS,EAAE,SAAS;wBACpBuB,KAAK,EAAE;0BACLjE,KAAK,EAAEkG,QAAQ,GAAG3D,YAAY,CAACD,UAAU,GAAG;wBAC9C;sBACF,CAAC,CAAC;oBACH;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACN5E,OAAA;sBAAA+G,QAAA,EAAOP;oBAAS;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,GAxBnB4B,SAAS;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyBR,CAAC;gBAEb,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5E,OAAA;cAAKwE,SAAS,EAAC,KAAK;cAAAuC,QAAA,gBAClB/G,OAAA;gBAAKwE,SAAS,EAAC,wCAAwC;gBAAAuC,QAAA,gBACrD/G,OAAA;kBAAIwE,SAAS,EAAE,yBAAyB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;kBAAAoG,QAAA,EAAC;gBAAU;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzG5E,OAAA;kBAAQwE,SAAS,EAAE,GAAG7D,UAAU,GAAG,mCAAmC,GAAG,mCAAmC,EAAG;kBAAAoG,QAAA,eAC7G/G,OAAA,CAAChB,cAAc;oBAACwF,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN5E,OAAA;gBAAKwE,SAAS,EAAC,WAAW;gBAAAuC,QAAA,EACvBL,MAAM,CAACmB,OAAO,CAAC,CAAA1C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEL,UAAU,KAAI,CAAC,CAAC,CAAC,CAACgD,GAAG,CAAC,CAAC,CAAChB,YAAY,EAAEqB,YAAY,CAAC,kBAC/EnI,OAAA;kBAEEoH,OAAO,EAAEA,CAAA,KAAMP,oBAAoB,CAACC,YAAY,CAAE;kBAClDtC,SAAS,EAAE,4FACTnE,gBAAgB,KAAKyG,YAAY,GAC7B,aAAa,GACbnG,UAAU,GAAG,iCAAiC,GAAG,iCAAiC,EACrF;kBACHoF,KAAK,EAAE;oBACLkB,eAAe,EAAE5G,gBAAgB,KAAKyG,YAAY,GAAGzC,YAAY,CAACF,MAAM,GAAG,aAAa;oBACxFrC,KAAK,EAAEzB,gBAAgB,KAAKyG,YAAY,GAAGzC,YAAY,CAACD,UAAU,GAAIzD,UAAU,GAAG,SAAS,GAAG;kBACjG,CAAE;kBAAAoG,QAAA,gBAEF/G,OAAA;oBAAKwE,SAAS,EAAC,0CAA0C;oBAAAuC,QAAA,EACtDoB,YAAY,CAAC5D;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACN5E,OAAA;oBAAA+G,QAAA,EAAOD;kBAAY;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAftBkC,YAAY;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgBX,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLQ,eAAe,iBACdpF,OAAA;cAAKwE,SAAS,EAAE,gBAAgB7D,UAAU,GAAG,iBAAiB,GAAG,iBAAiB,EAAG;cAAAoG,QAAA,gBACnF/G,OAAA;gBAAIwE,SAAS,EAAE,sDAAsD7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;gBAAAoG,QAAA,EAAC;cAEvH;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5E,OAAA;gBAAKwE,SAAS,EAAC,WAAW;gBAAAuC,QAAA,EACvBL,MAAM,CAACC,IAAI,CAACvB,eAAe,CAACL,aAAa,CAAC,CAAC+C,GAAG,CAAEM,eAAe,iBAC9DpI,OAAA;kBAEEoH,OAAO,EAAEA,CAAA,KAAM5G,sBAAsB,CAAC4H,eAAe,CAAE;kBACvD5D,SAAS,EAAE,kGACTjE,mBAAmB,KAAK6H,eAAe,GACnC,aAAa,GACbzH,UAAU,GAAG,iCAAiC,GAAG,iCAAiC,EACrF;kBACHoF,KAAK,EAAE;oBACLkB,eAAe,EAAE1G,mBAAmB,KAAK6H,eAAe,GAAG/D,YAAY,CAACF,MAAM,GAAG,aAAa;oBAC9FrC,KAAK,EAAEvB,mBAAmB,KAAK6H,eAAe,GAAG/D,YAAY,CAACD,UAAU,GAAIzD,UAAU,GAAG,SAAS,GAAG;kBACvG,CAAE;kBAAAoG,QAAA,gBAEF/G,OAAA;oBAAA+G,QAAA,EAAOqB;kBAAe;oBAAA3D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9B5E,OAAA,CAAC3C,YAAY;oBAACmH,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA,GAb/BwD,eAAe;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcd,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAEN5E,OAAA;YAAKwE,SAAS,EAAC,yCAAyC;YAAAuC,QAAA,eACtD/G,OAAA;cAAKwE,SAAS,EAAC,KAAK;cAAAuC,QAAA,GAEjBhG,eAAe,KAAK,OAAO,iBAC1Bf,OAAA;gBAAA+G,QAAA,gBACE/G,OAAA;kBAAIwE,SAAS,EAAC,0CAA0C;kBAAAuC,QAAA,EAAC;gBAAK;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnE5E,OAAA;kBAAKwE,SAAS,EAAC,WAAW;kBAAAuC,QAAA,gBACxB/G,OAAA;oBAAKwE,SAAS,EAAC,gCAAgC;oBAAAuC,QAAA,gBAC7C/G,OAAA;sBAAKwE,SAAS,EAAC,kCAAkC;sBAAAuC,QAAA,gBAC/C/G,OAAA;wBAAKwE,SAAS,EAAC;sBAAkC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxD5E,OAAA;wBAAMwE,SAAS,EAAC,mCAAmC;wBAAAuC,QAAA,EAAC;sBAAmB;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9E5E,OAAA;wBAAMwE,SAAS,EAAC,uBAAuB;wBAAAuC,QAAA,EAAC;sBAAM;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACN5E,OAAA;sBAAGwE,SAAS,EAAC,uBAAuB;sBAAAuC,QAAA,EAAC;oBAErC;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,gCAAgC;oBAAAuC,QAAA,gBAC7C/G,OAAA;sBAAKwE,SAAS,EAAC,kCAAkC;sBAAAuC,QAAA,gBAC/C/G,OAAA;wBAAKwE,SAAS,EAAC;sBAAmC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzD5E,OAAA;wBAAMwE,SAAS,EAAC,mCAAmC;wBAAAuC,QAAA,EAAC;sBAAqB;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAChF5E,OAAA;wBAAMwE,SAAS,EAAC,uBAAuB;wBAAAuC,QAAA,EAAC;sBAAM;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACN5E,OAAA;sBAAGwE,SAAS,EAAC,uBAAuB;sBAAAuC,QAAA,EAAC;oBAErC;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA7D,eAAe,KAAK,MAAM,iBACzBf,OAAA;gBAAA+G,QAAA,gBACE/G,OAAA;kBAAIwE,SAAS,EAAC,0CAA0C;kBAAAuC,QAAA,EAAC;gBAAa;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3E5E,OAAA;kBAAKwE,SAAS,EAAC,WAAW;kBAAAuC,QAAA,gBACxB/G,OAAA;oBAAKwE,SAAS,EAAC,gCAAgC;oBAAAuC,QAAA,gBAC7C/G,OAAA;sBAAKwE,SAAS,EAAC,kCAAkC;sBAAAuC,QAAA,gBAC/C/G,OAAA;wBAAKwE,SAAS,EAAC,mEAAmE;wBAAAuC,QAAA,eAChF/G,OAAA,CAACxC,KAAK;0BAACgH,SAAS,EAAC;wBAAoB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC,eACN5E,OAAA;wBAAMwE,SAAS,EAAC,mCAAmC;wBAAAuC,QAAA,EAAC;sBAAY;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvE5E,OAAA;wBAAMwE,SAAS,EAAC,uBAAuB;wBAAAuC,QAAA,EAAC;sBAAG;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACN5E,OAAA;sBAAGwE,SAAS,EAAC,uBAAuB;sBAAAuC,QAAA,EAAC;oBAErC;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,kDAAkD;oBAAAuC,QAAA,gBAC/D/G,OAAA;sBAAKwE,SAAS,EAAC,kCAAkC;sBAAAuC,QAAA,gBAC/C/G,OAAA;wBAAKwE,SAAS,EAAC,mEAAmE;wBAAAuC,QAAA,eAChF/G,OAAA;0BAAMwE,SAAS,EAAC,oBAAoB;0BAAAuC,QAAA,EAAC;wBAAG;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C,CAAC,eACN5E,OAAA;wBAAMwE,SAAS,EAAC,mCAAmC;wBAAAuC,QAAA,EAAC;sBAAG;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9D5E,OAAA;wBAAMwE,SAAS,EAAC,uBAAuB;wBAAAuC,QAAA,EAAC;sBAAM;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACN5E,OAAA;sBAAGwE,SAAS,EAAC,uBAAuB;sBAAAuC,QAAA,EAAC;oBAErC;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5E,OAAA;kBAAKwE,SAAS,EAAC,MAAM;kBAAAuC,QAAA,eACnB/G,OAAA;oBACEmC,IAAI,EAAC,MAAM;oBACXgF,WAAW,EAAC,0BAA0B;oBACtC3C,SAAS,EAAC;kBAAgI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3I;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA7D,eAAe,KAAK,UAAU,iBAC7Bf,OAAA;gBAAA+G,QAAA,gBACE/G,OAAA;kBAAIwE,SAAS,EAAC,0CAA0C;kBAAAuC,QAAA,EAAC;gBAAQ;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtE5E,OAAA;kBAAKwE,SAAS,EAAC,WAAW;kBAAAuC,QAAA,gBACxB/G,OAAA;oBAAKwE,SAAS,EAAC,6BAA6B;oBAAAuC,QAAA,gBAC1C/G,OAAA;sBAAKwE,SAAS,EAAC,mCAAmC;sBAAAuC,QAAA,EAAC;oBAAgB;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzE5E,OAAA;sBAAKwE,SAAS,EAAC,4BAA4B;sBAAAuC,QAAA,EAAC;oBAAoB;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtE5E,OAAA;sBAAKwE,SAAS,EAAC,4BAA4B;sBAAAuC,QAAA,EAAC;oBAAU;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,6BAA6B;oBAAAuC,QAAA,gBAC1C/G,OAAA;sBAAKwE,SAAS,EAAC,oCAAoC;sBAAAuC,QAAA,EAAC;oBAAe;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzE5E,OAAA;sBAAKwE,SAAS,EAAC,4BAA4B;sBAAAuC,QAAA,EAAC;oBAAmB;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrE5E,OAAA;sBAAKwE,SAAS,EAAC,4BAA4B;sBAAAuC,QAAA,EAAC;oBAAM;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,6BAA6B;oBAAAuC,QAAA,gBAC1C/G,OAAA;sBAAKwE,SAAS,EAAC,qCAAqC;sBAAAuC,QAAA,EAAC;oBAAkB;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7E5E,OAAA;sBAAKwE,SAAS,EAAC,4BAA4B;sBAAAuC,QAAA,EAAC;oBAAsB;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxE5E,OAAA;sBAAKwE,SAAS,EAAC,4BAA4B;sBAAAuC,QAAA,EAAC;oBAAU;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA7D,eAAe,KAAK,OAAO,iBAC1Bf,OAAA;gBAAA+G,QAAA,gBACE/G,OAAA;kBAAIwE,SAAS,EAAC,0CAA0C;kBAAAuC,QAAA,EAAC;gBAAY;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1E5E,OAAA;kBAAKwE,SAAS,EAAC,kBAAkB;kBAAAuC,QAAA,gBAC/B/G,OAAA;oBAAKwE,SAAS,EAAC,uCAAuC;oBAAAuC,QAAA,EAAC;kBAAQ;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrE5E,OAAA;oBAAKwE,SAAS,EAAC,uBAAuB;oBAAAuC,QAAA,EAAC;kBAAgC;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACN5E,OAAA;kBAAKwE,SAAS,EAAC,WAAW;kBAAAuC,QAAA,gBACxB/G,OAAA;oBAAQwE,SAAS,EAAC,0FAA0F;oBAAAuC,QAAA,EAAC;kBAE7G;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5E,OAAA;oBAAQwE,SAAS,EAAC,sFAAsF;oBAAAuC,QAAA,EAAC;kBAEzG;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN5E,OAAA;kBAAKwE,SAAS,EAAC,MAAM;kBAAAuC,QAAA,gBACnB/G,OAAA;oBAAIwE,SAAS,EAAC,0CAA0C;oBAAAuC,QAAA,EAAC;kBAAgB;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9E5E,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAuC,QAAA,gBACxB/G,OAAA;sBAAKwE,SAAS,EAAC,6BAA6B;sBAAAuC,QAAA,gBAC1C/G,OAAA;wBAAKwE,SAAS,EAAC,uBAAuB;wBAAAuC,QAAA,EAAC;sBAAmB;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChE5E,OAAA;wBAAKwE,SAAS,EAAC,uBAAuB;wBAAAuC,QAAA,EAAC;sBAAM;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACN5E,OAAA;sBAAKwE,SAAS,EAAC,6BAA6B;sBAAAuC,QAAA,gBAC1C/G,OAAA;wBAAKwE,SAAS,EAAC,uBAAuB;wBAAAuC,QAAA,EAAC;sBAAW;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACxD5E,OAAA;wBAAKwE,SAAS,EAAC,uBAAuB;wBAAAuC,QAAA,EAAC;sBAAG;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA7D,eAAe,KAAK,UAAU,iBAC7Bf,OAAA;gBAAKwE,SAAS,EAAC,sBAAsB;gBAAAuC,QAAA,gBACnC/G,OAAA;kBAAKwE,SAAS,EAAC,wCAAwC;kBAAAuC,QAAA,gBACrD/G,OAAA;oBAAIwE,SAAS,EAAE,yBAAyB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;oBAAAoG,QAAA,EAAC;kBAAS;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxG5E,OAAA;oBAAKwE,SAAS,EAAC,6BAA6B;oBAAAuC,QAAA,eAC1C/G,OAAA;sBAAKwE,SAAS,EAAC,6BAA6B;sBAAAuC,QAAA,gBAC1C/G,OAAA;wBAAKwE,SAAS,EAAC;sBAAmC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzD5E,OAAA;wBAAMwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;wBAAAoG,QAAA,GAC1E5D,WAAW,CAACT,MAAM,EAAC,SACtB;sBAAA;wBAAA+B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5E,OAAA;kBAAKwE,SAAS,EAAC,MAAM;kBAAAuC,QAAA,gBACnB/G,OAAA;oBAAKwE,SAAS,EAAC,kCAAkC;oBAAAuC,QAAA,eAC/C/G,OAAA;sBAAMwE,SAAS,EAAE,uBAAuB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EAAC;oBAAY;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1G,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,sBAAsB;oBAAAuC,QAAA,EAClC5D,WAAW,CAAC2E,GAAG,CAAEO,MAAM,iBACtBrI,OAAA;sBAAqBwE,SAAS,EAAC,6BAA6B;sBAAAuC,QAAA,eAC1D/G,OAAA;wBAAKwE,SAAS,EAAE,qFAAqF6D,MAAM,CAACvG,KAAK,EAAG;wBAAAiF,QAAA,GACjHsB,MAAM,CAACxG,MAAM,eACd7B,OAAA;0BAAKwE,SAAS,EAAC;wBAAuF;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1G;oBAAC,GAJEyD,MAAM,CAAC1G,EAAE;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAKd,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5E,OAAA;kBAAKwE,SAAS,EAAC,6BAA6B;kBAAAuC,QAAA,gBAC1C/G,OAAA;oBACEwE,SAAS,EAAC,oEAAoE;oBAC9EuB,KAAK,EAAE;sBAAEkB,eAAe,EAAE5C,YAAY,CAACL;oBAAQ,CAAE;oBAAA+C,QAAA,EAClD;kBAED;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5E,OAAA;oBAAQwE,SAAS,EAAE,2DACjB7D,UAAU,GAAG,6CAA6C,GAAG,6CAA6C,EACzG;oBAAAoG,QAAA,EAAC;kBAEJ;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGN5E,OAAA;kBAAKwE,SAAS,EAAC,MAAM;kBAAAuC,QAAA,gBACnB/G,OAAA;oBAAIwE,SAAS,EAAE,sDAAsD7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;oBAAAoG,QAAA,EAAC;kBAEvH;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL5E,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAuC,QAAA,gBACxB/G,OAAA;sBAAKwE,SAAS,EAAE,uBAAuB7D,UAAU,GAAG,aAAa,GAAG,YAAY,EAAG;sBAAAoG,QAAA,gBACjF/G,OAAA;wBAAMwE,SAAS,EAAE,eAAe7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;wBAAAoG,QAAA,EAAC;sBAAK;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3F5E,OAAA;wBAAMwE,SAAS,EAAE,GAAG7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;wBAAAoG,QAAA,EAAC;sBAA2B;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG,CAAC,eACN5E,OAAA;sBAAKwE,SAAS,EAAE,uBAAuB7D,UAAU,GAAG,aAAa,GAAG,YAAY,EAAG;sBAAAoG,QAAA,gBACjF/G,OAAA;wBAAMwE,SAAS,EAAE,eAAe7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;wBAAAoG,QAAA,EAAC;sBAAI;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC1F5E,OAAA;wBAAMwE,SAAS,EAAE,GAAG7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;wBAAAoG,QAAA,EAAC;sBAAc;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5E,OAAA;kBAAKwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,cAAe;kBAAAoG,QAAA,GACrFtF,YAAY,CAACiB,MAAM,EAAC,iBACvB;gBAAA;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA7D,eAAe,KAAK,OAAO,iBAC1Bf,OAAA;gBAAA+G,QAAA,gBACE/G,OAAA;kBAAKwE,SAAS,EAAC,wCAAwC;kBAAAuC,QAAA,gBACrD/G,OAAA;oBAAIwE,SAAS,EAAE,yBAAyB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;oBAAAoG,QAAA,EAAC;kBAAK;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpG5E,OAAA;oBACEoH,OAAO,EAAEA,CAAA,KAAM9F,WAAW,CAAC,CAACD,QAAQ,CAAE;oBACtCmD,SAAS,EAAE,2DACTnD,QAAQ,GACJ,YAAY,GACZV,UAAU,GAAG,6CAA6C,GAAG,6CAA6C,EAC7G;oBACHoF,KAAK,EAAE1E,QAAQ,GAAG;sBAAE4F,eAAe,EAAE5C,YAAY,CAACL;oBAAQ,CAAC,GAAG,CAAC,CAAE;oBAAA+C,QAAA,EAEhE1F,QAAQ,GAAG,QAAQ,GAAG;kBAAQ;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAGLvD,QAAQ,iBACPrB,OAAA;kBAAKwE,SAAS,EAAC,MAAM;kBAAAuC,QAAA,gBACnB/G,OAAA;oBAAKwE,SAAS,EAAC,kCAAkC;oBAAAuC,QAAA,eAC/C/G,OAAA;sBAAMwE,SAAS,EAAE,uBAAuB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EAAC;oBAAU;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAuC,QAAA,gBACxB/G,OAAA;sBACEoH,OAAO,EAAEA,CAAA,KAAM5F,iBAAiB,CAAC,aAAa,CAAE;sBAChDgD,SAAS,EAAE,4FACTjD,cAAc,KAAK,aAAa,GAC5B,wBAAwB,GACxBZ,UAAU,GAAG,iCAAiC,GAAG,iCAAiC,EACrF;sBACHoF,KAAK,EAAExE,cAAc,KAAK,aAAa,GAAG;wBAAE0F,eAAe,EAAE5C,YAAY,CAACF,MAAM;wBAAErC,KAAK,EAAEuC,YAAY,CAACD;sBAAW,CAAC,GAAG,CAAC,CAAE;sBAAA2C,QAAA,gBAExH/G,OAAA,CAAClC,KAAK;wBAAC0G,SAAS,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7B5E,OAAA;wBAAA+G,QAAA,EAAM;sBAAW;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,EACRzB,WAAW,CAAC2E,GAAG,CAAEO,MAAM,iBACtBrI,OAAA;sBAEEoH,OAAO,EAAEA,CAAA,KAAM5F,iBAAiB,CAAC6G,MAAM,CAACjF,IAAI,CAAE;sBAC9CoB,SAAS,EAAE,4FACTjD,cAAc,KAAK8G,MAAM,CAACjF,IAAI,GAC1B,wBAAwB,GACxBzC,UAAU,GAAG,iCAAiC,GAAG,iCAAiC,EACrF;sBACHoF,KAAK,EAAExE,cAAc,KAAK8G,MAAM,CAACjF,IAAI,GAAG;wBAAE6D,eAAe,EAAE5C,YAAY,CAACF,MAAM;wBAAErC,KAAK,EAAEuC,YAAY,CAACD;sBAAW,CAAC,GAAG,CAAC,CAAE;sBAAA2C,QAAA,gBAEtH/G,OAAA;wBAAKwE,SAAS,EAAE,4EAA4E6D,MAAM,CAACvG,KAAK,EAAG;wBAAAiF,QAAA,EACxGsB,MAAM,CAACxG;sBAAM;wBAAA4C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX,CAAC,eACN5E,OAAA;wBAAA+G,QAAA,EAAOsB,MAAM,CAACjF;sBAAI;wBAAAqB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,GAZrByD,MAAM,CAAC1G,EAAE;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAaR,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAGD5E,OAAA;kBAAKwE,SAAS,EAAC,6BAA6B;kBAAAuC,QAAA,gBAC1C/G,OAAA;oBAAKwE,SAAS,EAAE,kCAAkC7D,UAAU,GAAG,6BAA6B,GAAG,iBAAiB,EAAG;oBAC9GoF,KAAK,EAAE,CAACpF,UAAU,GAAG;sBAAEuG,WAAW,EAAE7C,YAAY,CAACJ,YAAY,GAAG;oBAAK,CAAC,GAAG,CAAC,CAAE;oBAAA8C,QAAA,gBAC/E/G,OAAA;sBAAKwE,SAAS,EAAC,uBAAuB;sBAAAuC,QAAA,EAAC;oBAAK;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClD5E,OAAA;sBAAKwE,SAAS,EAAE,uBAAwB;sBAACuB,KAAK,EAAE;wBAAEjE,KAAK,EAAEuC,YAAY,CAACL;sBAAQ,CAAE;sBAAA+C,QAAA,EAC7EnD,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAAC9E,MAAM,KAAK,MAAM,CAAC,CAACd;oBAAM;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAE,kCAAkC7D,UAAU,GAAG,6BAA6B,GAAG,iBAAiB,EAAG;oBAC9GoF,KAAK,EAAE,CAACpF,UAAU,GAAG;sBAAEuG,WAAW,EAAE7C,YAAY,CAACJ,YAAY,GAAG;oBAAK,CAAC,GAAG,CAAC,CAAE;oBAAA8C,QAAA,gBAC/E/G,OAAA;sBAAKwE,SAAS,EAAC,uBAAuB;sBAAAuC,QAAA,EAAC;oBAAW;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxD5E,OAAA;sBAAKwE,SAAS,EAAE,uBAAwB;sBAACuB,KAAK,EAAE;wBAAEjE,KAAK,EAAEuC,YAAY,CAACL;sBAAQ,CAAE;sBAAA+C,QAAA,EAC7EnD,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAAC9E,MAAM,KAAK,aAAa,CAAC,CAACd;oBAAM;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAE,kCAAkC7D,UAAU,GAAG,6BAA6B,GAAG,iBAAiB,EAAG;oBAC9GoF,KAAK,EAAE,CAACpF,UAAU,GAAG;sBAAEuG,WAAW,EAAE7C,YAAY,CAACJ,YAAY,GAAG;oBAAK,CAAC,GAAG,CAAC,CAAE;oBAAA8C,QAAA,gBAC/E/G,OAAA;sBAAKwE,SAAS,EAAC,uBAAuB;sBAAAuC,QAAA,EAAC;oBAAI;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjD5E,OAAA;sBAAKwE,SAAS,EAAE,uBAAwB;sBAACuB,KAAK,EAAE;wBAAEjE,KAAK,EAAEuC,YAAY,CAACL;sBAAQ,CAAE;sBAAA+C,QAAA,EAC7EnD,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAAC9E,MAAM,KAAK,WAAW,CAAC,CAACd;oBAAM;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5E,OAAA;kBAAKwE,SAAS,EAAC,WAAW;kBAAAuC,QAAA,gBACxB/G,OAAA;oBAAQwE,SAAS,EAAC,mHAAmH;oBAAAuC,QAAA,gBACnI/G,OAAA,CAACN,IAAI;sBAAC8E,SAAS,EAAC;oBAAqB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,YAE1C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAET5E,OAAA;oBAAKwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,cAAe;oBAAAoG,QAAA,EACrF1F,QAAQ,IAAIE,cAAc,KAAK,aAAa,GACzC,WAAWA,cAAc,UAAU,GACnC,GAAGqC,gBAAgB,CAAC,CAAC,CAAClB,MAAM;kBAAc;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAE3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA7D,eAAe,KAAK,OAAO,iBAC1Bf,OAAA;gBAAA+G,QAAA,gBACE/G,OAAA;kBAAIwE,SAAS,EAAC,0CAA0C;kBAAAuC,QAAA,EAAC;gBAAK;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnE5E,OAAA;kBAAKwE,SAAS,EAAC,WAAW;kBAAAuC,QAAA,gBACxB/G,OAAA;oBAAKwE,SAAS,EAAC,6EAA6E;oBAAAuC,QAAA,gBAC1F/G,OAAA;sBAAKwE,SAAS,EAAC,mCAAmC;sBAAAuC,QAAA,EAAC;oBAAY;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrE5E,OAAA;sBAAKwE,SAAS,EAAC,4BAA4B;sBAAAuC,QAAA,EAAC;oBAAqB;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvE5E,OAAA;sBAAKwE,SAAS,EAAC,wBAAwB;sBAAAuC,QAAA,gBACrC/G,OAAA;wBAAKwE,SAAS,EAAC,4CAA4C;wBAAAuC,QAAA,eACzD/G,OAAA;0BAAKwE,SAAS,EAAC,iCAAiC;0BAACuB,KAAK,EAAE;4BAAC0B,KAAK,EAAE;0BAAK;wBAAE;0BAAAhD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3E,CAAC,eACN5E,OAAA;wBAAMwE,SAAS,EAAC,wBAAwB;wBAAAuC,QAAA,EAAC;sBAAG;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,4EAA4E;oBAAAuC,QAAA,gBACzF/G,OAAA;sBAAKwE,SAAS,EAAC,mCAAmC;sBAAAuC,QAAA,EAAC;oBAAiB;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1E5E,OAAA;sBAAKwE,SAAS,EAAC,4BAA4B;sBAAAuC,QAAA,EAAC;oBAAa;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/D5E,OAAA;sBAAKwE,SAAS,EAAC,wBAAwB;sBAAAuC,QAAA,gBACrC/G,OAAA;wBAAKwE,SAAS,EAAC,4CAA4C;wBAAAuC,QAAA,eACzD/G,OAAA;0BAAKwE,SAAS,EAAC,gCAAgC;0BAACuB,KAAK,EAAE;4BAAC0B,KAAK,EAAE;0BAAK;wBAAE;0BAAAhD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1E,CAAC,eACN5E,OAAA;wBAAMwE,SAAS,EAAC,uBAAuB;wBAAAuC,QAAA,EAAC;sBAAG;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,8EAA8E;oBAAAuC,QAAA,gBAC3F/G,OAAA;sBAAKwE,SAAS,EAAC,mCAAmC;sBAAAuC,QAAA,EAAC;oBAAkB;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC3E5E,OAAA;sBAAKwE,SAAS,EAAC,4BAA4B;sBAAAuC,QAAA,EAAC;oBAAsB;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxE5E,OAAA;sBAAKwE,SAAS,EAAC,wBAAwB;sBAAAuC,QAAA,gBACrC/G,OAAA;wBAAKwE,SAAS,EAAC,4CAA4C;wBAAAuC,QAAA,eACzD/G,OAAA;0BAAKwE,SAAS,EAAC,kCAAkC;0BAACuB,KAAK,EAAE;4BAAC0B,KAAK,EAAE;0BAAK;wBAAE;0BAAAhD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5E,CAAC,eACN5E,OAAA;wBAAMwE,SAAS,EAAC,yBAAyB;wBAAAuC,QAAA,EAAC;sBAAG;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5E,OAAA;kBAAQwE,SAAS,EAAC,wHAAwH;kBAAAuC,QAAA,gBACxI/G,OAAA,CAACN,IAAI;oBAAC8E,SAAS,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN5E,OAAA;UAAKwE,SAAS,EAAE,2CAA2C7D,UAAU,GAAG,aAAa,GAAG,UAAU,EAAG;UAAAoG,QAAA,GAElGhG,eAAe,KAAK,UAAU,iBAC7Bf,OAAA;YAAKwE,SAAS,EAAC,sBAAsB;YAAAuC,QAAA,gBAEnC/G,OAAA;cAAKwE,SAAS,EAAE,gBAAgB7D,UAAU,GAAG,6BAA6B,GAAG,iBAAiB,EAAG;cAC5FoF,KAAK,EAAE,CAACpF,UAAU,GAAG;gBAAEsG,eAAe,EAAE5C,YAAY,CAACF;cAAO,CAAC,GAAG,CAAC,CAAE;cAAA4C,QAAA,eACtE/G,OAAA;gBAAKwE,SAAS,EAAC,mCAAmC;gBAAAuC,QAAA,gBAChD/G,OAAA;kBAAKwE,SAAS,EAAC,6BAA6B;kBAAAuC,QAAA,gBAC1C/G,OAAA;oBACEwE,SAAS,EAAC,kEAAkE;oBAC5EuB,KAAK,EAAE;sBAAEkB,eAAe,EAAE5C,YAAY,CAACL;oBAAQ,CAAE;oBAAA+C,QAAA,eAEjD/G,OAAA,CAACZ,aAAa;sBAACoF,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,eACN5E,OAAA;oBAAA+G,QAAA,gBACE/G,OAAA;sBAAIwE,SAAS,EAAE,yBAAyB7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EAAC;oBAEvF;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL5E,OAAA;sBAAGwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAoG,QAAA,GACvE5D,WAAW,CAACT,MAAM,EAAC,kBAAW,EAACS,WAAW,CAACT,MAAM,EAAC,SACrD;oBAAA;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5E,OAAA;kBAAKwE,SAAS,EAAC,6BAA6B;kBAAAuC,QAAA,gBAC1C/G,OAAA;oBACEwE,SAAS,EAAE,oCACT7D,UAAU,GAAG,iCAAiC,GAAG,iCAAiC,EACjF;oBAAAoG,QAAA,eAEH/G,OAAA,CAACpB,MAAM;sBAAC4F,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACT5E,OAAA;oBACEwE,SAAS,EAAE,oCACT7D,UAAU,GAAG,iCAAiC,GAAG,iCAAiC,EACjF;oBAAAoG,QAAA,eAEH/G,OAAA,CAAChB,cAAc;sBAACwF,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5E,OAAA;cAAKwE,SAAS,EAAE,wCAAwC7D,UAAU,GAAG,aAAa,GAAG,UAAU,EAAG;cAAAoG,QAAA,EAC/FtF,YAAY,CAACqG,GAAG,CAAC,CAACS,GAAG,EAAEC,KAAK,kBAC3BxI,OAAA;gBAAkBwE,SAAS,EAAC,4BAA4B;gBAAAuC,QAAA,gBACtD/G,OAAA;kBAAKwE,SAAS,EAAE,0FAA0F+D,GAAG,CAACzG,KAAK,EAAG;kBAAAiF,QAAA,EACnHwB,GAAG,CAAC1G;gBAAM;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACN5E,OAAA;kBAAKwE,SAAS,EAAC,gBAAgB;kBAAAuC,QAAA,gBAC7B/G,OAAA;oBAAKwE,SAAS,EAAC,kCAAkC;oBAAAuC,QAAA,gBAC/C/G,OAAA;sBAAMwE,SAAS,EAAE,uBAAuB7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EACnFwB,GAAG,CAAC3G;oBAAM;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACP5E,OAAA;sBAAMwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EAC1EpE,UAAU,CAAC4F,GAAG,CAACvG,SAAS;oBAAC;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN5E,OAAA;oBACEwE,SAAS,EAAE,8CACT+D,GAAG,CAAC3G,MAAM,KAAK,UAAU,GACrB,oBAAoB,GACpBjB,UAAU,GAAG,2BAA2B,GAAG,2BAA2B,EACzE;oBACHoF,KAAK,EAAEwC,GAAG,CAAC3G,MAAM,KAAK,UAAU,GAAG;sBAAEqF,eAAe,EAAE5C,YAAY,CAACL;oBAAQ,CAAC,GAAG,CAAC,CAAE;oBAAA+C,QAAA,eAElF/G,OAAA;sBAAGwE,SAAS,EAAC,SAAS;sBAAAuC,QAAA,EAAEwB,GAAG,CAACxG;oBAAO;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAvBE2D,GAAG,CAAC5G,EAAE;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBX,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN5E,OAAA;cAAKwE,SAAS,EAAE,gBAAgB7D,UAAU,GAAG,6BAA6B,GAAG,4BAA4B,EAAG;cAAAoG,QAAA,gBAC1G/G,OAAA;gBAAKwE,SAAS,EAAC,6BAA6B;gBAAAuC,QAAA,gBAC1C/G,OAAA;kBAAKwE,SAAS,EAAC,6BAA6B;kBAAAuC,QAAA,eAC1C/G,OAAA;oBACEwE,SAAS,EAAE,oCACT7D,UAAU,GAAG,iCAAiC,GAAG,iCAAiC,EACjF;oBAAAoG,QAAA,eAEH/G,OAAA,CAACN,IAAI;sBAAC8E,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN5E,OAAA;kBAAKwE,SAAS,EAAC,iBAAiB;kBAAAuC,QAAA,eAC9B/G,OAAA;oBACEmC,IAAI,EAAC,MAAM;oBACXsG,KAAK,EAAErG,UAAW;oBAClBsG,QAAQ,EAAGnD,CAAC,IAAKlD,aAAa,CAACkD,CAAC,CAAC+B,MAAM,CAACmB,KAAK,CAAE;oBAC/CE,UAAU,EAAGpD,CAAC,IAAKA,CAAC,CAACqD,GAAG,KAAK,OAAO,IAAIpG,UAAU,CAAC,CAAE;oBACrD2E,WAAW,EAAC,mBAAmB;oBAC/B3C,SAAS,EAAE,sEACT7D,UAAU,GACN,iFAAiF,GACjF,iFAAiF,EACpF;oBACHoF,KAAK,EAAE,CAACpF,UAAU,GAAG;sBAAEkI,cAAc,EAAExE,YAAY,CAACL;oBAAQ,CAAC,GAAG,CAAC;kBAAE;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5E,OAAA;kBACEoH,OAAO,EAAE5E,UAAW;kBACpBsG,QAAQ,EAAE,CAAC1G,UAAU,CAACK,IAAI,CAAC,CAAE;kBAC7B+B,SAAS,EAAE,+CACTpC,UAAU,CAACK,IAAI,CAAC,CAAC,GACb,aAAa,GACb,+BAA+B,EAClC;kBACHsD,KAAK,EAAE;oBAAEkB,eAAe,EAAE5C,YAAY,CAACL;kBAAQ,CAAE;kBAAA+C,QAAA,eAEjD/G,OAAA,CAACZ,aAAa;oBAACoF,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGN5E,OAAA;gBAAKwE,SAAS,EAAE,gBAAgB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;gBAAAoG,QAAA,eAC/E/G,OAAA;kBAAMwE,SAAS,EAAC,6BAA6B;kBAAAuC,QAAA,gBAC3C/G,OAAA;oBAAKwE,SAAS,EAAC,gBAAgB;oBAAAuC,QAAA,gBAC7B/G,OAAA;sBAAKwE,SAAS,EAAC;oBAAiD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACvE5E,OAAA;sBAAKwE,SAAS,EAAC,iDAAiD;sBAACuB,KAAK,EAAE;wBAACgD,cAAc,EAAE;sBAAM;oBAAE;sBAAAtE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxG5E,OAAA;sBAAKwE,SAAS,EAAC,iDAAiD;sBAACuB,KAAK,EAAE;wBAACgD,cAAc,EAAE;sBAAM;oBAAE;sBAAAtE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrG,CAAC,eACN5E,OAAA;oBAAA+G,QAAA,EAAM;kBAAkB;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA7D,eAAe,KAAK,OAAO,iBAC1Bf,OAAA;YAAKwE,SAAS,EAAC,KAAK;YAAAuC,QAAA,eAClB/G,OAAA;cAAKwE,SAAS,EAAC,WAAW;cAAAuC,QAAA,gBAExB/G,OAAA;gBAAKwE,SAAS,EAAC,MAAM;gBAAAuC,QAAA,eACnB/G,OAAA;kBAAKwE,SAAS,EAAC,wCAAwC;kBAAAuC,QAAA,gBACrD/G,OAAA;oBAAA+G,QAAA,gBACE/G,OAAA;sBAAIwE,SAAS,EAAE,+BAA+B7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EACzF1F,QAAQ,IAAIE,cAAc,KAAK,aAAa,GAAG,GAAGA,cAAc,UAAU,GAAG;oBAAW;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvF,CAAC,eACL5E,OAAA;sBAAGwE,SAAS,EAAE,GAAG7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EAC/D1F,QAAQ,GACLE,cAAc,KAAK,aAAa,GAC9B,uCAAuC,GACvC,qBAAqBA,cAAc,EAAE,GACvC;oBAAyB;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,6BAA6B;oBAAAuC,QAAA,gBAC1C/G,OAAA;sBAAQwE,SAAS,EAAE,iDACjB7D,UAAU,GAAG,iDAAiD,GAAG,gDAAgD,EAChH;sBAAAoG,QAAA,EAAC;oBAEJ;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT5E,OAAA;sBACEwE,SAAS,EAAC,+EAA+E;sBACzFuB,KAAK,EAAE;wBACLkB,eAAe,EAAE5C,YAAY,CAACL,OAAO;wBACrC,SAAS,EAAE;0BAAEiD,eAAe,EAAE5C,YAAY,CAACH;wBAAY;sBACzD,CAAE;sBACFmD,YAAY,EAAG9B,CAAC,IAAKA,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACkB,eAAe,GAAG5C,YAAY,CAACH,WAAY;sBAC/EqD,YAAY,EAAGhC,CAAC,IAAKA,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACkB,eAAe,GAAG5C,YAAY,CAACL,OAAQ;sBAAA+C,QAAA,gBAE3E/G,OAAA,CAACN,IAAI;wBAAC8E,SAAS,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC5B5E,OAAA;wBAAA+G,QAAA,EAAM;sBAAQ;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN5E,OAAA;gBAAKwE,SAAS,EAAC,uCAAuC;gBAAAuC,QAAA,gBAEpD/G,OAAA;kBAAKwE,SAAS,EAAE,gBAAiB;kBAACuB,KAAK,EAAE;oBAAEkB,eAAe,EAAEtG,UAAU,GAAG,SAAS,GAAG0D,YAAY,CAACF;kBAAO,CAAE;kBAAA4C,QAAA,gBACzG/G,OAAA;oBAAKwE,SAAS,EAAC,wCAAwC;oBAAAuC,QAAA,gBACrD/G,OAAA;sBAAIwE,SAAS,EAAE,iBAAiB7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EAAC;oBAE/E;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL5E,OAAA;sBACEwE,SAAS,EAAC,kDAAkD;sBAC5DuB,KAAK,EAAE;wBAAEkB,eAAe,EAAE5C,YAAY,CAACL;sBAAQ,CAAE;sBAAA+C,QAAA,EAEhDnD,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAAC9E,MAAM,KAAK,MAAM,CAAC,CAACd;oBAAM;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAuC,QAAA,GACvBnD,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACN,MAAM,KAAK,MAAM,CAAC,CAACsE,GAAG,CAAEhE,IAAI;sBAAA,IAAAkF,iBAAA,EAAAC,kBAAA;sBAAA,oBAClEjJ,OAAA;wBAAmBwE,SAAS,EAAE,uEAC5B7D,UAAU,GAAG,mDAAmD,GAAG,0CAA0C,EAC5G;wBACHoF,KAAK,EAAE,CAACpF,UAAU,GAAG;0BAAEuG,WAAW,EAAE7C,YAAY,CAACJ,YAAY,GAAG;wBAAK,CAAC,GAAG,CAAC,CAAE;wBAC5EoD,YAAY,EAAG9B,CAAC,IAAK;0BACnB,IAAI,CAAC5E,UAAU,EAAE;4BACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmB,WAAW,GAAG7C,YAAY,CAACJ,YAAY,GAAG,IAAI;4BAC7DsB,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmD,SAAS,GAAG,kBAAkB;0BAC/C;wBACF,CAAE;wBACF3B,YAAY,EAAGhC,CAAC,IAAK;0BACnB,IAAI,CAAC5E,UAAU,EAAE;4BACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmB,WAAW,GAAG7C,YAAY,CAACJ,YAAY,GAAG,IAAI;4BAC7DsB,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmD,SAAS,GAAG,eAAe;0BAC5C;wBACF,CAAE;wBAAAnC,QAAA,gBAEA/G,OAAA;0BAAKwE,SAAS,EAAC,uCAAuC;0BAAAuC,QAAA,gBACpD/G,OAAA;4BAAIwE,SAAS,EAAE,uBAAuB7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;4BAAAoG,QAAA,EACjFjD,IAAI,CAACR;0BAAK;4BAAAmB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT,CAAC,eACL5E,OAAA;4BAAMwE,SAAS,EAAE,yCACfV,IAAI,CAACL,QAAQ,KAAK,MAAM,GACpB,yBAAyB,GACzBK,IAAI,CAACL,QAAQ,KAAK,QAAQ,GAC1B,+BAA+B,GAC/B,2BAA2B,EAC9B;4BAAAsD,QAAA,EACAjD,IAAI,CAACL;0BAAQ;4BAAAgB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACN5E,OAAA;0BAAGwE,SAAS,EAAE,gBAAgB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;0BAAAoG,QAAA,EAC5EjD,IAAI,CAACH;wBAAW;0BAAAc,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB,CAAC,eACJ5E,OAAA;0BAAKwE,SAAS,EAAC,mCAAmC;0BAAAuC,QAAA,GAC/C1F,QAAQ,iBACPrB,OAAA;4BAAKwE,SAAS,EAAC,6BAA6B;4BAAAuC,QAAA,gBAC1C/G,OAAA;8BAAKwE,SAAS,EAAE,4EACd,EAAAwE,iBAAA,GAAA7F,WAAW,CAACgG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChG,IAAI,KAAKU,IAAI,CAACP,QAAQ,CAAC,cAAAyF,iBAAA,uBAA/CA,iBAAA,CAAiDlH,KAAK,KAAI,aAAa,EACtE;8BAAAiF,QAAA,EACA,EAAAkC,kBAAA,GAAA9F,WAAW,CAACgG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChG,IAAI,KAAKU,IAAI,CAACP,QAAQ,CAAC,cAAA0F,kBAAA,uBAA/CA,kBAAA,CAAiDpH,MAAM,KAAI;4BAAG;8BAAA4C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5D,CAAC,eACN5E,OAAA;8BAAMwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;8BAAAoG,QAAA,EAC1EjD,IAAI,CAACP;4BAAQ;8BAAAkB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CACN,eACD5E,OAAA;4BAAMwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;4BAAAoG,QAAA,EAC1E,IAAI9E,IAAI,CAAC6B,IAAI,CAACJ,OAAO,CAAC,CAACR,kBAAkB,CAAC;0BAAC;4BAAAuB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA,GAlDEd,IAAI,CAACnC,EAAE;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAmDZ,CAAC;oBAAA,CACP,CAAC,EACDhB,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAAC9E,MAAM,KAAK,MAAM,CAAC,CAACd,MAAM,KAAK,CAAC,iBAC/D1C,OAAA;sBAAKwE,SAAS,EAAE,oBAAoB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAoG,QAAA,gBACnF/G,OAAA,CAACL,MAAM;wBAAC6E,SAAS,EAAC;sBAAiC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACtD5E,OAAA;wBAAGwE,SAAS,EAAC,SAAS;wBAAAuC,QAAA,EAAC;sBAAc;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5E,OAAA;kBAAKwE,SAAS,EAAE,gBAAiB;kBAACuB,KAAK,EAAE;oBAAEkB,eAAe,EAAEtG,UAAU,GAAG,SAAS,GAAG0D,YAAY,CAACF;kBAAO,CAAE;kBAAA4C,QAAA,gBACzG/G,OAAA;oBAAKwE,SAAS,EAAC,wCAAwC;oBAAAuC,QAAA,gBACrD/G,OAAA;sBAAIwE,SAAS,EAAE,iBAAiB7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EAAC;oBAE/E;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL5E,OAAA;sBACEwE,SAAS,EAAC,kDAAkD;sBAC5DuB,KAAK,EAAE;wBAAEkB,eAAe,EAAE5C,YAAY,CAACL;sBAAQ,CAAE;sBAAA+C,QAAA,EAEhDnD,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAAC9E,MAAM,KAAK,aAAa,CAAC,CAACd;oBAAM;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAuC,QAAA,GACvBnD,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACN,MAAM,KAAK,aAAa,CAAC,CAACsE,GAAG,CAAEhE,IAAI;sBAAA,IAAAuF,kBAAA,EAAAC,kBAAA;sBAAA,oBACzEtJ,OAAA;wBAAmBwE,SAAS,EAAE,uEAC5B7D,UAAU,GAAG,mDAAmD,GAAG,0CAA0C,EAC5G;wBACHoF,KAAK,EAAE,CAACpF,UAAU,GAAG;0BAAEuG,WAAW,EAAE7C,YAAY,CAACJ,YAAY,GAAG;wBAAK,CAAC,GAAG,CAAC,CAAE;wBAC5EoD,YAAY,EAAG9B,CAAC,IAAK;0BACnB,IAAI,CAAC5E,UAAU,EAAE;4BACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmB,WAAW,GAAG7C,YAAY,CAACJ,YAAY,GAAG,IAAI;4BAC7DsB,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmD,SAAS,GAAG,kBAAkB;0BAC/C;wBACF,CAAE;wBACF3B,YAAY,EAAGhC,CAAC,IAAK;0BACnB,IAAI,CAAC5E,UAAU,EAAE;4BACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmB,WAAW,GAAG7C,YAAY,CAACJ,YAAY,GAAG,IAAI;4BAC7DsB,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmD,SAAS,GAAG,eAAe;0BAC5C;wBACF,CAAE;wBAAAnC,QAAA,gBAEA/G,OAAA;0BAAKwE,SAAS,EAAC,uCAAuC;0BAAAuC,QAAA,gBACpD/G,OAAA;4BAAIwE,SAAS,EAAE,uBAAuB7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;4BAAAoG,QAAA,EACjFjD,IAAI,CAACR;0BAAK;4BAAAmB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT,CAAC,eACL5E,OAAA;4BAAMwE,SAAS,EAAE,yCACfV,IAAI,CAACL,QAAQ,KAAK,MAAM,GACpB,yBAAyB,GACzBK,IAAI,CAACL,QAAQ,KAAK,QAAQ,GAC1B,+BAA+B,GAC/B,2BAA2B,EAC9B;4BAAAsD,QAAA,EACAjD,IAAI,CAACL;0BAAQ;4BAAAgB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACN5E,OAAA;0BAAGwE,SAAS,EAAE,gBAAgB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;0BAAAoG,QAAA,EAC5EjD,IAAI,CAACH;wBAAW;0BAAAc,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB,CAAC,eACJ5E,OAAA;0BAAKwE,SAAS,EAAC,mCAAmC;0BAAAuC,QAAA,GAC/C1F,QAAQ,iBACPrB,OAAA;4BAAKwE,SAAS,EAAC,6BAA6B;4BAAAuC,QAAA,gBAC1C/G,OAAA;8BAAKwE,SAAS,EAAE,4EACd,EAAA6E,kBAAA,GAAAlG,WAAW,CAACgG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChG,IAAI,KAAKU,IAAI,CAACP,QAAQ,CAAC,cAAA8F,kBAAA,uBAA/CA,kBAAA,CAAiDvH,KAAK,KAAI,aAAa,EACtE;8BAAAiF,QAAA,EACA,EAAAuC,kBAAA,GAAAnG,WAAW,CAACgG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChG,IAAI,KAAKU,IAAI,CAACP,QAAQ,CAAC,cAAA+F,kBAAA,uBAA/CA,kBAAA,CAAiDzH,MAAM,KAAI;4BAAG;8BAAA4C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5D,CAAC,eACN5E,OAAA;8BAAMwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;8BAAAoG,QAAA,EAC1EjD,IAAI,CAACP;4BAAQ;8BAAAkB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CACN,eACD5E,OAAA;4BAAMwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;4BAAAoG,QAAA,EAC1E,IAAI9E,IAAI,CAAC6B,IAAI,CAACJ,OAAO,CAAC,CAACR,kBAAkB,CAAC;0BAAC;4BAAAuB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA,GAlDEd,IAAI,CAACnC,EAAE;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAmDZ,CAAC;oBAAA,CACP,CAAC,EACDhB,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAAC9E,MAAM,KAAK,aAAa,CAAC,CAACd,MAAM,KAAK,CAAC,iBACtE1C,OAAA;sBAAKwE,SAAS,EAAE,oBAAoB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAoG,QAAA,gBACnF/G,OAAA;wBAAKwE,SAAS,EAAC;sBAAuE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7F5E,OAAA;wBAAGwE,SAAS,EAAC,SAAS;wBAAAuC,QAAA,EAAC;sBAAoB;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5E,OAAA;kBAAKwE,SAAS,EAAE,gBAAiB;kBAACuB,KAAK,EAAE;oBAAEkB,eAAe,EAAEtG,UAAU,GAAG,SAAS,GAAG0D,YAAY,CAACF;kBAAO,CAAE;kBAAA4C,QAAA,gBACzG/G,OAAA;oBAAKwE,SAAS,EAAC,wCAAwC;oBAAAuC,QAAA,gBACrD/G,OAAA;sBAAIwE,SAAS,EAAE,iBAAiB7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EAAC;oBAE/E;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL5E,OAAA;sBACEwE,SAAS,EAAC,kDAAkD;sBAC5DuB,KAAK,EAAE;wBAAEkB,eAAe,EAAE5C,YAAY,CAACL;sBAAQ,CAAE;sBAAA+C,QAAA,EAEhDnD,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAAC9E,MAAM,KAAK,WAAW,CAAC,CAACd;oBAAM;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN5E,OAAA;oBAAKwE,SAAS,EAAC,WAAW;oBAAAuC,QAAA,GACvBnD,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACN,MAAM,KAAK,WAAW,CAAC,CAACsE,GAAG,CAAEhE,IAAI;sBAAA,IAAAyF,kBAAA,EAAAC,kBAAA;sBAAA,oBACvExJ,OAAA;wBAAmBwE,SAAS,EAAE,uEAC5B7D,UAAU,GAAG,mDAAmD,GAAG,0CAA0C,EAC5G;wBACHoF,KAAK,EAAE,CAACpF,UAAU,GAAG;0BAAEuG,WAAW,EAAE7C,YAAY,CAACJ,YAAY,GAAG;wBAAK,CAAC,GAAG,CAAC,CAAE;wBAC5EoD,YAAY,EAAG9B,CAAC,IAAK;0BACnB,IAAI,CAAC5E,UAAU,EAAE;4BACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmB,WAAW,GAAG7C,YAAY,CAACJ,YAAY,GAAG,IAAI;4BAC7DsB,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmD,SAAS,GAAG,kBAAkB;0BAC/C;wBACF,CAAE;wBACF3B,YAAY,EAAGhC,CAAC,IAAK;0BACnB,IAAI,CAAC5E,UAAU,EAAE;4BACf4E,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmB,WAAW,GAAG7C,YAAY,CAACJ,YAAY,GAAG,IAAI;4BAC7DsB,CAAC,CAAC+B,MAAM,CAACvB,KAAK,CAACmD,SAAS,GAAG,eAAe;0BAC5C;wBACF,CAAE;wBAAAnC,QAAA,gBAEA/G,OAAA;0BAAKwE,SAAS,EAAC,uCAAuC;0BAAAuC,QAAA,gBACpD/G,OAAA;4BAAIwE,SAAS,EAAE,oCAAoC7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;4BAAAoG,QAAA,EACjGjD,IAAI,CAACR;0BAAK;4BAAAmB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT,CAAC,eACL5E,OAAA,CAACJ,YAAY;4BACX4E,SAAS,EAAC,SAAS;4BACnBuB,KAAK,EAAE;8BAAEjE,KAAK,EAAEuC,YAAY,CAACL;4BAAQ;0BAAE;4BAAAS,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACN5E,OAAA;0BAAGwE,SAAS,EAAE,gBAAgB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;0BAAAoG,QAAA,EAC5EjD,IAAI,CAACH;wBAAW;0BAAAc,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB,CAAC,eACJ5E,OAAA;0BAAKwE,SAAS,EAAC,mCAAmC;0BAAAuC,QAAA,GAC/C1F,QAAQ,iBACPrB,OAAA;4BAAKwE,SAAS,EAAC,6BAA6B;4BAAAuC,QAAA,gBAC1C/G,OAAA;8BAAKwE,SAAS,EAAE,4EACd,EAAA+E,kBAAA,GAAApG,WAAW,CAACgG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChG,IAAI,KAAKU,IAAI,CAACP,QAAQ,CAAC,cAAAgG,kBAAA,uBAA/CA,kBAAA,CAAiDzH,KAAK,KAAI,aAAa,EACtE;8BAAAiF,QAAA,EACA,EAAAyC,kBAAA,GAAArG,WAAW,CAACgG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChG,IAAI,KAAKU,IAAI,CAACP,QAAQ,CAAC,cAAAiG,kBAAA,uBAA/CA,kBAAA,CAAiD3H,MAAM,KAAI;4BAAG;8BAAA4C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5D,CAAC,eACN5E,OAAA;8BAAMwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;8BAAAoG,QAAA,EAC1EjD,IAAI,CAACP;4BAAQ;8BAAAkB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CACN,eACD5E,OAAA;4BAAMwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;4BAAAoG,QAAA,EAC1E,IAAI9E,IAAI,CAAC6B,IAAI,CAACJ,OAAO,CAAC,CAACR,kBAAkB,CAAC;0BAAC;4BAAAuB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA,GA7CEd,IAAI,CAACnC,EAAE;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA8CZ,CAAC;oBAAA,CACP,CAAC,EACDhB,gBAAgB,CAAC,CAAC,CAACC,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAAC9E,MAAM,KAAK,WAAW,CAAC,CAACd,MAAM,KAAK,CAAC,iBACpE1C,OAAA;sBAAKwE,SAAS,EAAE,oBAAoB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAoG,QAAA,gBACnF/G,OAAA,CAACJ,YAAY;wBAAC4E,SAAS,EAAC;sBAAiC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC5D5E,OAAA;wBAAGwE,SAAS,EAAC,SAAS;wBAAAuC,QAAA,EAAC;sBAAkB;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA7D,eAAe,KAAK,MAAM,IAAIsE,kBAAkB,KAC/CA,kBAAkB,CAACoE,SAAS,GAC1BpE,kBAAkB,CAACoE,SAAS,gBAE9BzJ,OAAA;YAAKwE,SAAS,EAAC,KAAK;YAAAuC,QAAA,eAClB/G,OAAA;cAAKwE,SAAS,EAAC,WAAW;cAAAuC,QAAA,gBAExB/G,OAAA;gBAAKwE,SAAS,EAAC,MAAM;gBAAAuC,QAAA,gBACnB/G,OAAA;kBAAKwE,SAAS,EAAE,4CAA4C7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;kBAAAoG,QAAA,gBAC3G/G,OAAA;oBAAA+G,QAAA,EAAO5G;kBAAa;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5B5E,OAAA,CAAC3C,YAAY;oBAACmH,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpC5E,OAAA;oBAAA+G,QAAA,EAAO1G;kBAAgB;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/B5E,OAAA,CAAC3C,YAAY;oBAACmH,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpC5E,OAAA;oBAAA+G,QAAA,EAAOxG;kBAAmB;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACN5E,OAAA;kBAAIwE,SAAS,EAAE,+BAA+B7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;kBAAAoG,QAAA,EAAExG;gBAAmB;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvH5E,OAAA;kBAAGwE,SAAS,EAAE,QAAQ7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;kBAAAoG,QAAA,EAAE1B,kBAAkB,CAAC1B;gBAAW;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAE5G5E,OAAA;kBAAKwE,SAAS,EAAC,6BAA6B;kBAAAuC,QAAA,gBAC1C/G,OAAA;oBAAMwE,SAAS,EAAE,8CAA8C8B,kBAAkB,CAACjB,kBAAkB,CAACH,UAAU,CAAC,EAAG;oBAAA6B,QAAA,EAChH1B,kBAAkB,CAACH;kBAAU;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACP5E,OAAA;oBAAKwE,SAAS,EAAC,6BAA6B;oBAAAuC,QAAA,gBAC1C/G,OAAA;sBAAMwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EAAC;oBAAW;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChG5E,OAAA;sBAAKwE,SAAS,EAAC,sBAAsB;sBAAAuC,QAAA,GAClC1B,kBAAkB,CAACJ,SAAS,CAACyE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC5B,GAAG,CAAC,CAAC6B,IAAI,EAAEnB,KAAK,kBACxDxI,OAAA;wBAAkBwE,SAAS,EAAE,oCAC3B7D,UAAU,GAAG,2CAA2C,GAAG,kCAAkC,EAC5F;wBAAAoG,QAAA,EACA4C;sBAAI,GAHInB,KAAK;wBAAA/D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAIV,CACP,CAAC,EACDS,kBAAkB,CAACJ,SAAS,CAACvC,MAAM,GAAG,CAAC,iBACtC1C,OAAA;wBAAMwE,SAAS,EAAE,oCACf7D,UAAU,GAAG,2CAA2C,GAAG,kCAAkC,EAC5F;wBAAAoG,QAAA,GAAC,GACD,EAAC1B,kBAAkB,CAACJ,SAAS,CAACvC,MAAM,GAAG,CAAC,EAAC,OAC5C;sBAAA;wBAAA+B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN5E,OAAA;gBAAKwE,SAAS,EAAC,MAAM;gBAAAuC,QAAA,gBACnB/G,OAAA;kBAAIwE,SAAS,EAAE,8BAA8B7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;kBAAAoG,QAAA,EAAC;gBAAsB;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvH5E,OAAA;kBAAKwE,SAAS,EAAC,YAAY;kBAAAuC,QAAA,EACxB1B,kBAAkB,CAACL,MAAM,CAAC8C,GAAG,CAAC,CAAC8B,KAAK,EAAEpB,KAAK,kBAC1CxI,OAAA;oBAAiBwE,SAAS,EAAE,2CAC1B7D,UAAU,GAAG,+CAA+C,GAAG,kCAAkC,EAChG;oBAAAoG,QAAA,eACD/G,OAAA;sBAAKwE,SAAS,EAAC,4BAA4B;sBAAAuC,QAAA,gBACzC/G,OAAA;wBAAKwE,SAAS,EAAE,kEACd7D,UAAU,GAAG,2BAA2B,GAAG,2BAA2B,EACrE;wBAAAoG,QAAA,eACD/G,OAAA;0BAAMwE,SAAS,EAAC,uBAAuB;0BAAAuC,QAAA,EAAEyB,KAAK,GAAG;wBAAC;0BAAA/D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC,eACN5E,OAAA;wBAAA+G,QAAA,gBACE/G,OAAA;0BAAIwE,SAAS,EAAE,oBAAoB7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;0BAAAoG,QAAA,EAC9E6C,KAAK,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;wBAAC;0BAAApF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,eACL5E,OAAA;0BAAGwE,SAAS,EAAE,WAAW7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;0BAAAoG,QAAA,EACvE6C,KAAK,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;wBAAC;0BAAApF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAjBE4D,KAAK;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAkBV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN5E,OAAA;gBAAKwE,SAAS,EAAE,yBAAyB7D,UAAU,GAAG,6BAA6B,GAAG,mBAAmB,EAAG;gBAAAoG,QAAA,gBAC1G/G,OAAA;kBAAIwE,SAAS,EAAE,8BAA8B7D,UAAU,GAAG,YAAY,GAAG,eAAe,EAAG;kBAAAoG,QAAA,EAAC;gBAA4B;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7H5E,OAAA;kBAAKwE,SAAS,EAAC,sDAAsD;kBAAAuC,QAAA,EAClE1B,kBAAkB,CAACJ,SAAS,CAAC6C,GAAG,CAAC,CAAC6B,IAAI,EAAEnB,KAAK,kBAC5CxI,OAAA;oBAAiBwE,SAAS,EAAE,0DAC1B7D,UAAU,GAAG,+CAA+C,GAAG,kCAAkC,EAChG;oBAAAoG,QAAA,eACD/G,OAAA;sBAAMwE,SAAS,EAAE,uBAAuB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAoG,QAAA,EAAE4C;oBAAI;sBAAAlF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC,GAH/F4D,KAAK;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,CACP,EAGA7D,eAAe,KAAK,MAAM,iBACzBf,OAAA;YAAKwE,SAAS,EAAC,KAAK;YAAAuC,QAAA,eAClB/G,OAAA;cAAKwE,SAAS,EAAC,WAAW;cAAAuC,QAAA,eACxB/G,OAAA;gBAAKwE,SAAS,EAAE,qBAAqB7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;gBAAAoG,QAAA,gBACpF/G,OAAA;kBAAKwE,SAAS,EAAC,MAAM;kBAAAuC,QAAA,GAClBhG,eAAe,KAAK,OAAO,iBAAIf,OAAA,CAACb,KAAK;oBAACqF,SAAS,EAAC;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC3E7D,eAAe,KAAK,MAAM,iBAAIf,OAAA,CAACxC,KAAK;oBAACgH,SAAS,EAAC;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC1E7D,eAAe,KAAK,UAAU,iBAAIf,OAAA,CAACxB,QAAQ;oBAACgG,SAAS,EAAC;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACjF7D,eAAe,KAAK,OAAO,iBAAIf,OAAA,CAACT,KAAK;oBAACiF,SAAS,EAAC;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC3E7D,eAAe,KAAK,OAAO,iBAAIf,OAAA,CAACR,IAAI;oBAACgF,SAAS,EAAC;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC1E7D,eAAe,KAAK,OAAO,iBAAIf,OAAA,CAACX,QAAQ;oBAACmF,SAAS,EAAC;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC9E7D,eAAe,KAAK,UAAU,iBAAIf,OAAA,CAACZ,aAAa;oBAACoF,SAAS,EAAC;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,eACN5E,OAAA;kBAAIwE,SAAS,EAAE,8BAA8B7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;kBAAAoG,QAAA,GAC3FhG,eAAe,KAAK,OAAO,IAAI,kBAAkB,EACjDA,eAAe,KAAK,MAAM,IAAI,oBAAoB,EAClDA,eAAe,KAAK,UAAU,IAAI,eAAe,EACjDA,eAAe,KAAK,OAAO,IAAI,eAAe,EAC9CA,eAAe,KAAK,OAAO,IAAI,iBAAiB,EAChDA,eAAe,KAAK,OAAO,IAAI,iBAAiB;gBAAA;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACL5E,OAAA;kBAAGwE,SAAS,EAAE,GAAG7D,UAAU,GAAG,eAAe,GAAG,eAAe,EAAG;kBAAAoG,QAAA,GAAC,cAAY,EAAChG,eAAe,EAAC,kEAA8D;gBAAA;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/J;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAr8DID,kBAAkB;AAAA6J,EAAA,GAAlB7J,kBAAkB;AAu8DxB,eAAeA,kBAAkB;AAAC,IAAA6J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}