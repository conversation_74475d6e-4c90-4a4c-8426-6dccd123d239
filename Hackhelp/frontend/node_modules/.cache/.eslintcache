[{"/Users/<USER>/Hackhelp/frontend/src/index.js": "1", "/Users/<USER>/Hackhelp/frontend/src/Pages/Signinup.js": "2", "/Users/<USER>/Hackhelp/frontend/src/Pages/Landing.js": "3", "/Users/<USER>/Hackhelp/frontend/src/Pages/LinkedIn.js": "4", "/Users/<USER>/Hackhelp/frontend/src/Pages/PowerPointer.js": "5", "/Users/<USER>/Hackhelp/frontend/src/Pages/Home.js": "6", "/Users/<USER>/Hackhelp/frontend/src/Pages/Fork.js": "7", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Signinup/Stepper.js": "8", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Signinup/Sample.js": "9", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Home/FinalWorkSpace.js": "10", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Home/Sample2.js": "11", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Home/WorkSpace.js": "12", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/Features.jsx": "13", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/Block.jsx": "14", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/Header.jsx": "15", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/TestimonialsSection.jsx": "16", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/crm.jsx": "17", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/Footer.jsx": "18", "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/Herosection.jsx": "19", "/Users/<USER>/Hackhelp/frontend/src/Pages/TeamChat.js": "20", "/Users/<USER>/Hackhelp/frontend/src/Pages/Tasks.js": "21"}, {"size": 1165, "mtime": 1748287721969, "results": "22", "hashOfConfig": "23"}, {"size": 190, "mtime": 1748287721958, "results": "24", "hashOfConfig": "23"}, {"size": 2200, "mtime": 1748287721958, "results": "25", "hashOfConfig": "23"}, {"size": 28534, "mtime": 1748455355087, "results": "26", "hashOfConfig": "23"}, {"size": 28451, "mtime": 1748455355087, "results": "27", "hashOfConfig": "23"}, {"size": 427, "mtime": 1748455355086, "results": "28", "hashOfConfig": "23"}, {"size": 7342, "mtime": 1748287721958, "results": "29", "hashOfConfig": "23"}, {"size": 25148, "mtime": 1748287721957, "results": "30", "hashOfConfig": "23"}, {"size": 6969, "mtime": 1748287721957, "results": "31", "hashOfConfig": "23"}, {"size": 31813, "mtime": 1748287721956, "results": "32", "hashOfConfig": "23"}, {"size": 101932, "mtime": 1748455355086, "results": "33", "hashOfConfig": "23"}, {"size": 25297, "mtime": 1748287721956, "results": "34", "hashOfConfig": "23"}, {"size": 9456, "mtime": 1748287721957, "results": "35", "hashOfConfig": "23"}, {"size": 1780, "mtime": 1748287721957, "results": "36", "hashOfConfig": "23"}, {"size": 1716, "mtime": 1748287721957, "results": "37", "hashOfConfig": "23"}, {"size": 11409, "mtime": 1748287721957, "results": "38", "hashOfConfig": "23"}, {"size": 7394, "mtime": 1748287721957, "results": "39", "hashOfConfig": "23"}, {"size": 6768, "mtime": 1748287721957, "results": "40", "hashOfConfig": "23"}, {"size": 1281, "mtime": 1748287721957, "results": "41", "hashOfConfig": "23"}, {"size": 11910, "mtime": 1748287721958, "results": "42", "hashOfConfig": "23"}, {"size": 22704, "mtime": 1748287721958, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "so4h8n", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Hackhelp/frontend/src/index.js", [], [], "/Users/<USER>/Hackhelp/frontend/src/Pages/Signinup.js", [], [], "/Users/<USER>/Hackhelp/frontend/src/Pages/Landing.js", ["107", "108"], [], "/Users/<USER>/Hackhelp/frontend/src/Pages/LinkedIn.js", ["109", "110"], [], "/Users/<USER>/Hackhelp/frontend/src/Pages/PowerPointer.js", [], [], "/Users/<USER>/Hackhelp/frontend/src/Pages/Home.js", ["111", "112"], [], "/Users/<USER>/Hackhelp/frontend/src/Pages/Fork.js", [], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Signinup/Stepper.js", [], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Signinup/Sample.js", ["113", "114"], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Home/FinalWorkSpace.js", ["115", "116"], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Home/Sample2.js", ["117", "118", "119", "120", "121", "122", "123", "124", "125", "126"], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Home/WorkSpace.js", ["127"], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/Features.jsx", ["128"], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/Block.jsx", [], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/Header.jsx", [], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/TestimonialsSection.jsx", [], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/crm.jsx", [], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/Footer.jsx", [], [], "/Users/<USER>/Hackhelp/frontend/src/Page_Components/Landing/Herosection.jsx", [], [], "/Users/<USER>/Hackhelp/frontend/src/Pages/TeamChat.js", ["129"], [], "/Users/<USER>/Hackhelp/frontend/src/Pages/Tasks.js", [], [], {"ruleId": "130", "severity": 1, "message": "131", "line": 12, "column": 10, "nodeType": "132", "messageId": "133", "endLine": 12, "endColumn": 21}, {"ruleId": "134", "severity": 1, "message": "135", "line": 49, "column": 43, "nodeType": "132", "endLine": 49, "endColumn": 50}, {"ruleId": "130", "severity": 1, "message": "136", "line": 43, "column": 18, "nodeType": "132", "messageId": "133", "endLine": 43, "endColumn": 27}, {"ruleId": "130", "severity": 1, "message": "137", "line": 44, "column": 27, "nodeType": "132", "messageId": "133", "endLine": 44, "endColumn": 45}, {"ruleId": "130", "severity": 1, "message": "138", "line": 2, "column": 8, "nodeType": "132", "messageId": "133", "endLine": 2, "endColumn": 20}, {"ruleId": "130", "severity": 1, "message": "139", "line": 4, "column": 8, "nodeType": "132", "messageId": "133", "endLine": 4, "endColumn": 27}, {"ruleId": "130", "severity": 1, "message": "140", "line": 9, "column": 9, "nodeType": "132", "messageId": "133", "endLine": 9, "endColumn": 19}, {"ruleId": "141", "severity": 1, "message": "142", "line": 105, "column": 23, "nodeType": "143", "endLine": 105, "endColumn": 86}, {"ruleId": "130", "severity": 1, "message": "144", "line": 15, "column": 25, "nodeType": "132", "messageId": "133", "endLine": 15, "endColumn": 41}, {"ruleId": "141", "severity": 1, "message": "142", "line": 652, "column": 11, "nodeType": "143", "endLine": 652, "endColumn": 82}, {"ruleId": "130", "severity": 1, "message": "145", "line": 2, "column": 49, "nodeType": "132", "messageId": "133", "endLine": 2, "endColumn": 55}, {"ruleId": "130", "severity": 1, "message": "146", "line": 2, "column": 64, "nodeType": "132", "messageId": "133", "endLine": 2, "endColumn": 69}, {"ruleId": "130", "severity": 1, "message": "147", "line": 2, "column": 71, "nodeType": "132", "messageId": "133", "endLine": 2, "endColumn": 84}, {"ruleId": "130", "severity": 1, "message": "148", "line": 2, "column": 86, "nodeType": "132", "messageId": "133", "endLine": 2, "endColumn": 90}, {"ruleId": "130", "severity": 1, "message": "149", "line": 2, "column": 99, "nodeType": "132", "messageId": "133", "endLine": 2, "endColumn": 108}, {"ruleId": "130", "severity": 1, "message": "150", "line": 2, "column": 118, "nodeType": "132", "messageId": "133", "endLine": 2, "endColumn": 121}, {"ruleId": "130", "severity": 1, "message": "151", "line": 2, "column": 123, "nodeType": "132", "messageId": "133", "endLine": 2, "endColumn": 128}, {"ruleId": "130", "severity": 1, "message": "152", "line": 2, "column": 130, "nodeType": "132", "messageId": "133", "endLine": 2, "endColumn": 136}, {"ruleId": "130", "severity": 1, "message": "153", "line": 2, "column": 208, "nodeType": "132", "messageId": "133", "endLine": 2, "endColumn": 212}, {"ruleId": "130", "severity": 1, "message": "154", "line": 2, "column": 302, "nodeType": "132", "messageId": "133", "endLine": 2, "endColumn": 311}, {"ruleId": "130", "severity": 1, "message": "144", "line": 10, "column": 25, "nodeType": "132", "messageId": "133", "endLine": 10, "endColumn": 41}, {"ruleId": "134", "severity": 1, "message": "155", "line": 12, "column": 11, "nodeType": "156", "endLine": 12, "endColumn": 87}, {"ruleId": "130", "severity": 1, "message": "157", "line": 25, "column": 9, "nodeType": "132", "messageId": "133", "endLine": 25, "endColumn": 20}, "no-unused-vars", "'showNavTabs' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "The ref value 'testimonialRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'testimonialRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "'setTaskId' is assigned a value but never used.", "'setPollingInterval' is assigned a value but never used.", "'WorkspaceApp' is defined but never used.", "'RedesignedWorkspace' is defined but never used.", "'toggleForm' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'setNotifications' is assigned a value but never used.", "'Shield' is defined but never used.", "'Heart' is defined but never used.", "'GraduationCap' is defined but never used.", "'Leaf' is defined but never used.", "'Lightbulb' is defined but never used.", "'Zap' is defined but never used.", "'Clock' is defined but never used.", "'Target' is defined but never used.", "'Star' is defined but never used.", "'BarChart3' is defined but never used.", "The 'tabs' array makes the dependencies of useEffect Hook (at line 135) change on every render. To fix this, wrap the initialization of 'tabs' in its own useMemo() Hook.", "VariableDeclarator", "'getInitials' is assigned a value but never used."]