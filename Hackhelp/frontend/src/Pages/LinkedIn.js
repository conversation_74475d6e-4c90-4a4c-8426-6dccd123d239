import React, { useState, useEffect } from 'react';

function LinkedInPostGenerator({ isDarkMode = false }) {
  const [currentTab, setCurrentTab] = useState('basic');
  const [formData, setFormData] = useState({
    // Basic Info
    name: "",
    role: "",
    college: "",
    year_of_study: "",
    
    // Team & Event
    team_name: "",
    team_members: "",
    hackathon_name: "",
    hackathon_theme: "",
    hackathon_mode: "",
    location: "",
    duration: "",
    
    // Project Details
    project_name: "",
    problem_solved: "",
    tech_stack: "",
    features: "",
    innovation: "",
    github_link: "",
    
    // Results & Reflection
    result: "",
    learnings: "",
    mentor_or_judges: "",
    gratitude_note: "",
    fun_moments: ""
  });

  const [rawMarkdown, setRawMarkdown] = useState('');
  const [isOutputVisible, setIsOutputVisible] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [taskId, setTaskId] = useState(null);
  const [pollingInterval, setPollingInterval] = useState(null);

  const boldMap = {};
  'abcdefghijklmnopqrstuvwxyz'.split('').forEach((c, i) => {
    boldMap[c] = String.fromCodePoint(0x1d41a + i);
    boldMap[c.toUpperCase()] = String.fromCodePoint(0x1d400 + i);
  });
  '0123456789'.split('').forEach((c, i) => {
    boldMap[c] = String.fromCodePoint(0x1d7ce + i);
  });

  const toBold = (str) => str.split('').map(c => boldMap[c] || c).join('');

  useEffect(() => {
    if (isCopied) {
      const timer = setTimeout(() => setIsCopied(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [isCopied]);

  // Clean up polling on component unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) clearInterval(pollingInterval);
    };
  }, [pollingInterval]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: '👤' },
    { id: 'event', label: 'Event & Team', icon: '🎯' },
    { id: 'project', label: 'Project', icon: '💡' },
    { id: 'results', label: 'Results', icon: '🏆' }
  ];

  const getFieldsForTab = (tabId) => {
    switch (tabId) {
      case 'basic':
        return [
          { key: 'name', label: 'Full Name', placeholder: 'Alex Johnson', type: 'input' },
          { key: 'role', label: 'Role/Title', placeholder: 'Software Engineer', type: 'input' },
          { key: 'college', label: 'College/University', placeholder: 'Stanford University', type: 'input' },
          { key: 'year_of_study', label: 'Year of Study', placeholder: '3', type: 'input' }
        ];
      case 'event':
        return [
          { key: 'hackathon_name', label: 'Hackathon Name', placeholder: 'TechCrunch Disrupt 2025', type: 'input' },
          { key: 'hackathon_theme', label: 'Theme/Track', placeholder: 'Fintech & Blockchain', type: 'input' },
          { key: 'hackathon_mode', label: 'Mode', placeholder: 'Hybrid', type: 'input' },
          { key: 'location', label: 'Location', placeholder: 'San Francisco, CA', type: 'input' },
          { key: 'duration', label: 'Duration', placeholder: '36 hours', type: 'input' },
          { key: 'team_name', label: 'Team Name', placeholder: 'Code Innovators', type: 'input' },
          { key: 'team_members', label: 'Team Members', placeholder: 'Sarah Chen, Mike Rodriguez, Emma Davis', type: 'input' }
        ];
      case 'project':
        return [
          { key: 'project_name', label: 'Project Name', placeholder: 'CryptoWallet Pro', type: 'input' },
          { key: 'problem_solved', label: 'Problem Solved', placeholder: 'Simplifying cryptocurrency transactions for everyday users with enhanced security', type: 'textarea' },
          { key: 'tech_stack', label: 'Tech Stack', placeholder: 'Next.js, Python, PostgreSQL, Web3.js, AWS', type: 'input' },
          { key: 'features', label: 'Key Features', placeholder: 'Multi-currency support, AI fraud detection, simplified UI, hardware wallet integration', type: 'textarea' },
          { key: 'innovation', label: 'Innovation/USP', placeholder: 'First-of-its-kind AI-powered transaction security with one-click DeFi integration', type: 'textarea' },
          { key: 'github_link', label: 'GitHub/Demo Link', placeholder: 'https://github.com/alexj/cryptowallet-pro', type: 'input' }
        ];
      case 'results':
        return [
          { key: 'result', label: 'Result/Achievement', placeholder: '2nd Place 🥈', type: 'input' },
          { key: 'learnings', label: 'Key Learnings', placeholder: 'Blockchain development, team collaboration, user experience design, pitch presentation', type: 'textarea' },
          { key: 'mentor_or_judges', label: 'Mentors/Judges', placeholder: 'Lisa Park from Coinbase Ventures', type: 'input' },
          { key: 'gratitude_note', label: 'Gratitude Message', placeholder: 'Huge thanks to the organizers, mentors, and our amazing team for this incredible journey!', type: 'textarea' },
          { key: 'fun_moments', label: 'Fun Moments', placeholder: 'Pizza-fueled coding marathon at 3 AM with spontaneous karaoke breaks! 🍕🎤', type: 'textarea' }
        ];
      default:
        return [];
    }
  };

  const generatePost = () => {
    setIsOutputVisible(false);
    setIsErrorVisible(false);
    setIsLoading(true);

    const payload = {
      ...formData,
      team_members: formData.team_members.split(',').map(m => m.trim()),
      tech_stack: formData.tech_stack.split(',').map(t => t.trim()),
      features: formData.features.split(',').map(f => f.trim())
    };

    fetch("http://localhost:8000/api/startup/generate-linkedin-post/", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload)
    })
      .then(response => {
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        return response.json();
      })
      .then(data => {
        if (data.linkedin_post) {
          const postContent = data.linkedin_post.replace(/\\#/g, '#');
          
          const formatted = postContent
            .replace(/^#+ (.*)$/gm, (_, header) => toBold(header))
            .replace(/\*\*(.*?)\*\*/g, (_, text) => toBold(text));
          
          setRawMarkdown(formatted);
          setIsOutputVisible(true);
        } else {
          throw new Error("No post content received");
        }
      })
      .catch(error => {
        setErrorMessage("❌ Error: " + error.message);
        setIsErrorVisible(true);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(rawMarkdown).then(() => setIsCopied(true));
  };

  const isFormValid = () => {
    return formData.name && formData.hackathon_name && formData.project_name;
  };

  return (
    <div className={`min-h-screen p-4 transition-colors duration-200 ${
      isDarkMode ? 'bg-[#111827]' : 'bg-gray-50'
    }`}>
      {/* Custom CSS for theme colors */}
      <style jsx>{`
        .theme-primary { color: #621639; }
        .theme-primary-light { color: #831843; }
        .theme-accent { background-color: #fce7f3; }
        .theme-accent-text { color: #831843; }
        .theme-border { border-color: #621639; }
        .theme-focus:focus { 
          --tw-ring-color: #621639; 
          border-color: #621639; 
        }
        .theme-button-light {
          background-color: #fce7f3;
          color: #831843;
        }
        .theme-button-light:hover {
          background-color: #621639;
          color: white;
        }
        .theme-button-dark {
          background-color: #4c1d3b;
          color: #fce7f3;
        }
        .theme-button-dark:hover {
          background-color: #621639;
        }
        .theme-tab-active-light {
          background-color: white;
          color: #831843;
          border-bottom: 2px solid #621639;
        }
        .theme-tab-active-dark {
          background-color: #475569;
          color: white;
          border-bottom: 2px solid #621639;
        }
        .theme-tab-inactive-light {
          color: #831843;
        }
        .theme-tab-inactive-light:hover {
          background-color: #f9fafb;
        }
        .theme-tab-inactive-dark {
          color: #d1d5db;
        }
        .theme-tab-inactive-dark:hover {
          background-color: #4b5563;
        }
      `}</style>

      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className={`text-xl font-bold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            ✨ LinkedIn Post Generator
          </h1>
          <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Create professional hackathon celebration posts in minutes
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-6">
          {/* Form Section */}
          <div className="space-y-4">
            {/* Tab Navigation */}
            <div className={`rounded-lg shadow-sm border overflow-hidden ${
              isDarkMode 
                ? 'bg-[#1f2937] border-slate-600' 
                : 'bg-white border-gray-200'
            }`}>
              <div className={`flex border-b ${
                isDarkMode 
                  ? 'bg-slate-600 border-slate-500' 
                  : 'theme-accent border-gray-200'
              }`}>
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setCurrentTab(tab.id)}
                    className={`flex-1 px-3 py-2 text-xs font-medium transition-all duration-200 focus:outline-none ${
                      currentTab === tab.id
                        ? isDarkMode ? 'theme-tab-active-dark' : 'theme-tab-active-light'
                        : isDarkMode ? 'theme-tab-inactive-dark' : 'theme-tab-inactive-light'
                    }`}
                  >
                    <span className="mr-1">{tab.icon}</span>
                    {tab.label}
                  </button>
                ))}
              </div>

              {/* Form Content */}
              <div className="p-4">
                <div className="space-y-3">
                  {getFieldsForTab(currentTab).map((field) => (
                    <div key={field.key}>
                      <label className={`block text-xs font-medium mb-1 ${
                        isDarkMode ? 'text-gray-200' : 'theme-accent-text'
                      }`}>
                        {field.label}
                      </label>
                      {field.type === 'textarea' ? (
                        <textarea
                          name={field.key}
                          value={formData[field.key]}
                          onChange={handleChange}
                          placeholder={field.placeholder}
                          rows={2}
                          className={`w-full px-3 py-2 text-sm rounded-md transition-colors resize-none theme-focus ${
                            isDarkMode 
                              ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' 
                              : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'
                          }`}
                        />
                      ) : (
                        <input
                          type="text"
                          name={field.key}
                          value={formData[field.key]}
                          onChange={handleChange}
                          placeholder={field.placeholder}
                          className={`w-full px-3 py-2 text-sm rounded-md transition-colors theme-focus ${
                            isDarkMode 
                              ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' 
                              : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'
                          }`}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Action Button */}
            <button
              onClick={generatePost}
              disabled={isLoading || !isFormValid()}
              className={`w-full py-3 px-4 text-sm rounded-lg font-semibold transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 focus:outline-none ${
                isLoading || !isFormValid()
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed transform-none'
                  : isDarkMode
                    ? 'theme-button-dark'
                    : 'theme-button-light'
              }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {taskId ? 'Processing...' : 'Generating...'}
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <span className="mr-1">🚀</span>
                  Generate Post
                </div>
              )}
            </button>
          </div>

          {/* Error Message */}
          {isErrorVisible && (
            <div className={`mb-4 rounded-md border-l-4 p-3 ${
              isDarkMode 
                ? 'bg-red-900 border-red-500' 
                : 'bg-red-50 border-red-500'
            }`}>
              <div className="flex items-center">
                <svg className={`h-4 w-4 mr-2 ${
                  isDarkMode ? 'text-red-400' : 'text-red-400'
                }`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <p className={`text-xs ${
                  isDarkMode ? 'text-red-300' : 'text-red-700'
                }`}>
                  {errorMessage}
                </p>
              </div>
            </div>
          )}

          {/* Output Section */}
          <div className="space-y-4">
            {/* Preview Card */}
            <div className={`rounded-lg shadow-sm border overflow-hidden ${
              isDarkMode 
                ? 'bg-slate-700 border-slate-600' 
                : 'bg-white border-gray-200'
            }`}>
              <div className={`px-4 py-3 border-b ${
                isDarkMode 
                  ? 'bg-slate-600 border-slate-500' 
                  : 'bg-gradient-to-r from-pink-50 to-purple-50 border-gray-200'
              }`}>
                <h3 className={`text-sm font-semibold flex items-center ${
                  isDarkMode ? 'text-white' : 'text-gray-800'
                }`}>
                  <span className="mr-1">📝</span>
                  Generated Post
                </h3>
              </div>

              {isOutputVisible ? (
                <div className="p-4">
                  <div className={`rounded-md p-3 mb-3 border ${
                    isDarkMode 
                      ? 'bg-slate-600 border-slate-500' 
                      : 'theme-accent border-gray-200'
                  }`}>
                    <div className={`whitespace-pre-wrap text-xs leading-relaxed font-mono ${
                      isDarkMode ? 'text-gray-200' : 'theme-accent-text'
                    }`}>
                      {rawMarkdown}
                    </div>
                  </div>
                  
                  <button
                    onClick={copyToClipboard}
                    className={`w-full py-2 px-3 text-sm rounded-md font-medium transition-all duration-200 border focus:outline-none ${
                      isCopied
                        ? 'bg-green-600 text-white border-green-500'
                        : isDarkMode
                          ? 'theme-button-dark border-slate-500'
                          : 'theme-button-light theme-border'
                    }`}
                  >
                    {isCopied ? (
                      <div className="flex items-center justify-center">
                        <span className="mr-1">✅</span>
                        Copied!
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <span className="mr-1">📋</span>
                        Copy to Clipboard
                      </div>
                    )}
                  </button>
                </div>
              ) : (
                <div className="p-8 text-center text-gray-400">
                  <div className="text-2xl mb-2">📄</div>
                  <p className="text-sm">Your generated post will appear here</p>
                  <p className="text-xs mt-1">Fill in the form and click generate</p>
                </div>
              )}
            </div>

            {/* Tips Card */}
            <div className={`rounded-lg p-4 border ${
              isDarkMode 
                ? 'bg-gradient-to-br from-slate-700 to-slate-600 border-slate-500' 
                : 'bg-gradient-to-br from-pink-50 to-purple-50 theme-border'
            }`}>
              <h4 className={`text-sm font-semibold mb-2 flex items-center ${
                isDarkMode ? 'text-white' : 'text-gray-800'
              }`}>
                <span className="mr-1">💡</span>
                Pro Tips
              </h4>
              <ul className={`space-y-1 text-xs ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>
                <li className="flex items-start">
                  <span className="mr-1 theme-primary">•</span>
                  Keep your project description concise but impactful
                </li>
                <li className="flex items-start">
                  <span className="mr-1 theme-primary">•</span>
                  Mention specific technologies and innovations
                </li>
                <li className="flex items-start">
                  <span className="mr-1 theme-primary">•</span>
                  Include gratitude to mentors and teammates
                </li>
                <li className="flex items-start">
                  <span className="mr-1 theme-primary">•</span>
                  Add relevant hashtags for better reach
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default LinkedInPostGenerator;


// import React, { useState, useEffect } from 'react';
// import { marked } from 'marked';

// function LinkedInPostGenerator() {
//   const [formData, setFormData] = useState({
//     name: "Mohnish Mythreya",
//     role: "Full Stack Developer",
//     college: "VIT Vellore",
//     year_of_study: 2,
//     team_name: "HackHelp Squad",
//     team_members: "Aryan, Sneha, Riya",
//     hackathon_name: "InnoVerse 2025",
//     hackathon_theme: "Healthcare Tech + AI",
//     hackathon_mode: "Offline",
//     location: "IIT Bombay",
//     duration: "48 hours",
//     project_name: "HackHelp",
//     problem_solved: "Lack of centralized assistance and support for hackathon participants",
//     tech_stack: "React, FastAPI, Gemini Pro, Docker, Redis, TailwindCSS",
//     features: "LinkedIn post generator, mentor matching, API request tester, team resource hub",
//     innovation: "Microservices-based AI toolchain for end-to-end hackathon support",
//     github_link: "https://github.com/hackhelp-2025",
//     result: "Winner 🏆",
//     learnings: "Microservices, containerization, prompt engineering, real-time communication",
//     mentor_or_judges: "Sundar Rajan from Microsoft AI",
//     gratitude_note: "Heartfelt thanks to mentors, volunteers, and teammates for making it magical!",
//     fun_moments: "Midnight dance break with teammates while debugging bugs 🤣🕺"
//   });

//   const boldMap = {};
//   'abcdefghijklmnopqrstuvwxyz'.split('').forEach((c, i) => {
//     boldMap[c] = String.fromCodePoint(0x1d41a + i);
//     boldMap[c.toUpperCase()] = String.fromCodePoint(0x1d400 + i);
//   });
//   '0123456789'.split('').forEach((c, i) => {
//     boldMap[c] = String.fromCodePoint(0x1d7ce + i);
//   });

//   const toBold = (str) => str.split('').map(c => boldMap[c] || c).join('');

//   const [rawMarkdown, setRawMarkdown] = useState('');
//   const [isOutputVisible, setIsOutputVisible] = useState(false);
//   const [isErrorVisible, setIsErrorVisible] = useState(false);
//   const [errorMessage, setErrorMessage] = useState('');
//   const [isLoading, setIsLoading] = useState(false);
//   const [isCopied, setIsCopied] = useState(false);
//   const [taskId, setTaskId] = useState(null);
//   const [pollingInterval, setPollingInterval] = useState(null);

//   useEffect(() => {
//     if (isCopied) {
//       const timer = setTimeout(() => setIsCopied(false), 2000);
//       return () => clearTimeout(timer);
//     }
//   }, [isCopied]);

//   // Clean up polling on component unmount
//   useEffect(() => {
//     return () => {
//       if (pollingInterval) clearInterval(pollingInterval);
//     };
//   }, [pollingInterval]);

//   const handleChange = (e) => {
//     const { name, value } = e.target;
//     setFormData(prev => ({ ...prev, [name]: value }));
//   };

//   const generatePost = () => {
//     setIsOutputVisible(false);
//     setIsErrorVisible(false);
//     setIsLoading(true);

//     const payload = {
//       ...formData,
//       team_members: formData.team_members.split(',').map(m => m.trim()),
//       tech_stack: formData.tech_stack.split(',').map(t => t.trim()),
//       features: formData.features.split(',').map(f => f.trim())
//     };

//     fetch("http://localhost:8000/api/startup/generate-linkedin-post/", {
//       method: "POST",
//       headers: { "Content-Type": "application/json" },
//       body: JSON.stringify(payload)
//     })
//       .then(response => {
//         if (!response.ok) throw new Error(`HTTP ${response.status}`);
//         return response.json();
//       })
//       .then(data => {
//         if (data.linkedin_post) {
//           const postContent = data.linkedin_post.replace(/\\#/g, '#');
          
//           const formatted = postContent
//             .replace(/^#+ (.*)$/gm, (_, header) => toBold(header))
//             .replace(/\*\*(.*?)\*\*/g, (_, text) => toBold(text));
          
//           setRawMarkdown(formatted);
//           setIsOutputVisible(true);
//         } else {
//           throw new Error("No post content received");
//         }
//       })
//       .catch(error => {
//         setErrorMessage("❌ Error: " + error.message);
//         setIsErrorVisible(true);
//       })
//       .finally(() => {
//         setIsLoading(false);
//       });
//   };

//   const copyToClipboard = () => {
//     navigator.clipboard.writeText(rawMarkdown).then(() => setIsCopied(true));
//   };

//   return (
//     <div className="min-h-screen bg-gradient-to-b from-white to-gray-100 py-12 px-4 sm:px-6 lg:px-8">
//       <div className="max-w-4xl mx-auto">
//         <div className="text-center mb-10">
//           <h1 className="text-4xl font-bold text-black mb-3">🚀 LinkedIn Post Generator</h1>
//           <p className="text-gray-600">Fill out the form below and click generate.</p>
//         </div>

//         <div className="bg-white shadow-lg rounded-xl p-6 mb-8">
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
//             {Object.entries(formData).map(([key, val]) => (
//               <div key={key} className="form-group">
//                 <label htmlFor={key} className="block text-sm font-medium text-gray-700 mb-1 capitalize">
//                   {key.replace(/_/g, ' ')}:
//                 </label>
//                 <textarea
//                   id={key}
//                   name={key}
//                   value={val}
//                   onChange={handleChange}
//                   rows={key === 'gratitude_note' || key === 'fun_moments' || key === 'problem_solved' ? 2 : 1}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-sm"
//                 />
//               </div>
//             ))}
//           </div>
//         </div>

//         <div className="flex flex-col sm:flex-row justify-center gap-4 mb-8">
//           <button
//             className={`px-6 py-3 rounded-md text-white font-medium transition duration-200 ${
//               isLoading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
//             } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center`}
//             onClick={generatePost}
//             disabled={isLoading}
//           >
//             {isLoading ? (
//               <>
//                 <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
//                   <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
//                   <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
//                 </svg>
//                 {taskId ? 'Processing...' : 'Generating...'}
//               </>
//             ) : (
//               'Generate Post'
//             )}
//           </button>

//           {isOutputVisible && (
//             <button
//               className={`px-6 py-3 rounded-md font-medium transition duration-200 ${
//                 isCopied ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
//               } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 flex items-center justify-center`}
//               onClick={copyToClipboard}
//             >
//               {isCopied ? '✓ Copied!' : 'Copy to Clipboard'}
//             </button>
//           )}
//         </div>

//         {isErrorVisible && (
//           <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-8 rounded">
//             <div className="flex">
//               <div className="flex-shrink-0">
//                 <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
//                   <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
//                 </svg>
//               </div>
//               <div className="ml-3">
//                 <p className="text-sm text-red-700">
//                   {errorMessage}
//                 </p>
//               </div>
//             </div>
//           </div>
//         )}

//         {isOutputVisible && (
//           <div className="bg-f3efec rounded-xl shadow-md p-6 mb-8 prose max-w-none">
//             <h3 className="text-lg font-medium mb-4 text-gray-800">Generated LinkedIn Post</h3>
//             <div 
//               className="whitespace-pre-wrap text-gray-800 border border-gray-200 rounded-md p-5 bg-white"
//               dangerouslySetInnerHTML={{ __html: marked.parse(rawMarkdown) }}
//             />
//           </div>
//         )}
//       </div>
//     </div>
//   );
// }

// export default LinkedInPostGenerator;