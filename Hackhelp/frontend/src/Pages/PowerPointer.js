import React, { useState } from 'react';

function PowerPointerGenerator({ isDarkMode = false }) {
  const [activeTab, setActiveTab] = useState('basic');
  const [formData, setFormData] = useState({
    project_name: "",
    problem_statement: "",
    solution: "",
    target_audience: "",
    key_features: "",
    tech_stack: "",
    business_impact: "",
    design_template: "1"
  });

  const [isOutputVisible, setIsOutputVisible] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState('');

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: '👤' },
    { id: 'project', label: 'Project', icon: '💡' },
    { id: 'technical', label: 'Technical', icon: '⚙️' },
    { id: 'design', label: 'Design', icon: '🎨' }
  ];

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const generatePresentation = () => {
    setIsOutputVisible(false);
    setIsErrorVisible(false);
    setIsLoading(true);
    
    const payload = {
      ...formData,
      key_features: formData.key_features.split(',').map(f => f.trim()),
      tech_stack: formData.tech_stack.split(',').map(t => t.trim())
    };

    fetch("http://localhost:8000/api/startup/generate-powerpoint/", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload)
    })
      .then(response => {
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        return response.json();
      })
      .then(data => {
        if (data.url) {
          const fullUrl = `http://localhost:8000/${data.url}`;
          setDownloadUrl(fullUrl);
          setIsOutputVisible(true);
        } else {
          throw new Error("No URL received in response");
        }
      })
      .catch(error => {
        setErrorMessage("❌ Error: " + error.message);
        setIsErrorVisible(true);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const getFieldsForTab = (tabId) => {
    switch (tabId) {
      case 'basic':
        return [
          { key: 'project_name', label: 'Project Name', type: 'input', placeholder: 'EcoTracker Pro' },
          { key: 'target_audience', label: 'Target Audience', type: 'input', placeholder: 'Environmental enthusiasts, researchers, and policy makers' }
        ];
      case 'project':
        return [
          { key: 'problem_statement', label: 'Problem Statement', type: 'textarea', placeholder: 'Lack of accessible tools for individuals to track and reduce their carbon footprint in daily activities' },
          { key: 'solution', label: 'Solution', type: 'textarea', placeholder: 'AI-powered mobile app that automatically tracks carbon emissions and provides personalized reduction strategies' },
          { key: 'key_features', label: 'Key Features', type: 'textarea', placeholder: 'Real-time emission tracking, AI recommendations, community challenges, progress analytics, carbon offset marketplace' },
          { key: 'business_impact', label: 'Business Impact', type: 'textarea', placeholder: 'Projected 30% reduction in user carbon footprint within 6 months, potential to impact 1M+ users globally' }
        ];
      case 'technical':
        return [
          { key: 'tech_stack', label: 'Tech Stack', type: 'textarea', placeholder: 'React Native, Node.js, TensorFlow, MongoDB, AWS, Google Maps API, Stripe' }
        ];
      case 'design':
        return [
          { key: 'design_template', label: 'Design Template', type: 'select', options: [
            { value: "1", label: "Professional (Default)" },
            { value: "2", label: "Creative" },
            { value: "3", label: "Minimal" },
            { value: "4", label: "Academic" },
            { value: "5", label: "Business" },
            { value: "6", label: "Technical" },
            { value: "7", label: "Modern" },
            { value: "8", label: "Corporate" },
            { value: "9", label: "Elegant" }
          ]}
        ];
      default:
        return [];
    }
  };

  const isFormValid = () => {
    return formData.project_name && formData.problem_statement && formData.solution;
  };

  return (
    <div className={`min-h-screen p-4 transition-colors duration-200 ${
      isDarkMode ? 'bg-[#111827]' : 'bg-gray-50'
    }`}>
      {/* Custom CSS for theme colors */}
      <style jsx>{`
        .theme-primary { color: #621639; }
        .theme-primary-light { color: #831843; }
        .theme-accent { background-color: #fce7f3; }
        .theme-accent-text { color: #831843; }
        .theme-border { border-color: #621639; }
        .theme-focus:focus { 
          --tw-ring-color: #621639; 
          border-color: #621639; 
        }
        .theme-button-light {
          background-color: #fce7f3;
          color: #831843;
        }
        .theme-button-light:hover {
          background-color: #621639;
          color: white;
        }
        .theme-button-dark {
          background-color: #4c1d3b;
          color: #fce7f3;
        }
        .theme-button-dark:hover {
          background-color: #621639;
        }
        .theme-tab-active-light {
          background-color: white;
          color: #831843;
          border-bottom: 2px solid #621639;
        }
        .theme-tab-active-dark {
          background-color: #475569;
          color: white;
          border-bottom: 2px solid #621639;
        }
        .theme-tab-inactive-light {
          color: #831843;
        }
        .theme-tab-inactive-light:hover {
          background-color: #f9fafb;
        }
        .theme-tab-inactive-dark {
          color: #d1d5db;
        }
        .theme-tab-inactive-dark:hover {
          background-color: #4b5563;
        }
      `}</style>

      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className={`text-xl font-bold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            ✨ PowerPoint Generator
          </h1>
          <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Create professional hackathon presentations in minutes
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-6">
          {/* Form Section */}
          <div className="space-y-4">
            {/* Tab Navigation */}
            <div className={`rounded-lg shadow-sm border overflow-hidden ${
              isDarkMode 
                ? 'bg-[#1f2937] border-slate-600' 
                : 'bg-white border-gray-200'
            }`}>
              <div className={`flex border-b ${
                isDarkMode 
                  ? 'bg-slate-600 border-slate-500' 
                  : 'theme-accent border-gray-200'
              }`}>
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 px-3 py-2 text-xs font-medium transition-all duration-200 focus:outline-none ${
                      activeTab === tab.id
                        ? isDarkMode ? 'theme-tab-active-dark' : 'theme-tab-active-light'
                        : isDarkMode ? 'theme-tab-inactive-dark' : 'theme-tab-inactive-light'
                    }`}
                  >
                    <span className="mr-1">{tab.icon}</span>
                    {tab.label}
                  </button>
                ))}
              </div>

              {/* Form Content */}
              <div className="p-4">
                <div className="space-y-3">
                  {getFieldsForTab(activeTab).map((field) => (
                    <div key={field.key}>
                      <label className={`block text-xs font-medium mb-1 ${
                        isDarkMode ? 'text-gray-200' : 'theme-accent-text'
                      }`}>
                        {field.label}
                      </label>
                      {field.type === 'textarea' ? (
                        <textarea
                          name={field.key}
                          value={formData[field.key]}
                          onChange={handleChange}
                          placeholder={field.placeholder}
                          rows={2}
                          className={`w-full px-3 py-2 text-sm rounded-md transition-colors resize-none theme-focus ${
                            isDarkMode 
                              ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' 
                              : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'
                          }`}
                        />
                      ) : field.type === 'select' ? (
                        <select
                          name={field.key}
                          value={formData[field.key]}
                          onChange={handleChange}
                          className={`w-full px-3 py-2 text-sm rounded-md transition-colors theme-focus ${
                            isDarkMode 
                              ? 'bg-slate-900 border-slate-500 text-white' 
                              : 'bg-[#f9fafb] border-gray-300 text-gray-900'
                          }`}
                        >
                          {field.options?.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      ) : (
                        <input
                          type="text"
                          name={field.key}
                          value={formData[field.key]}
                          onChange={handleChange}
                          placeholder={field.placeholder}
                          className={`w-full px-3 py-2 text-sm rounded-md transition-colors theme-focus ${
                            isDarkMode 
                              ? 'bg-slate-900 border-slate-500 text-white placeholder-gray-400' 
                              : 'bg-[#f9fafb] border-gray-300 text-gray-900 placeholder-gray-500'
                          }`}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Action Button */}
            <button
              onClick={generatePresentation}
              disabled={isLoading || !isFormValid()}
              className={`w-full py-3 px-4 text-sm rounded-lg font-semibold transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 focus:outline-none ${
                isLoading || !isFormValid()
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed transform-none'
                  : isDarkMode
                    ? 'theme-button-dark'
                    : 'theme-button-light'
              }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating Presentation...
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <span className="mr-1">🚀</span>
                  Generate Presentation
                </div>
              )}
            </button>
          </div>

          {/* Output Section */}
          <div className="space-y-4">
            {/* Error Message */}
            {isErrorVisible && (
              <div className={`rounded-md border-l-4 p-3 ${
                isDarkMode 
                  ? 'bg-red-900 border-red-500' 
                  : 'bg-red-50 border-red-500'
              }`}>
                <div className="flex items-center">
                  <svg className={`h-4 w-4 mr-2 ${
                    isDarkMode ? 'text-red-400' : 'text-red-400'
                  }`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className={`text-xs ${
                    isDarkMode ? 'text-red-300' : 'text-red-700'
                  }`}>
                    {errorMessage}
                  </p>
                </div>
              </div>
            )}

            {/* Preview Card */}
            <div className={`rounded-lg shadow-sm border overflow-hidden ${
              isDarkMode 
                ? 'bg-slate-700 border-slate-600' 
                : 'bg-white border-gray-200'
            }`}>
              <div className={`px-4 py-3 border-b ${
                isDarkMode 
                  ? 'bg-slate-600 border-slate-500' 
                  : 'bg-gradient-to-r from-pink-50 to-purple-50 border-gray-200'
              }`}>
                <h3 className={`text-sm font-semibold flex items-center ${
                  isDarkMode ? 'text-white' : 'text-gray-800'
                }`}>
                  <span className="mr-1">📋</span>
                  Generated Presentation
                </h3>
              </div>

              <div className="p-4">
                {/* Default State */}
                {!isOutputVisible && !isLoading && !isErrorVisible && (
                  <div className="text-center py-8">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3 ${
                      isDarkMode ? 'bg-slate-600' : 'bg-gray-100'
                    }`}>
                      <svg className={`w-6 h-6 ${
                        isDarkMode ? 'text-gray-400' : 'text-gray-400'
                      }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                      </svg>
                    </div>
                    <p className={`text-sm mb-1 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-600'
                    }`}>Your generated presentation will appear here</p>
                    <p className={`text-xs ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-400'
                    }`}>Fill in the form and click generate</p>
                  </div>
                )}

                {/* Loading State */}
                {isLoading && (
                  <div className="text-center py-8">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3 ${
                      isDarkMode ? 'bg-slate-600' : 'theme-accent'
                    }`}>
                      <svg className={`animate-spin h-6 w-6 ${
                        isDarkMode ? 'text-gray-300' : 'theme-primary'
                      }`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </div>
                    <p className={`text-sm mb-1 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-600'
                    }`}>Creating your presentation...</p>
                    <p className={`text-xs ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-400'
                    }`}>This may take a few moments</p>
                  </div>
                )}

                {/* Success State */}
                {isOutputVisible && downloadUrl && (
                  <div className="text-center py-8">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3 ${
                      isDarkMode ? 'bg-slate-600' : 'theme-accent'
                    }`}>
                      <svg className={`w-6 h-6 ${
                        isDarkMode ? 'text-green-400' : 'theme-primary'
                      }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
                      </svg>
                    </div>
                    <h4 className={`text-sm font-semibold mb-2 ${
                      isDarkMode ? 'text-white' : 'theme-primary'
                    }`}>Presentation Ready! 🎉</h4>
                    <p className={`text-xs mb-4 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-600'
                    }`}>Your presentation has been generated successfully.</p>
                    <a
                      href={downloadUrl}
                      download
                      className={`inline-flex items-center px-4 py-2 text-sm rounded-lg font-medium transition-all ${
                        isDarkMode
                          ? 'theme-button-dark'
                          : 'theme-button-light theme-border'
                      }`}
                    >
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                      </svg>
                      Download Presentation
                    </a>
                  </div>
                )}
              </div>
            </div>

            {/* Tips Card */}
            {!isLoading && !isOutputVisible && (
              <div className={`rounded-lg p-4 border ${
                isDarkMode 
                  ? 'bg-gradient-to-br from-slate-700 to-slate-600 border-slate-500' 
                  : 'bg-gradient-to-br from-pink-50 to-purple-50 theme-border'
              }`}>
                <h4 className={`text-sm font-semibold mb-2 flex items-center ${
                  isDarkMode ? 'text-white' : 'text-gray-800'
                }`}>
                  <span className="mr-1">💡</span>
                  Pro Tips
                </h4>
                <ul className={`space-y-1 text-xs ${
                  isDarkMode ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  <li className="flex items-start">
                    <span className="mr-1 theme-primary">•</span>
                    Keep your project description concise but impactful
                  </li>
                  <li className="flex items-start">
                    <span className="mr-1 theme-primary">•</span>
                    Mention specific technologies and innovations
                  </li>
                  <li className="flex items-start">
                    <span className="mr-1 theme-primary">•</span>
                    Include measurable business impact metrics
                  </li>
                  <li className="flex items-start">
                    <span className="mr-1 theme-primary">•</span>
                    Choose a template that matches your audience
                  </li>
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default PowerPointerGenerator;



// import React, { useState } from 'react';  // Remove useEffect since we won't need it

// function PowerPointerGenerator() {
//   const [formData, setFormData] = useState({
//     project_name: "HackHelp",
//     problem_statement: "Lack of centralized assistance and support for hackathon participants",
//     solution: "AI-powered platform providing comprehensive hackathon support tools",
//     target_audience: "Hackathon participants, organizers, and mentors",
//     key_features: "LinkedIn post generator, PowerPoint creator, mentor matching, team resource hub",
//     tech_stack: "React, Django, Gemini Pro, python-pptx, TailwindCSS",
//     business_impact: "Improved hackathon experience and increased participant success rate",
//     design_template: "1"  // Default template
//   });

//   const [isOutputVisible, setIsOutputVisible] = useState(false);
//   const [isErrorVisible, setIsErrorVisible] = useState(false);
//   const [errorMessage, setErrorMessage] = useState('');
//   const [isLoading, setIsLoading] = useState(false);
//   const [downloadUrl, setDownloadUrl] = useState('');

//   const handleChange = (e) => {
//     const { name, value } = e.target;
//     setFormData(prev => ({ ...prev, [name]: value }));
//   };

//   const generatePresentation = () => {
//     setIsOutputVisible(false);
//     setIsErrorVisible(false);
//     setIsLoading(true);
    
//     const payload = {
//       ...formData,
//       key_features: formData.key_features.split(',').map(f => f.trim()),
//       tech_stack: formData.tech_stack.split(',').map(t => t.trim())
//     };

//     fetch("http://localhost:8000/api/startup/generate-powerpoint/", {
//       method: "POST",
//       headers: { "Content-Type": "application/json" },
//       body: JSON.stringify(payload)
//     })
//       .then(response => {
//         if (!response.ok) throw new Error(`HTTP ${response.status}`);
//         return response.json();
//       })
//       .then(data => {
//         if (data.url) {
//           const fullUrl = `http://localhost:8000/${data.url}`;
//           setDownloadUrl(fullUrl);
//           setIsOutputVisible(true);
//         } else {
//           throw new Error("No URL received in response");
//         }
//       })
//       .catch(error => {
//         setErrorMessage("❌ Error: " + error.message);
//         setIsErrorVisible(true);
//       })
//       .finally(() => {
//         setIsLoading(false);
//       });
//   };
//   return (
//     <div className="min-h-screen bg-gradient-to-b from-white to-gray-100 py-12 px-4 sm:px-6 lg:px-8">
//       <div className="max-w-4xl mx-auto">
//         <div className="text-center mb-10">
//           <h1 className="text-4xl font-bold text-black mb-3">🎯 PowerPoint Generator</h1>
//           <p className="text-gray-600">Fill out the form below and choose a design template.</p>
//         </div>

//         <div className="bg-white shadow-lg rounded-xl p-6 mb-8">
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
//             {Object.entries(formData).map(([key, val]) => (
//               <div key={key} className="form-group">
//                 <label htmlFor={key} className="block text-sm font-medium text-gray-700 mb-1 capitalize">
//                   {key.replace(/_/g, ' ')}:
//                 </label>
//                 {key === 'design_template' ? (
//                   <select
//                     id={key}
//                     name={key}
//                     value={val}
//                     onChange={handleChange}
//                     className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-sm"
//                   >
//                     <option value="1">Professional (Default)</option>
//                     <option value="2">Creative</option>
//                     <option value="3">Minimal</option>
//                     <option value="4">Academic</option>
//                     <option value="5">Business</option>
//                     <option value="6">Technical</option>
//                     <option value="7">Modern</option>
//                     <option value="8">Corporate</option>
//                     <option value="9">Elegant</option>
//                   </select>
//                 ) : (
//                   <textarea
//                     id={key}
//                     name={key}
//                     value={val}
//                     onChange={handleChange}
//                     rows={key === 'problem_statement' || key === 'solution' || key === 'business_impact' ? 2 : 1}
//                     className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-sm"
//                   />
//                 )}
//               </div>
//             ))}
//           </div>
//         </div>

//         <div className="flex flex-col sm:flex-row justify-center gap-4 mb-8">
//         <button
//             className={`px-6 py-3 rounded-md text-white font-medium transition duration-200 ${
//             isLoading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
//             } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center`}
//             onClick={generatePresentation}
//             disabled={isLoading}
//         >
//             {isLoading ? (
//             <>
//                 <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
//                 <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
//                 <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
//                 </svg>
//                 Generating...
//             </>
//             ) : (
//             'Generate PowerPoint'
//             )}
//         </button>
//         </div>

//         {isErrorVisible && (
//           <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-8 rounded">
//             <div className="flex">
//               <div className="flex-shrink-0">
//                 <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
//                   <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
//                 </svg>
//               </div>
//               <div className="ml-3">
//                 <p className="text-sm text-red-700">
//                   {errorMessage}
//                 </p>
//               </div>
//             </div>
//           </div>
//         )}

//         {isOutputVisible && downloadUrl && (
//           <div className="bg-f3efec rounded-xl shadow-md p-6 mb-8">
//             <h3 className="text-lg font-medium mb-4 text-gray-800">Your PowerPoint is Ready!</h3>
//             <div className="flex justify-center">
//               <a
//                 href={downloadUrl}
//                 download
//                 className="px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition duration-200 flex items-center"
//               >
//                 <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
//                 </svg>
//                 Download Presentation
//               </a>
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// }

// export default PowerPointerGenerator;