import React,{ useState, useRef, useEffect } from 'react';
import { ChevronRight, Code, Smartphone, Brain, Shield, Globe, Heart, GraduationCap, Leaf, Users, Lightbulb, Trophy, Zap, Clock, Target, Presentation, Award, Settings, User, Calendar, FileText, CheckCircle, Star, Search, Bell, Moon, Sun, MoreHorizontal, Grid3X3, Home, Inbox, MessageCircle, ListTodo, BarChart3, Timer, Goal, UserPlus, Plus, Circle, CheckCircle2 } from 'lucide-react';
import LinkedInPostGenerator from '../../Pages/LinkedIn';
import PowerPointerGenerator from '../../Pages/PowerPointer';

const HackathonDashboard = () => {
  const [selectedStage, setSelectedStage] = useState('Development Phase');
  const [selectedCategory, setSelectedCategory] = useState('Web Development');
  const [selectedSubcategory, setSelectedSubcategory] = useState('Frontend Frameworks');
  const [hoveredIcon, setHoveredIcon] = useState(null);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [activeLeftPanel, setActiveLeftPanel] = useState('home');
  const [sidebarWidth, setSidebarWidth] = useState(288); // 72 * 4 = 288px (w-72)
  const [isResizing, setIsResizing] = useState(false);
  const [isLeader, setIsLeader] = useState(true);
  const [selectedMember, setSelectedMember] = useState('All Members');
  const [chatMessages, setChatMessages] = useState([
    {
      id: 1,
      sender: 'Sarah Wilson',
      avatar: 'SW',
      color: 'bg-green-500',
      message: "Hey team! Just finished setting up the database schema. Ready to move on to the API endpoints.",
      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
      type: 'message'
    },
    {
      id: 2,
      sender: 'Mike Chen',
      avatar: 'MC', 
      color: 'bg-purple-500',
      message: "Great work Sarah! I'm working on the authentication system. Should have it ready by end of day.",
      timestamp: new Date(Date.now() - 1000 * 60 * 10), // 10 minutes ago
      type: 'message'
    },
    {
      id: 3,
      sender: 'John Doe',
      avatar: 'JD',
      color: 'bg-blue-500', 
      message: "UI components are looking good. Added the new design system tokens. Check it out when you get a chance!",
      timestamp: new Date(Date.now() - 1000 * 60 * 8), // 8 minutes ago
      type: 'message'
    },
    {
      id: 4,
      sender: 'Emma Davis',
      avatar: 'ED',
      color: 'bg-pink-500',
      message: "📝 Just uploaded the API documentation to the shared drive",
      timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
      type: 'message'
    },
    {
      id: 5,
      sender: 'Alex Johnson',
      avatar: 'AJ',
      color: 'bg-orange-500',
      message: "Mobile testing is complete ✅ Everything looks responsive. Great job everyone!",
      timestamp: new Date(Date.now() - 1000 * 60 * 2), // 2 minutes ago
      type: 'message'
    }
  ]);
  const [newMessage, setNewMessage] = useState('');
  
  const resizeRef = useRef(null);
  const sidebarRef = useRef(null);

  const addMessage = () => {
    if (newMessage.trim()) {
      const message = {
        id: chatMessages.length + 1,
        sender: 'John Doe', // Current user
        avatar: 'JD',
        color: 'bg-blue-500',
        message: newMessage,
        timestamp: new Date(),
        type: 'message'
      };
      setChatMessages([...chatMessages, message]);
      setNewMessage('');
    }
  };

  const formatTime = (timestamp) => {
    const now = new Date();
    const diffMs = now - timestamp;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return timestamp.toLocaleDateString();
  };

  // Sample team members data
  const teamMembers = [
    { id: 1, name: 'John Doe', avatar: 'JD', color: 'bg-blue-500' },
    { id: 2, name: 'Sarah Wilson', avatar: 'SW', color: 'bg-green-500' },
    { id: 3, name: 'Mike Chen', avatar: 'MC', color: 'bg-purple-500' },
    { id: 4, name: 'Emma Davis', avatar: 'ED', color: 'bg-pink-500' },
    { id: 5, name: 'Alex Johnson', avatar: 'AJ', color: 'bg-orange-500' }
  ];

  // Sample tasks data
  const allTasks = [
    { 
      id: 1, 
      title: 'Design UI Components', 
      assignee: 'John Doe', 
      status: 'in-progress', 
      priority: 'high',
      dueDate: '2025-05-27',
      description: 'Create reusable UI components for the dashboard'
    },
    { 
      id: 2, 
      title: 'Setup Database Schema', 
      assignee: 'Sarah Wilson', 
      status: 'completed', 
      priority: 'high',
      dueDate: '2025-05-25',
      description: 'Design and implement the database structure'
    },
    { 
      id: 3, 
      title: 'Implement Authentication', 
      assignee: 'Mike Chen', 
      status: 'todo', 
      priority: 'medium',
      dueDate: '2025-05-28',
      description: 'Add user login and registration functionality'
    },
    { 
      id: 4, 
      title: 'Write API Documentation', 
      assignee: 'Emma Davis', 
      status: 'in-progress', 
      priority: 'low',
      dueDate: '2025-05-30',
      description: 'Document all API endpoints and usage'
    },
    { 
      id: 5, 
      title: 'Test Mobile Responsiveness', 
      assignee: 'Alex Johnson', 
      status: 'completed', 
      priority: 'medium',
      dueDate: '2025-05-24',
      description: 'Ensure the app works well on mobile devices'
    },
    { 
      id: 6, 
      title: 'Deploy to Staging', 
      assignee: 'John Doe', 
      status: 'todo', 
      priority: 'high',
      dueDate: '2025-05-29',
      description: 'Deploy the application to staging environment'
    }
  ];

  // Filter tasks based on role and selected member
  const getFilteredTasks = () => {
    if (!isLeader) {
      // If member, show only their tasks (assuming current user is John Doe)
      return allTasks.filter(task => task.assignee === 'John Doe');
    }
    
    if (selectedMember === 'All Members') {
      return allTasks;
    }
    
    return allTasks.filter(task => task.assignee === selectedMember);
  };

  // Color themes for different stages
  const stageThemes = {
    'Pre-Hackathon': {
      primary: '#2b216a', // Purple
      primaryLight: '#3730a3',
      primaryDark: '#1e1065',
      accent: '#e0e7ff',
      accentText: '#3730a3'
    },
    'Development Phase': {
      primary: '#0d9488', // Teal (default)
      primaryLight: '#14b8a6',
      primaryDark: '#0f766e',
      accent: '#ccfbf1',
      accentText: '#0f766e'
    },
    'Final Preparation': {
      primary: '#53195d', // Violet
      primaryLight: '#7c2d92',
      primaryDark: '#4a1454',
      accent: '#f3e8ff',
      accentText: '#7c2d92'
    },
    'Judging & Awards': {
      primary: '#621639', // Pink
      primaryLight: '#831843',
      primaryDark: '#4c1d3b',
      accent: '#fce7f3',
      accentText: '#831843'
    }
  };

  const currentTheme = stageThemes[selectedStage];

  const stages = {
    'Pre-Hackathon': {
      icon: <Settings className="w-6 h-6" />,
      color: 'bg-gray-500',
      bgGradient: 'from-gray-400 to-gray-600',
      categories: {
        'Registration': {
          icon: <User className="w-5 h-5" />,
          subcategories: {
            'Team Formation': {
              description: 'Build your dream team with complementary skills and shared vision.',
              topics: [
                'Individual Registration - Platform signup and profile creation',
                'Skill Assessment - Evaluate technical and soft skills',
                'Role Assignment - Define team member responsibilities',
                'Team Dynamics - Establish communication and workflow'
              ],
              techStack: ['Communication Tools', 'Project Management', 'Git', 'Slack', 'Discord'],
              difficulty: 'Beginner'
            }
          }
        },
        'Planning': {
          icon: <Calendar className="w-5 h-5" />,
          subcategories: {
            'Strategy Development': {
              description: 'Research and plan your approach before the hackathon begins.',
              topics: [
                'Problem Analysis - Understanding challenge requirements',
                'Solution Research - Investigating existing approaches',
                'Technology Selection - Choosing optimal tech stack',
                'Timeline Planning - Breaking down development phases'
              ],
              techStack: ['Research Tools', 'Mind Mapping', 'Documentation', 'Planning Software'],
              difficulty: 'Intermediate'
            }
          }
        }
      }
    },
    'Development Phase': {
      icon: <Code className="w-6 h-6" />,
      color: 'bg-blue-500',
      bgGradient: 'from-blue-400 to-blue-600',
      categories: {
        'Web Development': {
          icon: <Globe className="w-5 h-5" />,
          subcategories: {
            'Frontend Frameworks': {
              description: 'Build modern, responsive web applications using cutting-edge frontend technologies.',
              topics: [
                'React Applications - Component-based UI development',
                'Vue.js Projects - Progressive framework solutions',
                'Angular Solutions - Enterprise-grade applications',
                'Svelte Apps - Compile-time optimized frameworks'
              ],
              techStack: ['JavaScript', 'TypeScript', 'HTML5', 'CSS3', 'Webpack'],
              difficulty: 'Intermediate'
            },
            'Backend Development': {
              description: 'Create robust server-side applications and APIs that power modern web services.',
              topics: [
                'Node.js APIs - Scalable JavaScript backends',
                'Python Backends - Django/Flask applications',
                'Java Services - Spring Boot microservices',
                'Go Applications - High-performance web servers'
              ],
              techStack: ['Node.js', 'Python', 'Java', 'PostgreSQL', 'MongoDB'],
              difficulty: 'Advanced'
            }
          }
        },
        'Mobile Development': {
          icon: <Smartphone className="w-5 h-5" />,
          subcategories: {
            'Native iOS': {
              description: 'Develop high-performance iOS applications using Swift and native frameworks.',
              topics: [
                'Swift Applications - Native iOS development',
                'SwiftUI Interfaces - Declarative UI framework',
                'Core Data Integration - Local data persistence',
                'iOS Widgets - Home screen extensions'
              ],
              techStack: ['Swift', 'SwiftUI', 'Xcode', 'Core Data', 'UIKit'],
              difficulty: 'Intermediate'
            },
            'Cross-Platform': {
              description: 'Create applications that run on multiple platforms with shared codebases.',
              topics: [
                'React Native Apps - JavaScript-based mobile development',
                'Flutter Applications - Dart-based cross-platform framework',
                'Xamarin Solutions - C# cross-platform development',
                'Ionic Apps - Web-based mobile applications'
              ],
              techStack: ['React Native', 'Flutter', 'Dart', 'JavaScript', 'Cordova'],
              difficulty: 'Advanced'
            }
          }
        },
        'AI & Machine Learning': {
          icon: <Brain className="w-5 h-5" />,
          subcategories: {
            'Natural Language Processing': {
              description: 'Develop intelligent systems that understand and process human language.',
              topics: [
                'Text Analysis Tools - Sentiment and emotion analysis',
                'Language Translation - Real-time translation services',
                'Chatbots & Virtual Assistants - Conversational AI systems',
                'Content Generation - AI-powered writing tools'
              ],
              techStack: ['Python', 'TensorFlow', 'spaCy', 'NLTK', 'Transformers'],
              difficulty: 'Advanced'
            },
            'Computer Vision': {
              description: 'Build systems that can interpret and understand visual information.',
              topics: [
                'Image Recognition - Object and pattern detection',
                'Facial Recognition - Identity verification systems',
                'Medical Imaging - Healthcare diagnostic tools',
                'Augmented Reality - Real-world overlay applications'
              ],
              techStack: ['OpenCV', 'TensorFlow', 'PyTorch', 'YOLO', 'MediaPipe'],
              difficulty: 'Expert'
            }
          }
        }
      }
    },
    'Final Preparation': {
      icon: <FileText className="w-6 h-6" />,
      color: 'bg-orange-500',
      bgGradient: 'from-orange-400 to-orange-600',
      categories: {
        'Documentation': {
          icon: <FileText className="w-5 h-5" />,
          subcategories: {
            'Project Documentation': {
              description: 'Create comprehensive documentation for your hackathon project.',
              topics: [
                'README Creation - Project overview and setup instructions',
                'API Documentation - Endpoint descriptions and examples',
                'User Guides - Step-by-step usage instructions',
                'Technical Architecture - System design documentation'
              ],
              techStack: ['Markdown', 'GitBook', 'Swagger', 'Docusaurus', 'Notion'],
              difficulty: 'Intermediate'
            }
          }
        }
      }
    },
    'Judging & Awards': {
      icon: <Award className="w-6 h-6" />,
      color: 'bg-purple-500',
      bgGradient: 'from-purple-400 to-purple-600',
      categories: {
        'Presentation': {
          icon: <Presentation className="w-5 h-5" />,
          subcategories: {
            'Live Pitching': {
              description: 'Present your solution to judges and showcase your innovation.',
              topics: [
                'Pitch Delivery - Confident presentation techniques',
                'Demo Execution - Flawless product demonstration',
                'Judge Interaction - Professional Q&A handling',
                'Time Management - Effective use of presentation time'
              ],
              techStack: ['Presentation Tools', 'Screen Sharing', 'Video Conferencing', 'Demo Environments'],
              difficulty: 'Advanced'
            },
            'PowerPoint Presentation': {
              "component":<PowerPointerGenerator isDarkMode={isDarkMode}/>
            }
          }
        },
        'Post Hackathon': {
          icon: <Presentation className="w-5 h-5" />,
          subcategories: {
            'LinkedIn Post': {
              "component":<LinkedInPostGenerator isDarkMode={isDarkMode}/>
            }
          }
        }
      }
    }
  };

  const currentStage = stages[selectedStage];
  const currentCategory = currentStage?.categories[selectedCategory];
  const currentSubcategory = currentCategory?.subcategories[selectedSubcategory];

  // Resize functionality
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!isResizing) return;
      
      const newWidth = e.clientX - 56; // 56px is the width of the leftmost sidebar (w-14)
      const minWidth = 200;
      const maxWidth = 500;
      
      if (newWidth >= minWidth && newWidth <= maxWidth) {
        setSidebarWidth(newWidth);
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.body.style.cursor = 'default';
      document.body.style.userSelect = 'auto';
    };

    if (isResizing) {
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  const handleResizeStart = (e) => {
    e.preventDefault();
    setIsResizing(true);
  };

  const getDifficultyColor = (difficulty) => {
    switch(difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-orange-100 text-orange-800';
      case 'Expert': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleStageChange = (stageName) => {
    setSelectedStage(stageName);
    setActiveLeftPanel('home'); // Auto-navigate to home when stage changes
    const firstCategory = Object.keys(stages[stageName].categories)[0];
    setSelectedCategory(firstCategory);
    const firstSubcategory = Object.keys(stages[stageName].categories[firstCategory].subcategories)[0];
    setSelectedSubcategory(firstSubcategory);
  };

  const handleCategoryChange = (categoryName) => {
    setSelectedCategory(categoryName);
    const firstSubcategory = Object.keys(currentStage.categories[categoryName].subcategories)[0];
    setSelectedSubcategory(firstSubcategory);
  };

  return (
    <div className={`h-screen flex flex-col overflow-hidden ${isDarkMode ? 'bg-gray-900' : 'bg-white'} transition-all duration-300 font-sans`}>
      <style jsx>{`
        .custom-scrollbar {
          scrollbar-width: none;
          -ms-overflow-style: none;
        }
        .custom-scrollbar::-webkit-scrollbar {
          display: none;
        }
        .resize-handle {
          position: absolute;
          top: 0;
          right: 0;
          width: 4px;
          height: 100%;
          background: transparent;
          cursor: col-resize;
          z-index: 10;
        }
        .resize-handle:hover {
          background: rgba(59, 130, 246, 0.5);
        }
        .resize-handle.resizing {
          background: rgba(59, 130, 246, 0.8);
        }
      `}</style>
      
      {/* Main Header */}
      <div 
        className={`${isDarkMode ? 'border-gray-700' : 'border-b'} px-6 py-3 shadow-sm flex-shrink-0`}
        style={{
          backgroundColor: isDarkMode ? '#1f2937' : currentTheme.primary,
          borderColor: isDarkMode ? '#374151' : currentTheme.primaryDark
        }}
      >
        <div className="flex items-center justify-between">
          {/* Left - HackHelp Logo */}
          <div className="flex items-center space-x-3">
            <div 
              className={`w-8 h-8 ${isDarkMode ? 'bg-gray-700' : 'bg-white'} rounded-lg flex items-center justify-center`}
            >
              <Trophy 
                className={`w-5 h-5`}
                style={{
                  color: isDarkMode ? '#9ca3af' : currentTheme.primary
                }}
              />
            </div>
            <span className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-white'}`}>HackHelp</span>
          </div>

          {/* Center - Project Name */}
          <div className="flex-1 flex justify-center">
            <div className="flex items-center space-x-2">
              <h1 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-white'}`}>
                Hackathon Project Guide
              </h1>
            </div>
          </div>

          {/* Right - Settings, Toggle, Profile */}
          <div className="flex items-center space-x-3">
            {/* Search */}
            <div className="relative">
              <Search className={`w-5 h-5 ${isDarkMode ? 'text-gray-400' : 'text-white/70'} absolute left-3 top-1/2 transform -translate-y-1/2`} />
              <input
                type="text"
                placeholder="Search everything..."
                className={`pl-10 pr-4 py-2 rounded-lg border text-sm w-64 focus:outline-none focus:ring-2 ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-gray-500 focus:ring-gray-500/20' 
                    : 'border-white/20 text-white placeholder-white/70 focus:border-white focus:ring-white/20'
                }`}
                style={{
                  backgroundColor: isDarkMode ? '#374151' : `${currentTheme.primary}dd`,
                  borderColor: isDarkMode ? '#4b5563' : 'rgba(255,255,255,0.2)'
                }}
              />
              <div className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-xs ${isDarkMode ? 'text-gray-500' : 'text-white/60'}`}>
                Ctrl + K
              </div>
            </div>

            {/* Notifications */}
            <div className="relative">
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode 
                    ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' 
                    : 'text-white/80 hover:text-white'
                }`}
                onMouseEnter={(e) => {
                  if (!isDarkMode) {
                    e.target.style.backgroundColor = currentTheme.primaryDark;
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isDarkMode) {
                    e.target.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <Bell className="w-5 h-5" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
              </button>
              
              {/* Notification Tooltip */}
              {showNotifications && (
                <div className={`absolute right-0 top-full mt-2 w-80 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg shadow-lg border z-50`}>
                  <div className={`p-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                    <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Notifications</h3>
                  </div>
                  <div className="p-4 space-y-3">
                    <div className="text-sm">
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>New hackathon starting soon!</div>
                      <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>AI/ML Challenge 2025 registration open</div>
                    </div>
                    <div className="text-sm">
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Team invitation received</div>
                      <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>You've been invited to join "Code Warriors"</div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Dark Mode Toggle */}
            <button
              onClick={() => setIsDarkMode(!isDarkMode)}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' 
                  : 'text-white/80 hover:text-white'
              }`}
              onMouseEnter={(e) => {
                if (!isDarkMode) {
                  e.target.style.backgroundColor = currentTheme.primaryDark;
                }
              }}
              onMouseLeave={(e) => {
                if (!isDarkMode) {
                  e.target.style.backgroundColor = 'transparent';
                }
              }}
            >
              {isDarkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            </button>

            {/* Apps Grid */}
            <button 
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' 
                  : 'text-white/80 hover:text-white'
              }`}
              onMouseEnter={(e) => {
                if (!isDarkMode) {
                  e.target.style.backgroundColor = currentTheme.primaryDark;
                }
              }}
              onMouseLeave={(e) => {
                if (!isDarkMode) {
                  e.target.style.backgroundColor = 'transparent';
                }
              }}
            >
              <Grid3X3 className="w-5 h-5" />
            </button>

            {/* Settings */}
            <button 
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200' 
                  : 'text-white/80 hover:text-white'
              }`}
              onMouseEnter={(e) => {
                if (!isDarkMode) {
                  e.target.style.backgroundColor = currentTheme.primaryDark;
                }
              }}
              onMouseLeave={(e) => {
                if (!isDarkMode) {
                  e.target.style.backgroundColor = 'transparent';
                }
              }}
            >
              <Settings className="w-5 h-5" />
            </button>

            {/* Profile */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">PY</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden ">
        {/* Leftmost Sidebar - Always Available Features */}
        <div 
          className={`w-14 border-r flex flex-col items-center py-3 space-y-1 flex-shrink-0 m-2 rounded-xl`}
          style={{
            backgroundColor: isDarkMode ? '#111827' : currentTheme.primaryDark,
            borderColor: isDarkMode ? '#374151' : currentTheme.primaryDark
          }}
        >
          {/* HackHelp Logo */}
          <div className={`w-8 h-8 ${isDarkMode ? 'bg-gray-700' : 'bg-white'} rounded-lg flex items-center justify-center mb-3`}>
            <span 
              className="font-bold text-sm"
              style={{ color: isDarkMode ? '#9ca3af' : currentTheme.primary }}
            >H</span>
          </div>

          {/* Main Navigation Icons */}
          <div className="flex flex-col space-y-2">
            {/* Home */}
            <div className="relative group">
              <button
                onClick={() => setActiveLeftPanel('home')}
                onMouseEnter={() => setHoveredIcon('sidebar-home')}
                onMouseLeave={() => setHoveredIcon(null)}
                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${
                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${
                  activeLeftPanel === 'home'
                    ? isDarkMode ? 'bg-white/20' : 'bg-white'
                    : 'hover:bg-white/10'
                }`}>
                  <Home 
                    className="w-4 h-4" 
                    style={activeLeftPanel === 'home' && !isDarkMode ? { color: currentTheme.primary } : {}}
                  />
                </div>
                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'home' ? 'font-semibold' : ''}`}>Home</span>
              </button>
              {hoveredIcon === 'sidebar-home' && (
                <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50">
                  Home
                  <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45"></div>
                </div>
              )}
            </div>

            {/* Inbox */}
            <div className="relative group">
              <button
                onClick={() => setActiveLeftPanel('inbox')}
                onMouseEnter={() => setHoveredIcon('sidebar-inbox')}
                onMouseLeave={() => setHoveredIcon(null)}
                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${
                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${
                  activeLeftPanel === 'inbox'
                    ? isDarkMode ? 'bg-white/20' : 'bg-white'
                    : 'hover:bg-white/10'
                }`}>
                  <Inbox 
                    className="w-4 h-4" 
                    style={activeLeftPanel === 'inbox' && !isDarkMode ? { color: currentTheme.primary } : {}}
                  />
                </div>
                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'inbox' ? 'font-semibold' : ''}`}>Inbox</span>
              </button>
              {hoveredIcon === 'sidebar-inbox' && (
                <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50">
                  Inbox
                  <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45"></div>
                </div>
              )}
            </div>

            {/* Calendar */}
            <div className="relative group">
              <button
                onClick={() => setActiveLeftPanel('calendar')}
                onMouseEnter={() => setHoveredIcon('sidebar-calendar')}
                onMouseLeave={() => setHoveredIcon(null)}
                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${
                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${
                  activeLeftPanel === 'calendar'
                    ? isDarkMode ? 'bg-white/20' : 'bg-white'
                    : 'hover:bg-white/10'
                }`}>
                  <Calendar 
                    className="w-4 h-4" 
                    style={activeLeftPanel === 'calendar' && !isDarkMode ? { color: currentTheme.primary } : {}}
                  />
                </div>
                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'calendar' ? 'font-semibold' : ''}`}>Calendar</span>
              </button>
              {hoveredIcon === 'sidebar-calendar' && (
                <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50">
                  Calendar
                  <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45"></div>
                </div>
              )}
            </div>

            {/* Brain (AI Chat) */}
            <div className="relative group">
              <button
                onClick={() => setActiveLeftPanel('chat')}
                onMouseEnter={() => setHoveredIcon('sidebar-brain')}
                onMouseLeave={() => setHoveredIcon(null)}
                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors relative ${
                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${
                  activeLeftPanel === 'chat'
                    ? isDarkMode ? 'bg-white/20' : 'bg-white'
                    : 'hover:bg-white/10'
                }`}>
                  <Brain 
                    className="w-4 h-4" 
                    style={activeLeftPanel === 'chat' && !isDarkMode ? { color: currentTheme.primary } : {}}
                  />
                </div>
                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'chat' ? 'font-semibold' : ''}`}>Brain</span>
                <div className="absolute top-0 right-0 w-3 h-3 bg-green-500 rounded-full"></div>
              </button>
              {hoveredIcon === 'sidebar-brain' && (
                <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50">
                  Brain (AI Chat)
                  <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45"></div>
                </div>
              )}
            </div>

            {/* Team Chat */}
            <div className="relative group">
              <button
                onClick={() => setActiveLeftPanel('teamchat')}
                onMouseEnter={() => setHoveredIcon('sidebar-teamchat')}
                onMouseLeave={() => setHoveredIcon(null)}
                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${
                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors relative ${
                  activeLeftPanel === 'teamchat'
                    ? isDarkMode ? 'bg-white/20' : 'bg-white'
                    : 'hover:bg-white/10'
                }`}>
                  <MessageCircle 
                    className="w-4 h-4" 
                    style={activeLeftPanel === 'teamchat' && !isDarkMode ? { color: currentTheme.primary } : {}}
                  />
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">3</span>
                  </div>
                </div>
                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'teamchat' ? 'font-semibold' : ''}`}>Chat</span>
              </button>
              {hoveredIcon === 'sidebar-teamchat' && (
                <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50">
                  Team Chat
                  <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45"></div>
                </div>
              )}
            </div>

            {/* Tasks */}
            <div className="relative group">
              <button
                onClick={() => setActiveLeftPanel('tasks')}
                onMouseEnter={() => setHoveredIcon('sidebar-tasks')}
                onMouseLeave={() => setHoveredIcon(null)}
                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${
                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${
                  activeLeftPanel === 'tasks'
                    ? isDarkMode ? 'bg-white/20' : 'bg-white'
                    : 'hover:bg-white/10'
                }`}>
                  <ListTodo 
                    className="w-4 h-4" 
                    style={activeLeftPanel === 'tasks' && !isDarkMode ? { color: currentTheme.primary } : {}}
                  />
                </div>
                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'tasks' ? 'font-semibold' : ''}`}>Tasks</span>
              </button>
              {hoveredIcon === 'sidebar-tasks' && (
                <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50">
                  Tasks
                  <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45"></div>
                </div>
              )}
            </div>

            {/* Goals */}
            <div className="relative group">
              <button
                onClick={() => setActiveLeftPanel('goals')}
                onMouseEnter={() => setHoveredIcon('sidebar-goals')}
                onMouseLeave={() => setHoveredIcon(null)}
                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${
                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${
                  activeLeftPanel === 'goals'
                    ? isDarkMode ? 'bg-white/20' : 'bg-white'
                    : 'hover:bg-white/10'
                }`}>
                  <Goal 
                    className="w-4 h-4" 
                    style={activeLeftPanel === 'goals' && !isDarkMode ? { color: currentTheme.primary } : {}}
                  />
                </div>
                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'goals' ? 'font-semibold' : ''}`}>Goals</span>
              </button>
              {hoveredIcon === 'sidebar-goals' && (
                <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50">
                  Goals
                  <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45"></div>
                </div>
              )}
            </div>

            {/* Timesheet */}
            <div className="relative group">
              <button
                onClick={() => setActiveLeftPanel('timer')}
                onMouseEnter={() => setHoveredIcon('sidebar-timesheet')}
                onMouseLeave={() => setHoveredIcon(null)}
                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${
                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${
                  activeLeftPanel === 'timer'
                    ? isDarkMode ? 'bg-white/20' : 'bg-white'
                    : 'hover:bg-white/10'
                }`}>
                  <Timer 
                    className="w-4 h-4" 
                    style={activeLeftPanel === 'timer' && !isDarkMode ? { color: currentTheme.primary } : {}}
                  />
                </div>
                <span className={`text-xs mt-0.5 ${activeLeftPanel === 'timer' ? 'font-semibold' : ''}`}>Timesheet</span>
              </button>
              {hoveredIcon === 'sidebar-timesheet' && (
                <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50">
                  Timesheet
                  <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45"></div>
                </div>
              )}
            </div>

            {/* More */}
            <div className="relative group">
              <button
                onMouseEnter={() => setHoveredIcon('sidebar-more')}
                onMouseLeave={() => setHoveredIcon(null)}
                className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${
                  isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                <div className="w-8 h-8 rounded-lg flex items-center justify-center transition-colors hover:bg-white/10">
                  <MoreHorizontal className="w-4 h-4" />
                </div>
                <span className="text-xs mt-0.5">More</span>
              </button>
              {hoveredIcon === 'sidebar-more' && (
                <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50">
                  More Apps
                  <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45"></div>
                </div>
              )}
            </div>
          </div>

          {/* Bottom Section */}
          <div className="flex-1"></div>
          
          {/* Invite */}
          <div className="relative group">
            <button
              onMouseEnter={() => setHoveredIcon('sidebar-invite')}
              onMouseLeave={() => setHoveredIcon(null)}
              className={`w-10 h-10 flex flex-col items-center justify-center transition-colors ${
                isDarkMode ? 'text-white/70 hover:text-white' : 'text-white/70 hover:text-white'
              }`}
            >
              <div className="w-8 h-8 rounded-lg flex items-center justify-center transition-colors hover:bg-white/10">
                <UserPlus className="w-4 h-4" />
              </div>
              <span className="text-xs mt-0.5">Invite</span>
            </button>
            {hoveredIcon === 'sidebar-invite' && (
              <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 bg-black/80 text-white text-sm rounded-lg whitespace-nowrap z-50">
                Invite Members
                <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-black/80 rotate-45"></div>
              </div>
            )}
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Categories/Stages Sidebar - Now Resizable */}
          <div 
            ref={sidebarRef}
            className={`border-r flex flex-col flex-shrink-0 relative`}
            style={{ 
              width: `${sidebarWidth}px`,
              backgroundColor: isDarkMode ? '#1f2937' : '#f9fafb',
              borderColor: isDarkMode ? '#374151' : '#e5e7eb'
            }}
          >
            {/* Resize Handle */}
            <div
              ref={resizeRef}
              className={`resize-handle ${isResizing ? 'resizing' : ''}`}
              onMouseDown={handleResizeStart}
            />

            {activeLeftPanel === 'home' ? (
              <div className="flex-1 overflow-y-auto custom-scrollbar">
                {/* Hackathon Stages Section */}
                <div className={`p-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <h2 className={`text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>Hackathon Stages</h2>
                    <button className={`${isDarkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`}>
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className={`flex justify-between text-xs mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      <span>Progress</span>
                      <span>{Math.round(((Object.keys(stages).indexOf(selectedStage) + 1) / Object.keys(stages).length) * 100)}%</span>
                    </div>
                    <div className={`w-full rounded-full h-2 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                      <div 
                        className="h-2 rounded-full transition-all duration-700 ease-out"
                        style={{
                          backgroundColor: currentTheme.primary,
                          width: `${((Object.keys(stages).indexOf(selectedStage) + 1) / Object.keys(stages).length) * 100}%`
                        }}
                      ></div>
                    </div>
                  </div>

                  <div className="space-y-1">
                    {Object.entries(stages).map(([stageName, stageData]) => {
                      const isActive = selectedStage === stageName;
                      const isPast = Object.keys(stages).indexOf(selectedStage) > Object.keys(stages).indexOf(stageName);
                      
                      return (
                        <button
                          key={stageName}
                          onClick={() => handleStageChange(stageName)}
                          className={`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-3 ${
                            isActive
                              ? 'font-medium'
                              : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'
                          }`}
                          style={{
                            backgroundColor: isActive ? currentTheme.accent : 'transparent',
                            color: isActive ? currentTheme.accentText : (isDarkMode ? '#d1d5db' : '#374151')
                          }}
                        >
                          <div className="flex items-center justify-center w-5 h-5">
                            {isPast && !isActive ? (
                              <CheckCircle className="w-4 h-4 text-green-600" />
                            ) : (
                              React.cloneElement(stageData.icon, { 
                                className: `w-4 h-4`,
                                style: {
                                  color: isActive ? currentTheme.accentText : '#6b7280'
                                }
                              })
                            )}
                          </div>
                          <span>{stageName}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Categories Section */}
                <div className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className={`text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>Categories</h2>
                    <button className={`${isDarkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`}>
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="space-y-1">
                    {Object.entries(currentStage?.categories || {}).map(([categoryName, categoryData]) => (
                      <button
                        key={categoryName}
                        onClick={() => handleCategoryChange(categoryName)}
                        className={`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-3 ${
                          selectedCategory === categoryName
                            ? 'font-medium'
                            : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'
                        }`}
                        style={{
                          backgroundColor: selectedCategory === categoryName ? currentTheme.accent : 'transparent',
                          color: selectedCategory === categoryName ? currentTheme.accentText : (isDarkMode ? '#d1d5db' : '#374151')
                        }}
                      >
                        <div className="flex items-center justify-center w-5 h-5">
                          {categoryData.icon}
                        </div>
                        <span>{categoryName}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Subcategories */}
                {currentCategory && (
                  <div className={`border-t p-4 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                    <h3 className={`text-xs font-semibold uppercase tracking-wide mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      Subcategories
                    </h3>
                    <div className="space-y-1">
                      {Object.keys(currentCategory.subcategories).map((subcategoryName) => (
                        <button
                          key={subcategoryName}
                          onClick={() => setSelectedSubcategory(subcategoryName)}
                          className={`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center justify-between ${
                            selectedSubcategory === subcategoryName
                              ? 'font-medium'
                              : isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'
                          }`}
                          style={{
                            backgroundColor: selectedSubcategory === subcategoryName ? currentTheme.accent : 'transparent',
                            color: selectedSubcategory === subcategoryName ? currentTheme.accentText : (isDarkMode ? '#9ca3af' : '#4b5563')
                          }}
                        >
                          <span>{subcategoryName}</span>
                          <ChevronRight className="w-3 h-3" />
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex-1 overflow-y-auto custom-scrollbar">
                <div className="p-4">
                  {/* Dynamic Content based on activeLeftPanel */}
                  {activeLeftPanel === 'inbox' && (
                    <div>
                      <h2 className="text-sm font-semibold text-gray-900 mb-4">Inbox</h2>
                      <div className="space-y-3">
                        <div className="p-3 rounded-lg bg-white border">
                          <div className="flex items-center space-x-2 mb-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span className="text-sm font-medium text-gray-900">New Task Assignment</span>
                            <span className="text-xs text-gray-500">2h ago</span>
                          </div>
                          <p className="text-sm text-gray-700">
                            You've been assigned to work on the frontend components.
                          </p>
                        </div>
                        <div className="p-3 rounded-lg bg-white border">
                          <div className="flex items-center space-x-2 mb-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span className="text-sm font-medium text-gray-900">Code Review Completed</span>
                            <span className="text-xs text-gray-500">4h ago</span>
                          </div>
                          <p className="text-sm text-gray-700">
                            Your pull request has been approved and merged.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeLeftPanel === 'chat' && (
                    <div>
                      <h2 className="text-sm font-semibold text-gray-900 mb-4">AI Brain Chat</h2>
                      <div className="space-y-3">
                        <div className="p-3 rounded-lg bg-white border">
                          <div className="flex items-center space-x-2 mb-2">
                            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                              <Brain className="w-3 h-3 text-white" />
                            </div>
                            <span className="text-sm font-medium text-gray-900">AI Assistant</span>
                            <span className="text-xs text-gray-500">now</span>
                          </div>
                          <p className="text-sm text-gray-700">
                            How can I help you with your hackathon project today?
                          </p>
                        </div>
                        <div className="p-3 rounded-lg bg-blue-50 border border-blue-200">
                          <div className="flex items-center space-x-2 mb-2">
                            <div className="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">You</span>
                            </div>
                            <span className="text-sm font-medium text-gray-900">You</span>
                            <span className="text-xs text-gray-500">1m ago</span>
                          </div>
                          <p className="text-sm text-gray-700">
                            I need help with React component optimization.
                          </p>
                        </div>
                      </div>
                      <div className="mt-4">
                        <input
                          type="text"
                          placeholder="Ask AI Brain anything..."
                          className="w-full px-3 py-2 rounded border text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  )}

                  {activeLeftPanel === 'calendar' && (
                    <div>
                      <h2 className="text-sm font-semibold text-gray-900 mb-4">Calendar</h2>
                      <div className="space-y-3">
                        <div className="p-3 rounded bg-white border">
                          <div className="text-sm font-medium text-blue-600">Today - 10:00 AM</div>
                          <div className="text-sm text-gray-900 mt-1">Team Standup Meeting</div>
                          <div className="text-xs text-gray-500 mt-1">15 minutes</div>
                        </div>
                        <div className="p-3 rounded bg-white border">
                          <div className="text-sm font-medium text-green-600">Today - 2:00 PM</div>
                          <div className="text-sm text-gray-900 mt-1">Code Review Session</div>
                          <div className="text-xs text-gray-500 mt-1">1 hour</div>
                        </div>
                        <div className="p-3 rounded bg-white border">
                          <div className="text-sm font-medium text-purple-600">Tomorrow - 9:00 AM</div>
                          <div className="text-sm text-gray-900 mt-1">Hackathon Presentation</div>
                          <div className="text-xs text-gray-500 mt-1">30 minutes</div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeLeftPanel === 'timer' && (
                    <div>
                      <h2 className="text-sm font-semibold text-gray-900 mb-4">Time Tracker</h2>
                      <div className="text-center mb-6">
                        <div className="text-2xl font-mono text-gray-900 mb-2">02:34:18</div>
                        <div className="text-sm text-gray-500">Working on: Frontend Development</div>
                      </div>
                      <div className="space-y-3">
                        <button className="w-full py-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors text-sm">
                          Start Timer
                        </button>
                        <button className="w-full py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors text-sm">
                          Stop Timer
                        </button>
                      </div>
                      <div className="mt-6">
                        <h3 className="text-xs font-semibold text-gray-500 mb-3">Today's Sessions</h3>
                        <div className="space-y-2">
                          <div className="p-2 rounded bg-white border">
                            <div className="text-sm text-gray-900">Backend Development</div>
                            <div className="text-xs text-gray-500">1h 23m</div>
                          </div>
                          <div className="p-2 rounded bg-white border">
                            <div className="text-sm text-gray-900">Code Review</div>
                            <div className="text-xs text-gray-500">45m</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeLeftPanel === 'teamchat' && (
                    <div className="flex flex-col h-full">
                      <div className="flex items-center justify-between mb-4">
                        <h2 className={`text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>Team Chat</h2>
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center space-x-1">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              {teamMembers.length} online
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Team Members Online */}
                      <div className="mb-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className={`text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Team Members</span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {teamMembers.map((member) => (
                            <div key={member.id} className="flex items-center space-x-1">
                              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs relative ${member.color}`}>
                                {member.avatar}
                                <div className="absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-green-500 rounded-full border border-white"></div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Quick Actions */}
                      <div className="mb-4 grid grid-cols-2 gap-2">
                        <button 
                          className="px-3 py-2 rounded text-xs font-medium transition-colors text-white"
                          style={{ backgroundColor: currentTheme.primary }}
                        >
                          📢 Announce
                        </button>
                        <button className={`px-3 py-2 rounded text-xs font-medium transition-colors ${
                          isDarkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}>
                          📎 Share File
                        </button>
                      </div>

                      {/* Recent Activity */}
                      <div className="mb-4">
                        <h3 className={`text-xs font-semibold uppercase tracking-wide mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          Recent Activity
                        </h3>
                        <div className="space-y-2">
                          <div className={`text-xs p-2 rounded ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                            <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Sarah</span>
                            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}> completed "Setup Database"</span>
                          </div>
                          <div className={`text-xs p-2 rounded ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                            <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Mike</span>
                            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}> shared a file</span>
                          </div>
                        </div>
                      </div>

                      {/* Message Count */}
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} text-center`}>
                        {chatMessages.length} messages today
                      </div>
                    </div>
                  )}

                  {activeLeftPanel === 'tasks' && (
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h2 className={`text-sm font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>Tasks</h2>
                        <button 
                          onClick={() => setIsLeader(!isLeader)}
                          className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                            isLeader 
                              ? 'text-white'
                              : isDarkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                          style={isLeader ? { backgroundColor: currentTheme.primary } : {}}
                        >
                          {isLeader ? 'Leader' : 'Member'}
                        </button>
                      </div>

                      {/* Member filter for leaders */}
                      {isLeader && (
                        <div className="mb-4">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className={`text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Filter by:</span>
                          </div>
                          <div className="space-y-1">
                            <button
                              onClick={() => setSelectedMember('All Members')}
                              className={`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-2 ${
                                selectedMember === 'All Members'
                                  ? 'text-white font-medium'
                                  : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'
                              }`}
                              style={selectedMember === 'All Members' ? { backgroundColor: currentTheme.accent, color: currentTheme.accentText } : {}}
                            >
                              <Users className="w-4 h-4" />
                              <span>All Members</span>
                            </button>
                            {teamMembers.map((member) => (
                              <button
                                key={member.id}
                                onClick={() => setSelectedMember(member.name)}
                                className={`w-full text-left px-3 py-2 rounded text-sm transition-colors flex items-center space-x-2 ${
                                  selectedMember === member.name
                                    ? 'text-white font-medium'
                                    : isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-700'
                                }`}
                                style={selectedMember === member.name ? { backgroundColor: currentTheme.accent, color: currentTheme.accentText } : {}}
                              >
                                <div className={`w-5 h-5 rounded-full flex items-center justify-center text-white text-xs ${member.color}`}>
                                  {member.avatar}
                                </div>
                                <span>{member.name}</span>
                              </button>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Task Status Counts */}
                      <div className="mb-4 grid grid-cols-3 gap-2">
                        <div className={`p-2 rounded border text-center ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border'}`}
                             style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '30' } : {}}>
                          <div className="text-xs text-gray-500">To Do</div>
                          <div className={`text-sm font-semibold`} style={{ color: currentTheme.primary }}>
                            {getFilteredTasks().filter(t => t.status === 'todo').length}
                          </div>
                        </div>
                        <div className={`p-2 rounded border text-center ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border'}`}
                             style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '30' } : {}}>
                          <div className="text-xs text-gray-500">In Progress</div>
                          <div className={`text-sm font-semibold`} style={{ color: currentTheme.primary }}>
                            {getFilteredTasks().filter(t => t.status === 'in-progress').length}
                          </div>
                        </div>
                        <div className={`p-2 rounded border text-center ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border'}`}
                             style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '30' } : {}}>
                          <div className="text-xs text-gray-500">Done</div>
                          <div className={`text-sm font-semibold`} style={{ color: currentTheme.primary }}>
                            {getFilteredTasks().filter(t => t.status === 'completed').length}
                          </div>
                        </div>
                      </div>

                      {/* Quick Actions */}
                      <div className="space-y-2">
                        <button className="w-full py-2 rounded border border-dashed border-gray-300 text-gray-500 hover:bg-gray-50 transition-colors text-sm">
                          <Plus className="w-4 h-4 inline mr-2" />
                          Add Task
                        </button>
                        
                        <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} text-center`}>
                          {isLeader && selectedMember !== 'All Members' 
                            ? `Showing ${selectedMember}'s tasks`
                            : `${getFilteredTasks().length} tasks total`
                          }
                        </div>
                      </div>
                    </div>
                  )}

                  {activeLeftPanel === 'goals' && (
                    <div>
                      <h2 className="text-sm font-semibold text-gray-900 mb-4">Goals</h2>
                      <div className="space-y-3">
                        <div className="p-3 rounded border-l-4 border-green-500 bg-white border-t border-r border-b">
                          <div className="text-sm font-medium text-gray-900">Complete MVP</div>
                          <div className="text-xs text-gray-500 mt-1">Due: End of hackathon</div>
                          <div className="flex items-center mt-2">
                            <div className="w-full bg-gray-200 rounded-full h-1.5 mr-2">
                              <div className="bg-green-500 h-1.5 rounded-full" style={{width: '75%'}}></div>
                            </div>
                            <span className="text-xs text-green-600">75%</span>
                          </div>
                        </div>
                        <div className="p-3 rounded border-l-4 border-blue-500 bg-white border-t border-r border-b">
                          <div className="text-sm font-medium text-gray-900">Learn React Hooks</div>
                          <div className="text-xs text-gray-500 mt-1">Personal goal</div>
                          <div className="flex items-center mt-2">
                            <div className="w-full bg-gray-200 rounded-full h-1.5 mr-2">
                              <div className="bg-blue-500 h-1.5 rounded-full" style={{width: '60%'}}></div>
                            </div>
                            <span className="text-xs text-blue-600">60%</span>
                          </div>
                        </div>
                        <div className="p-3 rounded border-l-4 border-purple-500 bg-white border-t border-r border-b">
                          <div className="text-sm font-medium text-gray-900">Team Collaboration</div>
                          <div className="text-xs text-gray-500 mt-1">Soft skill development</div>
                          <div className="flex items-center mt-2">
                            <div className="w-full bg-gray-200 rounded-full h-1.5 mr-2">
                              <div className="bg-purple-500 h-1.5 rounded-full" style={{width: '90%'}}></div>
                            </div>
                            <span className="text-xs text-purple-600">90%</span>
                          </div>
                        </div>
                      </div>
                      <button className="mt-4 w-full py-2 rounded border border-dashed border-gray-300 text-gray-500 hover:bg-gray-50 transition-colors text-sm">
                        <Plus className="w-4 h-4 inline mr-2" />
                        Add Goal
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Main Content */}
          <div className={`flex-1 overflow-y-auto custom-scrollbar ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}>
            {/* Team Chat workspace content */}
            {activeLeftPanel === 'teamchat' && (
              <div className="flex flex-col h-full">
                {/* Chat Header */}
                <div className={`p-4 border-b ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200'}`}
                     style={!isDarkMode ? { backgroundColor: currentTheme.accent } : {}}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div 
                        className="w-10 h-10 rounded-lg flex items-center justify-center text-white"
                        style={{ backgroundColor: currentTheme.primary }}
                      >
                        <MessageCircle className="w-5 h-5" />
                      </div>
                      <div>
                        <h1 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          Team Chat
                        </h1>
                        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {teamMembers.length} members • {teamMembers.length} online
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button 
                        className={`p-2 rounded-lg transition-colors ${
                          isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-white/50 text-gray-600'
                        }`}
                      >
                        <Search className="w-4 h-4" />
                      </button>
                      <button 
                        className={`p-2 rounded-lg transition-colors ${
                          isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-white/50 text-gray-600'
                        }`}
                      >
                        <MoreHorizontal className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Chat Messages */}
                <div className={`flex-1 overflow-y-auto p-4 space-y-4 ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}>
                  {chatMessages.map((msg, index) => (
                    <div key={msg.id} className="flex items-start space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs flex-shrink-0 ${msg.color}`}>
                        {msg.avatar}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {msg.sender}
                          </span>
                          <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            {formatTime(msg.timestamp)}
                          </span>
                        </div>
                        <div 
                          className={`inline-block px-3 py-2 rounded-lg max-w-md ${
                            msg.sender === 'John Doe' 
                              ? 'text-white ml-auto'
                              : isDarkMode ? 'bg-gray-800 text-gray-200' : 'bg-gray-100 text-gray-900'
                          }`}
                          style={msg.sender === 'John Doe' ? { backgroundColor: currentTheme.primary } : {}}
                        >
                          <p className="text-sm">{msg.message}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Message Input */}
                <div className={`p-4 border-t ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`}>
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <button 
                        className={`p-2 rounded-lg transition-colors ${
                          isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-200 text-gray-600'
                        }`}
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                    <div className="flex-1 relative">
                      <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && addMessage()}
                        placeholder="Type a message..."
                        className={`w-full px-4 py-2 rounded-lg border focus:outline-none focus:ring-2 ${
                          isDarkMode 
                            ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-gray-500' 
                            : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500'
                        }`}
                        style={!isDarkMode ? { focusRingColor: currentTheme.primary } : {}}
                      />
                    </div>
                    <button 
                      onClick={addMessage}
                      disabled={!newMessage.trim()}
                      className={`p-2 rounded-lg transition-colors text-white ${
                        newMessage.trim() 
                          ? 'opacity-100' 
                          : 'opacity-50 cursor-not-allowed'
                      }`}
                      style={{ backgroundColor: currentTheme.primary }}
                    >
                      <MessageCircle className="w-4 h-4" />
                    </button>
                  </div>
                  
                  {/* Typing indicator */}
                  <div className={`mt-2 text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    <span className="flex items-center space-x-1">
                      <div className="flex space-x-1">
                        <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                      <span>Sarah is typing...</span>
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Tasks workspace content */}
            {activeLeftPanel === 'tasks' && (
              <div className="p-6">
                <div className="max-w-6xl">
                  {/* Header */}
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h1 className={`text-2xl font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {isLeader && selectedMember !== 'All Members' ? `${selectedMember}'s Tasks` : 'All Tasks'}
                        </h1>
                        <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {isLeader 
                            ? selectedMember === 'All Members' 
                              ? 'Manage all team tasks and assignments'
                              : `Tasks assigned to ${selectedMember}`
                            : 'Your personal task list'
                          }
                        </p>
                      </div>
                      <div className="flex items-center space-x-3">
                        <button className={`px-4 py-2 rounded-lg border transition-colors ${
                          isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}>
                          Sort by Due Date
                        </button>
                        <button 
                          className="px-4 py-2 text-white rounded-lg transition-colors flex items-center space-x-2"
                          style={{ 
                            backgroundColor: currentTheme.primary,
                            '&:hover': { backgroundColor: currentTheme.primaryDark }
                          }}
                          onMouseEnter={(e) => e.target.style.backgroundColor = currentTheme.primaryDark}
                          onMouseLeave={(e) => e.target.style.backgroundColor = currentTheme.primary}
                        >
                          <Plus className="w-4 h-4" />
                          <span>New Task</span>
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Task Columns */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* To Do Column */}
                    <div className={`rounded-lg p-4`} style={{ backgroundColor: isDarkMode ? '#1f2937' : currentTheme.accent }}>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          To Do
                        </h3>
                        <span 
                          className="px-2 py-1 rounded text-xs font-medium text-white"
                          style={{ backgroundColor: currentTheme.primary }}
                        >
                          {getFilteredTasks().filter(t => t.status === 'todo').length}
                        </span>
                      </div>
                      <div className="space-y-3">
                        {getFilteredTasks().filter(task => task.status === 'todo').map((task) => (
                          <div key={task.id} className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                            isDarkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-200 hover:shadow-lg'
                          }`}
                          style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '20' } : {}}
                          onMouseEnter={(e) => {
                            if (!isDarkMode) {
                              e.target.style.borderColor = currentTheme.primaryLight + '40';
                              e.target.style.transform = 'translateY(-1px)';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!isDarkMode) {
                              e.target.style.borderColor = currentTheme.primaryLight + '20';
                              e.target.style.transform = 'translateY(0)';
                            }
                          }}
                          >
                            <div className="flex items-start justify-between mb-2">
                              <h4 className={`font-medium text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                {task.title}
                              </h4>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${
                                task.priority === 'high' 
                                  ? 'bg-red-100 text-red-700'
                                  : task.priority === 'medium'
                                  ? 'bg-yellow-100 text-yellow-700'
                                  : 'bg-gray-100 text-gray-700'
                              }`}>
                                {task.priority}
                              </span>
                            </div>
                            <p className={`text-xs mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                              {task.description}
                            </p>
                            <div className="flex items-center justify-between">
                              {isLeader && (
                                <div className="flex items-center space-x-2">
                                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs ${
                                    teamMembers.find(m => m.name === task.assignee)?.color || 'bg-gray-500'
                                  }`}>
                                    {teamMembers.find(m => m.name === task.assignee)?.avatar || 'U'}
                                  </div>
                                  <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                    {task.assignee}
                                  </span>
                                </div>
                              )}
                              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                {new Date(task.dueDate).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        ))}
                        {getFilteredTasks().filter(t => t.status === 'todo').length === 0 && (
                          <div className={`text-center py-8 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                            <Circle className="w-8 h-8 mx-auto mb-2 opacity-50" />
                            <p className="text-sm">No tasks to do</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* In Progress Column */}
                    <div className={`rounded-lg p-4`} style={{ backgroundColor: isDarkMode ? '#1f2937' : currentTheme.accent }}>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          In Progress
                        </h3>
                        <span 
                          className="px-2 py-1 rounded text-xs font-medium text-white"
                          style={{ backgroundColor: currentTheme.primary }}
                        >
                          {getFilteredTasks().filter(t => t.status === 'in-progress').length}
                        </span>
                      </div>
                      <div className="space-y-3">
                        {getFilteredTasks().filter(task => task.status === 'in-progress').map((task) => (
                          <div key={task.id} className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                            isDarkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-200 hover:shadow-lg'
                          }`}
                          style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '20' } : {}}
                          onMouseEnter={(e) => {
                            if (!isDarkMode) {
                              e.target.style.borderColor = currentTheme.primaryLight + '40';
                              e.target.style.transform = 'translateY(-1px)';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!isDarkMode) {
                              e.target.style.borderColor = currentTheme.primaryLight + '20';
                              e.target.style.transform = 'translateY(0)';
                            }
                          }}
                          >
                            <div className="flex items-start justify-between mb-2">
                              <h4 className={`font-medium text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                {task.title}
                              </h4>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${
                                task.priority === 'high' 
                                  ? 'bg-red-100 text-red-700'
                                  : task.priority === 'medium'
                                  ? 'bg-yellow-100 text-yellow-700'
                                  : 'bg-gray-100 text-gray-700'
                              }`}>
                                {task.priority}
                              </span>
                            </div>
                            <p className={`text-xs mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                              {task.description}
                            </p>
                            <div className="flex items-center justify-between">
                              {isLeader && (
                                <div className="flex items-center space-x-2">
                                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs ${
                                    teamMembers.find(m => m.name === task.assignee)?.color || 'bg-gray-500'
                                  }`}>
                                    {teamMembers.find(m => m.name === task.assignee)?.avatar || 'U'}
                                  </div>
                                  <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                    {task.assignee}
                                  </span>
                                </div>
                              )}
                              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                {new Date(task.dueDate).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        ))}
                        {getFilteredTasks().filter(t => t.status === 'in-progress').length === 0 && (
                          <div className={`text-center py-8 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                            <div className="w-8 h-8 rounded-full border-2 border-gray-300 mx-auto mb-2 opacity-50"></div>
                            <p className="text-sm">No tasks in progress</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Done Column */}
                    <div className={`rounded-lg p-4`} style={{ backgroundColor: isDarkMode ? '#1f2937' : currentTheme.accent }}>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          Done
                        </h3>
                        <span 
                          className="px-2 py-1 rounded text-xs font-medium text-white"
                          style={{ backgroundColor: currentTheme.primary }}
                        >
                          {getFilteredTasks().filter(t => t.status === 'completed').length}
                        </span>
                      </div>
                      <div className="space-y-3">
                        {getFilteredTasks().filter(task => task.status === 'completed').map((task) => (
                          <div key={task.id} className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                            isDarkMode ? 'bg-gray-800 border-gray-600 hover:border-gray-500' : 'bg-white border-gray-200 hover:shadow-lg'
                          }`}
                          style={!isDarkMode ? { borderColor: currentTheme.primaryLight + '20' } : {}}
                          onMouseEnter={(e) => {
                            if (!isDarkMode) {
                              e.target.style.borderColor = currentTheme.primaryLight + '40';
                              e.target.style.transform = 'translateY(-1px)';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!isDarkMode) {
                              e.target.style.borderColor = currentTheme.primaryLight + '20';
                              e.target.style.transform = 'translateY(0)';
                            }
                          }}
                          >
                            <div className="flex items-start justify-between mb-2">
                              <h4 className={`font-medium text-sm line-through ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                                {task.title}
                              </h4>
                              <CheckCircle2 
                                className="w-5 h-5" 
                                style={{ color: currentTheme.primary }}
                              />
                            </div>
                            <p className={`text-xs mb-3 ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                              {task.description}
                            </p>
                            <div className="flex items-center justify-between">
                              {isLeader && (
                                <div className="flex items-center space-x-2">
                                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs ${
                                    teamMembers.find(m => m.name === task.assignee)?.color || 'bg-gray-500'
                                  }`}>
                                    {teamMembers.find(m => m.name === task.assignee)?.avatar || 'U'}
                                  </div>
                                  <span className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                                    {task.assignee}
                                  </span>
                                </div>
                              )}
                              <span className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                                {new Date(task.dueDate).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        ))}
                        {getFilteredTasks().filter(t => t.status === 'completed').length === 0 && (
                          <div className={`text-center py-8 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                            <CheckCircle2 className="w-8 h-8 mx-auto mb-2 opacity-50" />
                            <p className="text-sm">No completed tasks</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeLeftPanel === 'home' && currentSubcategory && (
              currentSubcategory.component ? 
                currentSubcategory.component 
              : 
              <div className="p-6">
                <div className="max-w-4xl">
                  {/* Header */}
                  <div className="mb-6">
                    <div className={`flex items-center space-x-2 text-xs mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      <span>{selectedStage}</span>
                      <ChevronRight className="w-3 h-3" />
                      <span>{selectedCategory}</span>
                      <ChevronRight className="w-3 h-3" />
                      <span>{selectedSubcategory}</span>
                    </div>
                    <h1 className={`text-2xl font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{selectedSubcategory}</h1>
                    <p className={`mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>{currentSubcategory.description}</p>
                    
                    <div className="flex items-center space-x-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(currentSubcategory.difficulty)}`}>
                        {currentSubcategory.difficulty}
                      </span>
                      <div className="flex items-center space-x-2">
                        <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Tech Stack:</span>
                        <div className="flex flex-wrap gap-2">
                          {currentSubcategory.techStack.slice(0, 3).map((tech, index) => (
                            <span key={index} className={`px-2 py-1 rounded text-xs border ${
                              isDarkMode ? 'bg-gray-800 text-gray-300 border-gray-700' : 'bg-gray-100 text-gray-700 border'
                            }`}>
                              {tech}
                            </span>
                          ))}
                          {currentSubcategory.techStack.length > 3 && (
                            <span className={`px-2 py-1 rounded text-xs border ${
                              isDarkMode ? 'bg-gray-800 text-gray-300 border-gray-700' : 'bg-gray-100 text-gray-700 border'
                            }`}>
                              +{currentSubcategory.techStack.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Topics */}
                  <div className="mb-6">
                    <h2 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Project Ideas & Topics</h2>
                    <div className="grid gap-4">
                      {currentSubcategory.topics.map((topic, index) => (
                        <div key={index} className={`rounded-lg p-4 border transition-colors ${
                          isDarkMode ? 'bg-gray-800 border-gray-700 hover:bg-gray-750' : 'bg-white border hover:bg-gray-50'
                        }`}>
                          <div className="flex items-start space-x-4">
                            <div className={`w-6 h-6 rounded flex items-center justify-center flex-shrink-0 ${
                              isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-blue-100 text-blue-600'
                            }`}>
                              <span className="font-semibold text-xs">{index + 1}</span>
                            </div>
                            <div>
                              <h3 className={`font-medium mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                {topic.split(' - ')[0]}
                              </h3>
                              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                {topic.split(' - ')[1]}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Tech Stack Details */}
                  <div className={`rounded-lg p-4 border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border'}`}>
                    <h2 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Recommended Technology Stack</h2>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                      {currentSubcategory.techStack.map((tech, index) => (
                        <div key={index} className={`px-4 py-2 rounded border text-center transition-colors ${
                          isDarkMode ? 'bg-gray-700 border-gray-600 hover:bg-gray-650' : 'bg-white border hover:bg-gray-50'
                        }`}>
                          <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>{tech}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Alternative content when other panels are active */}
            {activeLeftPanel !== 'home' && (
              <div className="p-6">
                <div className="max-w-4xl">
                  <div className={`text-center py-16 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    <div className="mb-4">
                      {activeLeftPanel === 'inbox' && <Inbox className="w-12 h-12 mx-auto mb-4" />}
                      {activeLeftPanel === 'chat' && <Brain className="w-12 h-12 mx-auto mb-4" />}
                      {activeLeftPanel === 'calendar' && <Calendar className="w-12 h-12 mx-auto mb-4" />}
                      {activeLeftPanel === 'timer' && <Timer className="w-12 h-12 mx-auto mb-4" />}
                      {activeLeftPanel === 'goals' && <Goal className="w-12 h-12 mx-auto mb-4" />}
                      {activeLeftPanel === 'tasks' && <ListTodo className="w-12 h-12 mx-auto mb-4" />}
                      {activeLeftPanel === 'teamchat' && <MessageCircle className="w-12 h-12 mx-auto mb-4" />}
                    </div>
                    <h2 className={`text-xl font-semibold mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                      {activeLeftPanel === 'inbox' && 'Inbox Management'}
                      {activeLeftPanel === 'chat' && 'AI Brain Assistant'}
                      {activeLeftPanel === 'calendar' && 'Calendar View'}
                      {activeLeftPanel === 'timer' && 'Time Tracking'}
                      {activeLeftPanel === 'goals' && 'Goal Management'}
                      {activeLeftPanel === 'tasks' && 'Task Management'}
                    </h2>
                    <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Manage your {activeLeftPanel} from the sidebar. Click "Home" to return to hackathon stages.</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HackathonDashboard;
